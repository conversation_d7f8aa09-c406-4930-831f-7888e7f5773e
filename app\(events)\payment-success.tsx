import React, {useMemo, useCallback} from "react";
import {Text, View, ScrollView, TouchableOpacity} from "react-native";
import ScreenWithHeader from "../../components/screen-with-header";
import {router, useLocalSearchParams} from "expo-router";
import CheckCircleIcon from "../../components/icons/check-circle-icon";
import ShoppingBagIcon from "../../components/icons/shopping-bag-icon";
import Ticket02Icon from "../../components/icons/ticket-02-icon";
import ChevronLeftIcon from "../../components/icons/chevron-left-icon";
import ChevronRightIcon from "../../components/icons/chevron-right-icon";

// Imports para tipos de pagamento
import {PaymentType, PaymentEntity} from "../../models/api/payments.models";
import {useUserTickets} from "../../hooks/api/use-tickets";

import styles from "@/styles/events/payment-success.style";

const PaymentSuccess: React.FC = () => {
  const params = useLocalSearchParams();

  // Extrair dados dos parâmetros
  const paymentData = useMemo(() => {
    try {
      if (params.paymentData && typeof params.paymentData === "string") {
        return JSON.parse(params.paymentData);
      }
    } catch (error) {
      console.error("❌ [PAYMENT-SUCCESS] Erro ao parsear paymentData:", error);
    }
    return null;
  }, [params.paymentData]);

  const entityTitle = (params.entityTitle as string) || "Produto";
  const entityType = params.entityType
    ? parseInt(params.entityType as string)
    : PaymentEntity.Event;
  const totalValue = params.totalValue
    ? parseFloat(params.totalValue as string)
    : 0;
  const paymentType = params.paymentType
    ? parseInt(params.paymentType as string)
    : PaymentType.Pix;

  // Extrair entityId do paymentData para buscar tickets relacionados
  const entityId = useMemo(() => {
    if (paymentData?.entityId) {
      return paymentData.entityId;
    }
    // Tentar extrair de outras possíveis fontes
    if (paymentData?.event?.id) {
      return paymentData.event.id;
    }
    if (paymentData?.eventId) {
      return paymentData.eventId;
    }
    if (typeof paymentData?.entity === "object" && paymentData.entity.id) {
      return paymentData.entity.id;
    }
    return null;
  }, [paymentData]);

  // Buscar tickets do usuário para encontrar o ticket do evento
  const {
    data: ticketsData,
    isLoading: isLoadingTickets,
    refetch: refetchTickets
  } = useUserTickets(
    {
      pageSize: 100 // Buscar mais tickets para garantir que encontremos o mais recente
    },
    {
      enabled: entityType === PaymentEntity.Event && !!entityId,
      // Refetch mais frequentemente para pegar tickets recém-criados
      refetchInterval: 3000, // Refetch a cada 3 segundos
      refetchIntervalInBackground: false
    } as any
  );

  // Encontrar o ticket específico para o evento
  const eventTicket = useMemo(() => {
    if (!ticketsData?.data || !entityId) return null;

    console.log("🔍 [PAYMENT-SUCCESS] Buscando ticket para evento:", entityId);
    console.log(
      "🔍 [PAYMENT-SUCCESS] Total de tickets encontrados:",
      ticketsData.data.length
    );

    // Filtrar tickets do evento específico
    const eventTickets = ticketsData.data.filter(
      (ticket) => ticket.eventId === entityId
    );

    console.log(
      "🔍 [PAYMENT-SUCCESS] Tickets do evento específico:",
      eventTickets.length
    );

    if (eventTickets.length === 0) {
      console.warn(
        "🔍 [PAYMENT-SUCCESS] Nenhum ticket encontrado para o evento:",
        entityId
      );
      return null;
    }

    // Se há múltiplos tickets, pegar o primeiro (mais recente na API)
    const selectedTicket = eventTickets[0];
    console.log("✅ [PAYMENT-SUCCESS] Ticket selecionado:", selectedTicket.id);

    return selectedTicket;
  }, [ticketsData, entityId]);

  console.log("✅ [PAYMENT-SUCCESS] Dados recebidos:", {
    entityTitle,
    entityType,
    totalValue,
    paymentType,
    hasPaymentData: !!paymentData,
    entityId,
    hasEventTicket: !!eventTicket,
    ticketId: eventTicket?.id
  });

  // Função para obter informações do método de pagamento (reutilizada da tela de revisão)
  const getPaymentMethodInfo = useCallback(() => {
    switch (paymentType) {
      case PaymentType.Pix:
        return "PIX";
      case PaymentType.Boleto:
        return "Boleto Bancário";
      case PaymentType.CreditCard:
        if (paymentData?.creditCard) {
          // Tentar extrair informações do cartão dos dados de pagamento
          return "Cartão de Crédito";
        }
        return "Cartão de Crédito";
      default:
        return "PIX";
    }
  }, [paymentType, paymentData]);

  // Verificar se deve mostrar botão "Ver ingresso" (apenas para eventos)
  const shouldShowTicketButton = entityType === PaymentEntity.Event;

  // Verificar se deve mostrar campos específicos de eventos
  const shouldShowEventFields = entityType === PaymentEntity.Event;

  // Determinar quantidade de itens
  const itemCount = 1; // Por padrão 1 item, pode ser expandido no futuro

  // Função para obter informações do organizador/criador
  const getOrganizerInfo = useCallback(() => {
    // Tentar extrair do paymentData primeiro
    if (paymentData) {
      // Verificar se há informações do evento/produto no paymentData
      if (paymentData.event?.organizer) {
        return paymentData.event.organizer;
      }
      if (paymentData.product?.creator) {
        return paymentData.product.creator;
      }
      if (paymentData.opportunity?.organizer) {
        return paymentData.opportunity.organizer;
      }
      // Verificar outros campos possíveis
      if (paymentData.organizer) {
        return paymentData.organizer;
      }
      if (paymentData.creator) {
        return paymentData.creator;
      }
    }

    // Fallback baseado no tipo de entidade
    switch (entityType) {
      case PaymentEntity.Event:
        return "Organizador do Evento";
      case PaymentEntity.Product:
        return "Criador do Produto";
      case PaymentEntity.Opportunity:
        return "Organizador da Oportunidade";
      default:
        return "Organizador";
    }
  }, [paymentData, entityType]);

  // Função para obter texto do botão de ação principal
  const getPrimaryActionButtonText = useCallback(() => {
    switch (entityType) {
      case PaymentEntity.Event:
        return "Ver ingresso";
      case PaymentEntity.Product:
        return "Baixar produto";
      case PaymentEntity.Opportunity:
        return "Ver oportunidade";
      default:
        return "Ver ingresso";
    }
  }, [entityType]);

  // Função para obter texto do botão primário de navegação
  const getPrimaryNavigationButtonText = useCallback(() => {
    switch (entityType) {
      case PaymentEntity.Event:
        return "Ver eventos agendados";
      case PaymentEntity.Product:
        return "Ver produtos adquiridos";
      case PaymentEntity.Opportunity:
        return "Ver oportunidades";
      default:
        return "Ver eventos agendados";
    }
  }, [entityType]);

  // Função para obter texto do botão secundário de navegação
  const getSecondaryNavigationButtonText = useCallback(() => {
    switch (entityType) {
      case PaymentEntity.Event:
        return "Voltar aos eventos";
      case PaymentEntity.Product:
        return "Voltar aos produtos";
      case PaymentEntity.Opportunity:
        return "Voltar à central de negócios";
      default:
        return "Voltar aos eventos";
    }
  }, [entityType]);

  // Função para obter destino do botão primário de navegação
  const getNavigationDestination = useCallback(() => {
    switch (entityType) {
      case PaymentEntity.Event:
        return "/(tabs)/schedule";
      case PaymentEntity.Product:
        // Após comprar um produto, deve levar para a tela de "Meus produtos"
        return "/(settings)/my-products";
      case PaymentEntity.Opportunity:
        return "/(business)/business-center";
      default:
        return "/(tabs)/schedule";
    }
  }, [entityType]);

  // Destino do botão secundário de navegação
  const getSecondaryNavigationDestination = useCallback(() => {
    switch (entityType) {
      case PaymentEntity.Product:
        // Botão secundário deve voltar para o catálogo de produtos
        return "/(tabs)/products";
      case PaymentEntity.Event:
        return "/(tabs)/schedule";
      case PaymentEntity.Opportunity:
        return "/(business)/business-center";
      default:
        return "/(tabs)/schedule";
    }
  }, [entityType]);

  const handleViewEvents = () => {
    router.push(getNavigationDestination());
  };

  const handleSecondaryNavigation = () => {
    router.push(getSecondaryNavigationDestination());
  };

  // Função para navegar para o ticket
  const handleViewTicket = useCallback(async () => {
    if (eventTicket?.id) {
      console.log(
        "🎫 [PAYMENT-SUCCESS] Navegando para ticket específico:",
        eventTicket.id
      );
      router.push(`/(events)/ticket-details?id=${eventTicket.id}`);
    } else if (isLoadingTickets) {
      console.log("🎫 [PAYMENT-SUCCESS] Ainda carregando tickets, aguarde...");
      return; // Não fazer nada se ainda está carregando
    } else {
      console.error(
        "🎫 [PAYMENT-SUCCESS] Ticket não encontrado após pagamento!"
      );
      console.log("🎫 [PAYMENT-SUCCESS] Tentando recarregar tickets...");

      try {
        // Tentar recarregar os tickets
        await refetchTickets();

        // Aguardar um pouco para o estado atualizar
        setTimeout(() => {
          if (eventTicket?.id) {
            console.log(
              "✅ [PAYMENT-SUCCESS] Ticket encontrado após reload:",
              eventTicket.id
            );
            router.push(`/(events)/ticket-details?id=${eventTicket.id}`);
          } else {
            console.warn(
              "⚠️ [PAYMENT-SUCCESS] Ticket ainda não encontrado após reload"
            );
            alert(
              "Ingresso ainda não está disponível. Isso pode levar alguns segundos após o pagamento. Tente novamente em breve ou verifique em 'Meus Ingressos'."
            );
          }
        }, 1000);
      } catch (error) {
        console.error(
          "❌ [PAYMENT-SUCCESS] Erro ao recarregar tickets:",
          error
        );
        alert(
          "Erro ao buscar ingresso. Verifique sua conexão e tente novamente."
        );
      }
    }
  }, [eventTicket, router, isLoadingTickets, refetchTickets]);

  return (
    <ScreenWithHeader screenTitle="Pagamento efetuado com sucesso!" backButton>
      <ScrollView
        style={styles.scrollContainer}
        showsVerticalScrollIndicator={false}
      >
        {/* Success Icon and Message */}
        <View style={styles.successContainer}>
          <CheckCircleIcon width={76} height={76} style={styles.successIcon} />

          <Text style={styles.title}>Pagamento efetuado com sucesso!</Text>

          <Text style={styles.subtitle}>
            Enviaremos um e-mail para você com as informações do evento.
          </Text>
        </View>

        {/* Products Header */}
        <View style={styles.productsHeader}>
          <Text style={styles.productsTitle}>Produtos adquiridos</Text>

          {itemCount > 1 && (
            <View style={styles.navigationContainer}>
              <TouchableOpacity
                style={[styles.navButton, styles.navButtonDisabled]}
              >
                <ChevronLeftIcon
                  width={20}
                  height={20}
                  style={styles.navIcon}
                  stroke="#475466"
                />
              </TouchableOpacity>

              <Text style={[styles.paginationText, styles.paginationCurrent]}>
                1
              </Text>
              <Text style={[styles.paginationText, styles.paginationSeparator]}>
                /
              </Text>
              <Text style={[styles.paginationText, styles.paginationTotal]}>
                {itemCount}
              </Text>

              <TouchableOpacity style={styles.navButton}>
                <ChevronRightIcon
                  width={20}
                  height={20}
                  style={styles.navIcon}
                  stroke="#DFE9F0"
                />
              </TouchableOpacity>
            </View>
          )}
        </View>

        {/* Product Card */}
        <View style={styles.productCard}>
          <View style={styles.productIconContainer}>
            <ShoppingBagIcon
              width={24}
              height={24}
              style={styles.productIcon}
              replaceColor="#EAECF0"
            />
          </View>

          <Text style={styles.productName}>{entityTitle}</Text>

          <View style={styles.productInfoRow}>
            <Text style={styles.productInfoLabel}>Sediado por</Text>
            <Text style={styles.productInfoValue}>{getOrganizerInfo()}</Text>
          </View>

          {shouldShowEventFields && (
            <View style={styles.productInfoRow}>
              <Text style={styles.productInfoLabel}>Local do evento</Text>
              <Text
                style={[
                  styles.productInfoValue,
                  styles.productInfoValueMultiline
                ]}
              >
                Expocentro Balneário Camboriú - SC
              </Text>
            </View>
          )}

          <View style={styles.productInfoRow}>
            <Text style={styles.productInfoLabel}>Data / hora do evento</Text>
            <Text style={styles.productInfoValue}>08/06/2025 - 15:45 PM</Text>
          </View>

          {shouldShowEventFields && (
            <View style={styles.productInfoRow}>
              <Text style={styles.productInfoLabel}>Nº ingresso</Text>
              <Text style={styles.productInfoValue}>
                {eventTicket?.code || eventTicket?.id || "556516658419614"}
              </Text>
            </View>
          )}

          {shouldShowTicketButton && (
            <View style={styles.productInfoRow}>
              <Text style={styles.productInfoLabel}>Qtde. ingressos</Text>
              <Text style={styles.productInfoValue}>
                {itemCount} ({itemCount === 1 ? "um" : itemCount}) ingresso
                {itemCount !== 1 ? "s" : ""}
              </Text>
            </View>
          )}

          <View style={styles.priceContainer}>
            <Text style={styles.priceLabel}>Valor do item</Text>
            <View style={styles.priceValuesContainer}>
              <Text style={styles.discountedPrice}>
                R$ {totalValue.toFixed(2).replace(".", ",")}
              </Text>
            </View>
          </View>

          {shouldShowTicketButton && (
            <TouchableOpacity
              style={styles.actionButton}
              onPress={handleViewTicket}
            >
              <Ticket02Icon
                width={20}
                height={20}
                style={styles.actionButtonIcon}
              />
              <Text style={styles.actionButtonText}>
                {getPrimaryActionButtonText()}
              </Text>
            </TouchableOpacity>
          )}
        </View>

        {/* Payment Information */}
        <Text style={styles.paymentInfoTitle}>Informações de pagamento</Text>

        <View style={styles.paymentInfoRow}>
          <Text style={styles.paymentInfoLabel}>Método de pagamento</Text>
          <Text style={styles.paymentInfoValue}>{getPaymentMethodInfo()}</Text>
        </View>

        <View style={styles.paymentInfoRow}>
          <Text style={styles.paymentInfoLabel}>
            Subtotal ({itemCount} {itemCount === 1 ? "item" : "itens"})
          </Text>
          <Text style={styles.paymentInfoValue}>
            R$ {totalValue.toFixed(2).replace(".", ",")}
          </Text>
        </View>

        <View style={styles.paymentTotalRow}>
          <Text style={styles.paymentTotalLabel}>Soma total</Text>
          <Text style={styles.paymentTotalValue}>
            R$ {totalValue.toFixed(2).replace(".", ",")}
          </Text>
        </View>

        {/* Buttons */}
        <View style={styles.buttonsContainer}>
          <TouchableOpacity
            style={styles.primaryButton}
            onPress={handleViewEvents}
          >
            <Text style={styles.primaryButtonText}>
              {getPrimaryNavigationButtonText()}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.secondaryButton}
            onPress={handleSecondaryNavigation}
          >
            <Text style={styles.secondaryButtonText}>
              {getSecondaryNavigationButtonText()}
            </Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </ScreenWithHeader>
  );
};

export default PaymentSuccess;
