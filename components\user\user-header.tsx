import {ImageBackground} from "expo-image";
import React from "react";
import {Text, View, ActivityIndicator} from "react-native";
import styles from "../../styles/components/user/user-header.style";

import Button from "../button";
import EditIcon from "../icons/edit-icon";
import GearIcon from "../icons/gear-icon";
import MarkerPinIcon from "../icons/marker-pin-icon";
import Avatar from "./avatar";

import {useCurrentUser} from "../../hooks/api/use-users";
import {useAuth} from "../../contexts/AuthContext";
import {useRouter} from "expo-router";
import stylesConstants from "../../styles/styles-constants";
import {useGuestUser} from "../../contexts/guest-user-context";

const UserHeader: React.FC = () => {
  const router = useRouter();
  const {user: authUser, isLoading: authLoading} = useAuth();
  const {data: apiUser, isLoading: apiLoading} = useCurrentUser();
  const {isGuest} = useGuestUser();

  // Usar dados do AuthContext como prioridade, API como fallback
  const user = authUser || apiUser;
  const isLoading = authLoading || apiLoading;

  // Função para extrair cidade do endereço completo
  const getLocationDisplay = (user: any) => {
    if (user?.address?.city && user?.address?.state) {
      return `${user.address.city} - ${user.address.state}`;
    }
    if (user?.address?.city) {
      return user.address.city;
    }
    return "Localização não informada";
  };

  return (
    <View style={styles.container}>
      <ImageBackground
        imageStyle={styles.backgroundImageStyle}
        style={styles.backgroundImage}
        source={require("../../assets/textures/rotated-pattern.png")}
      >
        <View style={styles.overlay} />
        <View style={styles.content}>
          <View style={styles.innerContainer}>
            {isLoading ? (
              <ActivityIndicator
                size="large"
                color={stylesConstants.colors.textPrimary}
              />
            ) : (
              <>
                <Avatar user={user} name={user?.name} />
                <View style={styles.userInfo}>
                  <Text style={styles.userName}>{user?.name || "Usuário"}</Text>
                  <View style={styles.locationContainer}>
                    <MarkerPinIcon />
                    <Text style={styles.locationText}>
                      {getLocationDisplay(user)}
                    </Text>
                  </View>
                </View>
              </>
            )}
            {/* Only show buttons for registered users, not for guest users */}
            {!isGuest && (
              <View style={styles.buttonsContainer}>
                <Button
                  style={styles.primaryButton}
                  text="Editar dados"
                  icon={<EditIcon width={20} height={20} />}
                  backgroundColor="#0F7C4D"
                  borderColor="#0F7C4D"
                  onPress={() => {
                    router.push("/(profile)/edit-profile");
                  }}
                />
                <Button
                  style={styles.secondaryButton}
                  text="Ajustes gerais"
                  icon={<GearIcon width={20} height={20} />}
                  backgroundColor="transparent"
                  borderColor="#FCFCFD"
                  onPress={() => {
                    router.push("/(settings)/general-settings");
                  }}
                />
              </View>
            )}
          </View>
        </View>
      </ImageBackground>
    </View>
  );
};
export default UserHeader;
