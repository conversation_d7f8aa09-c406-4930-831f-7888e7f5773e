/**
 * Contexto de autenticação para gerenciar estado global
 */

import React, {
  createContext,
  useContext,
  useEffect,
  useState,
  ReactNode,
  useMemo
} from "react";
import {useRouter, useSegments} from "expo-router";
import {useQueryClient} from "@tanstack/react-query";
import {tokenManager} from "@/services/api/auth/token-manager";
import {apiClient} from "@/services/api/base/api-client";
import {UserInfo} from "@/models/api/auth.models";
import {ApiLogger} from "@/services/api/base/api-logger";
import {usersKeys} from "@/hooks/api/use-users";
import * as SecureStore from "expo-secure-store";
import {useGuestUser} from "./guest-user-context";
import * as LocalAuthentication from "expo-local-authentication";
import useNotification from "@/hooks/use-notification";

// Chave para armazenar dados de sessão para biometria
const BIOMETRIC_SESSION_KEY = "session_data";

interface AuthContextType {
  user: UserInfo | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  requiresBiometricAuth: boolean;
  login: (email: string, password: string) => Promise<void>;
  logout: () => Promise<void>;
  refreshUser: () => Promise<void>;
  reloadSession: () => Promise<void>;
  authenticateWithBiometrics: () => Promise<boolean>;
  skipBiometricAuth: () => void;
  setOnAuthSuccessCallback: (callback: (() => Promise<void>) | null) => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export function AuthProvider({children}: AuthProviderProps) {
  const [user, setUser] = useState<UserInfo | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [requiresBiometricAuth, setRequiresBiometricAuth] = useState(false);
  const [pendingTokenData, setPendingTokenData] = useState<any>(null);
  const [onAuthSuccessCallback, setOnAuthSuccessCallback] = useState<
    (() => Promise<void>) | null
  >(null);
  const router = useRouter();
  const segments = useSegments();
  const queryClient = useQueryClient();
  const {clearGuestUser, isGuest} = useGuestUser();
  const {pushToken} = useNotification();

  const isAuthenticated = !!user || (isGuest && !requiresBiometricAuth);

  // Log para debug do estado de autenticação
  useEffect(() => {
    ApiLogger.info("🔍 AuthContext Estado:", {
      hasUser: !!user,
      isGuest,
      requiresBiometricAuth,
      isAuthenticated
    });
  }, [user, isGuest, requiresBiometricAuth, isAuthenticated]);

  // Verificar se está em rota protegida
  const inAuthGroup = segments[0] === "(auth)";
  const inRegistrationGroup = segments[0] === "(registration)";

  useEffect(() => {
    // Configurar callback para token expirado
    apiClient.setOnTokenExpired(() => {
      ApiLogger.warn("Token expirado, redirecionando para login");
      handleLogout();
    });

    // Configurar callback para acesso negado (403)
    apiClient.setOnForbiddenAccess(() => {
      ApiLogger.warn(
        "Acesso negado - usuário não tem permissão para este recurso"
      );
      // Para 403, não fazemos logout automático, apenas logamos o erro
      // O usuário pode continuar usando outras partes do app
    });

    // Verificar sessão existente apenas se não há token já configurado
    const currentToken = tokenManager.getTokenData();
    console.log("🔍 AuthContext init - Token atual:", {
      hasToken: !!currentToken,
      hasAccessToken: !!currentToken?.accessToken
    });

    if (!currentToken) {
      console.log(
        "🔄 AuthContext init - Nenhum token encontrado, verificando sessão existente"
      );
      checkExistingSession();
    } else {
      console.log(
        "✅ AuthContext init - Token já existe, pulando checkExistingSession"
      );
      // Se já há um token, verificar se é de usuário upsell
      const checkUpsellUser = async () => {
        try {
          const AsyncStorage = await import(
            "@react-native-async-storage/async-storage"
          );
          const guestDataString = await AsyncStorage.default.getItem(
            "@club_m_guest_user_data"
          );

          if (guestDataString) {
            console.log(
              "🎯 AuthContext init - Usuário upsell detectado, fazendo login automático"
            );
            await performAutomaticLogin(currentToken);
          } else {
            console.log(
              "🔐 AuthContext init - Usuário regular detectado, exigindo biometria"
            );
            await interceptAutomaticLogin(currentToken);
          }
        } catch (error) {
          console.log(
            "❌ AuthContext init - Erro ao verificar tipo de usuário:",
            error
          );
          await interceptAutomaticLogin(currentToken);
        }
      };

      checkUpsellUser();
    }
  }, []);

  useEffect(() => {
    // Gerenciar navegação baseada no estado de autenticação
    // Mas não interferir na tela de introdução (index) ou quando biometria é obrigatória

    ApiLogger.info("🧭 AuthContext Navigation Effect:", {
      isLoading,
      requiresBiometricAuth,
      isAuthenticated,
      segments: segments.join("/"),
      inAuthGroup,
      inRegistrationGroup
    });

    if (!isLoading && !requiresBiometricAuth) {
      const isOnIntroScreen = !segments.length || segments[0] === "index";

      if (
        isAuthenticated &&
        (inAuthGroup || inRegistrationGroup || isOnIntroScreen)
      ) {
        // Usuário autenticado em tela de auth, registration ou introdução -> redirecionar para home
        ApiLogger.info("🏠 Redirecionando usuário autenticado para home");
        router.replace("/(tabs)/home");
      } else if (
        !isAuthenticated &&
        !inAuthGroup &&
        !inRegistrationGroup &&
        !isOnIntroScreen
      ) {
        // Usuário não autenticado em tela protegida -> redirecionar para login
        // Mas não se estiver na tela de introdução ou registration
        ApiLogger.info("🔑 Redirecionando usuário não autenticado para login");
        router.replace("/(auth)/login");
      }
    } else if (requiresBiometricAuth) {
      ApiLogger.info(
        "🔐 Biometria obrigatória ativa - não fazendo redirecionamento automático"
      );
    }
  }, [
    isAuthenticated,
    inAuthGroup,
    inRegistrationGroup,
    isLoading,
    segments,
    requiresBiometricAuth
  ]);

  const checkExistingSession = async () => {
    try {
      setIsLoading(true);
      ApiLogger.info("🔍 Verificando sessão existente...");

      // Carregar token do storage
      const tokenData = await tokenManager.loadTokenFromStorage();
      console.log("🔍 checkExistingSession - Token carregado do storage:", {
        hasTokenData: !!tokenData,
        hasAccessToken: !!tokenData?.accessToken,
        tokenType: tokenData?.tokenType,
        expiresAt: tokenData?.expiresAt
      });

      if (!tokenData) {
        ApiLogger.info("❌ Nenhum token encontrado - usuário não autenticado");
        setIsLoading(false);
        return;
      }

      ApiLogger.info("✅ Token encontrado - verificando validade...");
      console.log(
        "✅ checkExistingSession - Token encontrado, verificando validade"
      );

      // Verificar se token está válido
      if (tokenManager.isTokenExpired()) {
        ApiLogger.info("⏰ Token expirado - tentando renovar...");
        // Tentar renovar token
        if (tokenData.refreshToken) {
          try {
            await tokenManager.refreshToken();
            const updatedTokenData = tokenManager.getTokenData();
            if (updatedTokenData) {
              // Token renovado com sucesso - exigir biometria para login automático
              ApiLogger.info(
                "✅ Token renovado - interceptando para biometria..."
              );
              await interceptAutomaticLogin(updatedTokenData);
            } else {
              ApiLogger.info("❌ Falha ao renovar token - fazendo logout");
              await handleLogout();
            }
          } catch (error) {
            ApiLogger.error("Erro ao renovar token", error as Error);
            await handleLogout();
          }
        } else {
          ApiLogger.info("❌ Sem refresh token - fazendo logout");
          await handleLogout();
        }
      } else {
        // Token válido - verificar se é usuário upsell
        try {
          const AsyncStorage = await import(
            "@react-native-async-storage/async-storage"
          );
          const guestDataString = await AsyncStorage.default.getItem(
            "@club_m_guest_user_data"
          );

          if (guestDataString) {
            // Usuário upsell - fazer login automático sem biometria
            ApiLogger.info(
              "✅ Token válido para usuário upsell - fazendo login automático"
            );
            await performAutomaticLogin(tokenData);
          } else {
            // Usuário regular - exigir biometria para login automático
            ApiLogger.info(
              "✅ Token válido - interceptando para biometria obrigatória..."
            );
            await interceptAutomaticLogin(tokenData);
          }
        } catch (error) {
          // Em caso de erro, usar fluxo padrão com biometria
          ApiLogger.warn(
            "Erro ao verificar dados de guest user, usando fluxo padrão"
          );
          await interceptAutomaticLogin(tokenData);
        }
      }
    } catch (error) {
      ApiLogger.error("Erro ao verificar sessão existente", error as Error);
      await handleLogout();
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Realizar login automático para usuários upsell
   */
  const performAutomaticLogin = async (tokenData: any) => {
    try {
      ApiLogger.info("🚀 Realizando login automático para usuário upsell");
      console.log("🚀 performAutomaticLogin - Iniciando login automático:", {
        hasTokenData: !!tokenData,
        hasAccessToken: !!tokenData?.accessToken,
        tokenType: tokenData?.tokenType
      });

      // Configurar token no API client
      apiClient.setAuthToken(tokenData);
      console.log("✅ performAutomaticLogin - Token configurado no apiClient");

      // Carregar informações do usuário
      console.log("🔄 performAutomaticLogin - Chamando loadUserInfo...");
      await loadUserInfo();
      console.log(
        "✅ performAutomaticLogin - loadUserInfo concluído com sucesso"
      );

      ApiLogger.info("✅ Login automático realizado com sucesso");
    } catch (error) {
      console.log("❌ performAutomaticLogin - Erro:", error);
      ApiLogger.error("Erro no login automático", error as Error);
      await handleLogout();
    }
  };

  /**
   * Interceptar login automático e exigir biometria
   */
  const interceptAutomaticLogin = async (tokenData: any) => {
    try {
      // Se há um token válido, isso significa que o usuário já fez login antes
      // SEMPRE exigir biometria para usuários que retornam, independentemente de dados salvos
      ApiLogger.info(
        "🔐 Token válido encontrado - usuário retornando detectado - exigindo autenticação biométrica obrigatória"
      );

      // SEMPRE mostrar tela de biometria para usuários que retornam
      // Não importa se o dispositivo suporta biometria ou não - o usuário pode escolher "Usar senha"
      ApiLogger.info(
        "🚫 Interceptando login automático - mostrando tela de biometria obrigatória"
      );

      // Configurar token no API client temporariamente para verificar validade
      apiClient.setAuthToken(tokenData);

      // Armazenar dados do token para uso após autenticação biométrica ou senha
      setPendingTokenData(tokenData);

      // Ativar flag para mostrar prompt de biometria
      setRequiresBiometricAuth(true);
      ApiLogger.info(
        "✅ requiresBiometricAuth definido como TRUE - usuário verá tela de biometria"
      );

      return;
    } catch (error) {
      ApiLogger.error("Erro ao interceptar login automático", error as Error);
      // Em caso de erro, redirecionar para login por segurança
      await handleLogout();
    }
  };

  /**
   * Prosseguir com login automático normal
   */
  const proceedWithAutomaticLogin = async (tokenData: any) => {
    try {
      ApiLogger.info(
        "🚀 Prosseguindo com login automático após biometria bem-sucedida"
      );

      // Configurar token no API client
      apiClient.setAuthToken(tokenData);

      // Carregar informações do usuário
      await loadUserInfo();

      // Executar callback de sucesso de autenticação (para deep links)
      if (onAuthSuccessCallback) {
        try {
          await onAuthSuccessCallback();
          setOnAuthSuccessCallback(null); // Limpar callback após execução
        } catch (error) {
          ApiLogger.error(
            "Erro ao executar callback de sucesso de autenticação",
            error as Error
          );
        }
      }

      ApiLogger.info(
        "✅ Login automático realizado com sucesso após biometria"
      );
    } catch (error) {
      ApiLogger.error("Erro no login automático", error as Error);
      await handleLogout();
    }
  };

  const loadUserInfo = async () => {
    try {
      // Fazer requisição real para obter dados do usuário
      const tokenData = tokenManager.getTokenData();
      console.log("🔍 loadUserInfo - Verificando token:", {
        hasTokenData: !!tokenData,
        hasAccessToken: !!tokenData?.accessToken,
        tokenType: tokenData?.tokenType
      });

      if (tokenData) {
        try {
          // Verificar se o token está configurado no apiClient
          const apiClientToken = (apiClient as any).tokenData;
          console.log("🔍 loadUserInfo - Token no apiClient:", {
            hasApiClientToken: !!apiClientToken,
            hasAccessToken: !!apiClientToken?.accessToken,
            tokenType: apiClientToken?.tokenType
          });

          // Importar UsersService dinamicamente para evitar dependência circular
          const {default: UsersService} = await import(
            "@/services/api/users/users.service"
          );

          console.log(
            "🚀 loadUserInfo - Fazendo requisição para /api/users/@me"
          );
          // Buscar dados reais do usuário
          const userInfo = await UsersService.getCurrentUser();
          console.log("✅ loadUserInfo - Sucesso na requisição:", {
            userId: userInfo.id,
            name: userInfo.name
          });

          ApiLogger.info("👤 Definindo usuário após carregamento:", {
            userId: userInfo.id
          });
          setUser(userInfo);

          // Invalidar cache do React Query para garantir dados atualizados
          queryClient.invalidateQueries({queryKey: usersKeys.current()});

          ApiLogger.info("Dados do usuário carregados com sucesso", {
            userId: userInfo.id,
            name: userInfo.name
          });
        } catch (apiError: any) {
          console.log("❌ loadUserInfo - Erro na requisição:", {
            status: apiError?.status ?? apiError?.response?.status,
            message: apiError?.message,
            error: apiError
          });

          const status = apiError?.status ?? apiError?.response?.status;

          // Tratamento especial para 401: pode ser usuário upsell (guest)
          if (status === 401) {
            try {
              const AsyncStorage = await import(
                "@react-native-async-storage/async-storage"
              );
              const guestDataString = await AsyncStorage.default.getItem(
                "@club_m_guest_user_data"
              );

              if (guestDataString) {
                const guestData = JSON.parse(guestDataString);
                ApiLogger.info(
                  "👤 401 recebido, mas usuário upsell detectado - mantendo token e usando dados de guest user"
                );
                const guestUser: UserInfo = {
                  id: "guest",
                  name: guestData.name,
                  email: "<EMAIL>",
                  document: guestData.document,
                  phone: guestData.phone,
                  avatar: "",
                  isActive: true,
                  roles: ["guest"],
                  permissions: ["read"]
                };
                setUser(guestUser);
                return; // NÃO fazer logout para upsell
              }

              // Sem dados de guest: token inválido mesmo -> logout
              ApiLogger.warn(
                "Token inválido (401) sem dados de guest - fazendo logout"
              );
              await handleLogout();
              return;
            } catch (guestCheckError) {
              ApiLogger.warn(
                "Erro ao verificar dados de guest user após 401",
                guestCheckError as Error
              );
              ApiLogger.warn("Fazendo logout por segurança");
              await handleLogout();
              return;
            }
          }

          // Para outros erros (não 401), tentar fallback de guest se existir
          try {
            const AsyncStorage = await import(
              "@react-native-async-storage/async-storage"
            );
            const guestDataString = await AsyncStorage.default.getItem(
              "@club_m_guest_user_data"
            );

            if (guestDataString) {
              const guestData = JSON.parse(guestDataString);
              ApiLogger.info(
                "👤 Usuário upsell detectado após erro da API - usando dados de guest user"
              );
              const guestUser: UserInfo = {
                id: "guest",
                name: guestData.name,
                email: "<EMAIL>",
                document: guestData.document,
                phone: guestData.phone,
                avatar: "",
                isActive: true,
                roles: ["guest"],
                permissions: ["read"]
              };
              setUser(guestUser);
              return;
            }
          } catch (guestError) {
            ApiLogger.warn(
              "Erro ao carregar dados de guest user",
              guestError as Error
            );
          }

          // Se não há dados de guest user, usar dados mock como fallback (sem limpar token aqui)
          ApiLogger.warn(
            "API falhou e não há dados de guest user, usando dados mock como fallback",
            apiError
          );

          const mockUser: UserInfo = {
            id: "1",
            name: "Mozart",
            email: "<EMAIL>",
            document: "12345678901",
            phone: "+55 11 99999-9999",
            avatar:
              "https://upload.wikimedia.org/wikipedia/commons/thumb/1/1e/Wolfgang-amadeus-mozart_1.jpg/1200px-Wolfgang-amadeus-mozart_1.jpg",
            isActive: true,
            roles: ["user"],
            permissions: ["read"]
          };
          ApiLogger.info("👤 Definindo usuário MOCK após falha da API");
          setUser(mockUser);
        }
      }
    } catch (error) {
      ApiLogger.error(
        "Erro ao carregar informações do usuário",
        error as Error
      );
      throw error;
    }
  };

  /**
   * Salvar dados de sessão para login biométrico
   */
  const saveBiometricSessionData = async () => {
    try {
      // Verificar se o dispositivo suporta biometria
      const hasHardware = await LocalAuthentication.hasHardwareAsync();
      const isEnrolled = await LocalAuthentication.isEnrolledAsync();

      if (hasHardware && isEnrolled && user) {
        const tokenData = tokenManager.getTokenData();
        if (tokenData) {
          const sessionData = {
            token: tokenData.accessToken,
            refreshToken: tokenData.refreshToken,
            user: user,
            expiresAt:
              tokenData.expiresAt ||
              new Date(Date.now() + tokenData.expiresIn * 1000),
            issuedAt: new Date()
          };

          await SecureStore.setItemAsync(
            BIOMETRIC_SESSION_KEY,
            JSON.stringify(sessionData)
          );

          ApiLogger.info("Dados de sessão salvos para login biométrico");
        }
      }
    } catch (error) {
      ApiLogger.warn("Erro ao salvar dados para biometria", error as Error);
      // Não falhar o login se não conseguir salvar dados para biometria
    }
  };

  const handleLogin = async (emailOrDocument: string, password: string) => {
    try {
      setIsLoading(true);

      // Importar AuthService dinamicamente para evitar dependência circular
      const {AuthService} = await import("@/services/api/auth/auth.service");

      // Verificar se é email ou documento (CPF)
      const isEmail = emailOrDocument.includes("@");

      // Log do token para debug
      ApiLogger.info("AuthContext - Token disponível para login", {
        hasPushToken: !!pushToken,
        pushTokenLength: pushToken?.length,
        pushTokenPreview: pushToken ? `${pushToken.substring(0, 20)}...` : null
      });

      const response = isEmail
        ? await AuthService.login({
            email: emailOrDocument,
            password,
            firebaseToken: pushToken ?? undefined
          })
        : await AuthService.appLogin({
            document: emailOrDocument,
            password,
            firebaseToken: pushToken ?? undefined
          });

      if (response.accessToken) {
        await loadUserInfo();

        // Salvar dados para login biométrico após carregar informações do usuário
        await saveBiometricSessionData();

        // Executar callback de sucesso de autenticação (para deep links)
        if (onAuthSuccessCallback) {
          try {
            await onAuthSuccessCallback();
            setOnAuthSuccessCallback(null); // Limpar callback após execução
          } catch (error) {
            ApiLogger.error(
              "Erro ao executar callback de sucesso de autenticação",
              error as Error
            );
          }
        }

        // Invalidar cache para garantir dados atualizados após login
        queryClient.invalidateQueries({queryKey: usersKeys.current()});
        ApiLogger.info("Login realizado com sucesso");
      } else {
        throw new Error("Token de acesso não recebido");
      }
    } catch (error) {
      ApiLogger.error("Erro no login", error as Error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const handleLogout = async () => {
    try {
      ApiLogger.info("🚪 Iniciando logout...");
      setIsLoading(true);

      // Importar AuthService dinamicamente
      const {default: AuthService} = await import(
        "@/services/api/auth/auth.service"
      );

      try {
        await AuthService.logout();
      } catch (error) {
        // Continuar com logout local mesmo se falhar no servidor
        ApiLogger.warn(
          "Erro no logout do servidor, continuando com logout local",
          error
        );
      }

      // Limpar estado local
      ApiLogger.info("🧹 Limpando estado local - setUser(null)");
      setUser(null);
      apiClient.clearAuthToken();

      // Limpar dados de sessão biométrica
      try {
        await SecureStore.deleteItemAsync(BIOMETRIC_SESSION_KEY);
        ApiLogger.info("Dados de sessão biométrica removidos");
      } catch (error) {
        ApiLogger.warn(
          "Erro ao remover dados de sessão biométrica",
          error as Error
        );
      }

      // Limpar cache do React Query
      queryClient.clear();

      // Limpar dados de guest user
      await clearGuestUser();

      ApiLogger.info("Logout realizado com sucesso");
    } catch (error) {
      ApiLogger.error("Erro no logout", error as Error);
      // Mesmo com erro, limpar estado local
      setUser(null);
      apiClient.clearAuthToken();

      // Tentar limpar dados de sessão biométrica mesmo em caso de erro
      try {
        await SecureStore.deleteItemAsync(BIOMETRIC_SESSION_KEY);
      } catch (biometricError) {
        ApiLogger.warn(
          "Erro ao remover dados de sessão biométrica no fallback",
          biometricError as Error
        );
      }

      // Limpar cache do React Query mesmo em caso de erro
      queryClient.clear();
    } finally {
      setIsLoading(false);
    }
  };

  const refreshUser = async () => {
    try {
      await loadUserInfo();
      // Invalidar cache para garantir dados atualizados
      queryClient.invalidateQueries({queryKey: usersKeys.current()});
    } catch (error) {
      ApiLogger.error("Erro ao atualizar dados do usuário", error as Error);
      throw error;
    }
  };

  /**
   * Autenticar com biometria para login automático
   */
  const authenticateWithBiometrics = async (): Promise<boolean> => {
    try {
      if (!pendingTokenData) {
        ApiLogger.error("Nenhum token pendente para autenticação biométrica");
        return false;
      }

      // Verificar se dispositivo ainda suporta biometria
      const hasHardware = await LocalAuthentication.hasHardwareAsync();
      const isEnrolled = await LocalAuthentication.isEnrolledAsync();

      if (!hasHardware || !isEnrolled) {
        ApiLogger.warn("Biometria não disponível durante autenticação");
        return false;
      }

      // Configurar opções de autenticação biométrica
      const biometricConfig = {
        promptMessage: "Confirme que é você",
        fallbackLabel: "Usar senha",
        disableDeviceFallback: false
      };

      // Realizar autenticação biométrica
      const authResult = await LocalAuthentication.authenticateAsync(
        biometricConfig
      );

      if (authResult.success) {
        // Autenticação bem-sucedida - prosseguir com login
        ApiLogger.info("Autenticação biométrica bem-sucedida");

        // Limpar estados de biometria
        setRequiresBiometricAuth(false);
        setPendingTokenData(null);

        // Prosseguir com login automático
        await proceedWithAutomaticLogin(pendingTokenData);

        return true;
      } else {
        ApiLogger.warn("Autenticação biométrica falhou", authResult.error);
        return false;
      }
    } catch (error) {
      ApiLogger.error("Erro na autenticação biométrica", error as Error);
      return false;
    }
  };

  /**
   * Pular autenticação biométrica e ir para login
   */
  const skipBiometricAuth = async () => {
    ApiLogger.info("Usuário optou por pular autenticação biométrica");

    try {
      let userDocument = "";

      // Primeiro, tentar obter CPF dos dados de sessão biométrica salvos
      try {
        const savedSessionData = await SecureStore.getItemAsync(
          BIOMETRIC_SESSION_KEY
        );
        if (savedSessionData) {
          const sessionData = JSON.parse(savedSessionData);
          if (sessionData.user && sessionData.user.document) {
            userDocument = sessionData.user.document;
            ApiLogger.info(
              "📄 CPF obtido dos dados de sessão biométrica:",
              userDocument
            );
          }
        }
      } catch (error) {
        ApiLogger.warn(
          "Erro ao obter CPF dos dados de sessão biométrica:",
          error
        );
      }

      // Se não conseguiu obter o CPF dos dados biométricos, tentar obter do usuário atual
      if (!userDocument && user && user.document) {
        userDocument = user.document;
        ApiLogger.info("📄 CPF obtido do usuário atual:", userDocument);
      }

      // Log do resultado da busca do CPF
      if (userDocument) {
        ApiLogger.info(
          "✅ CPF encontrado para pré-preenchimento:",
          userDocument
        );
      } else {
        ApiLogger.warn("⚠️ CPF não encontrado - usuário terá que digitar");
      }

      // Limpar estados
      setRequiresBiometricAuth(false);
      setPendingTokenData(null);

      // Limpar token e redirecionar para login com CPF pré-preenchido
      apiClient.clearAuthToken();

      if (userDocument) {
        // Garantir que o CPF esteja no formato correto (apenas números)
        const cleanDocument = userDocument.replace(/\D/g, "");
        ApiLogger.info("✅ Redirecionando para login com CPF:", cleanDocument);
        router.replace({
          pathname: "/(auth)/login",
          params: {document: cleanDocument, fromBiometric: "true"}
        });
      } else {
        ApiLogger.warn(
          "⚠️ CPF não encontrado - redirecionando para login sem pré-preenchimento"
        );
        router.replace("/(auth)/login");
      }
    } catch (error) {
      ApiLogger.error("Erro ao obter dados para login", error as Error);

      // Limpar estados
      setRequiresBiometricAuth(false);
      setPendingTokenData(null);

      // Limpar token e redirecionar para login
      apiClient.clearAuthToken();
      router.replace("/(auth)/login");
    }
  };

  const reloadSession = async () => {
    console.log("🔄 Recarregando sessão após upsell");
    await checkExistingSession();
  };

  const value: AuthContextType = useMemo(
    () => ({
      user,
      isAuthenticated,
      isLoading,
      requiresBiometricAuth,
      login: handleLogin,
      logout: handleLogout,
      refreshUser,
      reloadSession,
      authenticateWithBiometrics,
      skipBiometricAuth,
      setOnAuthSuccessCallback
    }),
    [
      user,
      isAuthenticated,
      isLoading,
      requiresBiometricAuth,
      handleLogin,
      handleLogout,
      refreshUser,
      reloadSession,
      authenticateWithBiometrics,
      skipBiometricAuth,
      setOnAuthSuccessCallback
    ]
  );

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth deve ser usado dentro de um AuthProvider");
  }
  return context;
}

export default AuthContext;
