import React from "react";
import Svg, {Defs, Clip<PERSON><PERSON>, Path, G, SvgProps} from "react-native-svg";

interface LogoutIconProps extends SvgProps {
  width?: number;
  height?: number;
  fill?: string;
}

const LogoutIcon: React.FC<LogoutIconProps> = ({
  width = 24,
  height = 24,
  fill = "rgb(52, 64, 83)",
  ...props
}) => {
  return (
    <Svg width={width} height={height} viewBox="0 0 24 24" {...props}>
      <Defs>
        <ClipPath id="clipPath6800219361">
          <Path
            d="M0 0L24 0L24 24L0 24L0 0Z"
            fillRule="nonzero"
            transform="matrix(1 0 0 1 0 0)"
          />
        </ClipPath>
      </Defs>
      <G clipPath="url(#clipPath6800219361)">
        <Path
          d="M2 4Q2 3.16348 2.01382 2.86054Q2.03793 2.33184 2.1363 1.96472Q2.4137 0.929448 3.17157 0.171573Q3.92945 -0.586302 4.96472 -0.863703Q5.33184 -0.962072 5.86054 -0.986184Q6.16348 -1 7 -1L13.2 -1Q14.7109 -1 15.2518 -0.955806Q16.1989 -0.878426 16.816 -0.564026Q17.974 0.0260166 18.564 1.18404Q18.8784 1.80108 18.9558 2.74817Q19 3.28907 19 4.8L19 13.2Q19 14.7109 18.9558 15.2518Q18.8784 16.1989 18.564 16.816Q17.974 17.974 16.816 18.564Q16.1989 18.8784 15.2518 18.9558Q14.7109 19 13.2 19L7 19Q6.16345 19 5.86054 18.9862Q5.33185 18.9621 4.96472 18.8637Q3.92945 18.5863 3.17157 17.8284Q2.4137 17.0706 2.1363 16.0353Q2.03793 15.6682 2.01382 15.1395Q2 14.8365 2 14C2 13.4477 2.44772 13 3 13C3.55228 13 4 13.4477 4 14Q4 14.7909 4.01174 15.0483Q4.02596 15.3602 4.06815 15.5176Q4.20685 16.0353 4.58579 16.4142Q4.96473 16.7932 5.48236 16.9319Q5.6398 16.974 5.95165 16.9883Q6.20907 17 7 17L13.2 17Q14.6294 17 15.089 16.9624Q15.6426 16.9172 15.908 16.782Q16.487 16.487 16.782 15.908Q16.9172 15.6426 16.9624 15.089Q17 14.6294 17 13.2L17 4.8Q17 3.37065 16.9624 2.91104Q16.9172 2.35736 16.782 2.09202Q16.487 1.51301 15.908 1.21799Q15.6426 1.08279 15.089 1.03755Q14.6294 1 13.2 1L7 1Q6.20906 1 5.95165 1.01174Q5.63981 1.02596 5.48236 1.06815Q4.96472 1.20685 4.58579 1.58579Q4.20685 1.96472 4.06815 2.48236Q4.02596 2.6398 4.01174 2.95165Q4 3.20907 4 4C4 4.55228 3.55228 5 3 5C2.44772 5 2 4.55228 2 4ZM9.70711 4.29289C9.31659 3.90237 8.68341 3.90237 8.29289 4.29289C7.90237 4.68341 7.90237 5.31659 8.29289 5.70711L10.5858 8L0 8C-0.55228 8 -1 8.44772 -1 9C-1 9.55228 -0.55228 10 0 10L10.5858 10L8.29289 12.2929C7.90237 12.6834 7.90237 13.3166 8.29289 13.7071C8.68341 14.0976 9.31659 14.0976 9.70711 13.7071L13.7071 9.70711Q13.7767 9.63746 13.8315 9.55557Q13.8862 9.47368 13.9239 9.38268Q13.9616 9.29169 13.9808 9.19509Q14 9.09849 14 9Q14 8.90151 13.9808 8.80491Q13.9616 8.70831 13.9239 8.61732Q13.8862 8.52632 13.8315 8.44443Q13.7767 8.36254 13.7071 8.29289L9.70711 4.29289Z"
          fillRule="evenodd"
          transform="matrix(1 0 0 1 3 3)"
          fill={fill}
        />
      </G>
    </Svg>
  );
};

export default LogoutIcon;
