import React from "react";
import {TouchableOpacity} from "react-native";
import FilterIcon from "./icons/filter-icon";
import styles from "../styles/components/filter-button.style";

export interface FilterButtonProps {
  onPress?: () => void;
}

const FilterButton: React.FC<FilterButtonProps> = ({onPress}) => {
  return (
    <TouchableOpacity
      style={styles.container}
      onPress={onPress}
      testID="filter-button"
    >
      <FilterIcon />
    </TouchableOpacity>
  );
};

export default FilterButton;
