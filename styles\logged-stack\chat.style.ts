import {StyleSheet} from "react-native";

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#111828" // Cor exata do Motiff
  },
  messageListContainer: {
    flex: 1,
    paddingHorizontal: 24 // 24px como no design
  },
  chatContent: {
    paddingVertical: 18,
    flexGrow: 1,
    justifyContent: "flex-end"
  },
  // Date divider styles
  dateDivider: {
    alignItems: "center",
    marginVertical: 18,
    paddingHorizontal: 24
  },
  dateDividerText: {
    fontSize: 14,
    fontWeight: "700",
    lineHeight: 20,
    color: "#DFE9F0",
    textAlign: "center",
    fontFamily: "Ubuntu"
  },

  // Message bubble styles
  messageRow: {
    flexDirection: "row",
    alignItems: "flex-start",
    marginBottom: 14,
    gap: 10
  },
  messageRowUser: {
    justifyContent: "flex-end",
    paddingLeft: 79, // Espaçamento do design para mensagens próprias
    gap: 0 // Remove gap for user messages since there's no avatar
  },
  avatarContainer: {
    width: 33.5,
    height: 33.5,
    justifyContent: "center",
    alignItems: "center"
  },
  avatar: {
    width: 33.5,
    height: 33.5,
    borderRadius: 16.75
  },
  avatarBorder: {
    // Removido pois não aparece no design
  },
  messageContainer: {
    paddingVertical: 10,
    paddingHorizontal: 14,
    minHeight: 40
  },
  messageFromOther: {
    backgroundColor: "#1D2939", // Cor exata do Motiff
    borderRadius: 8,
    borderTopLeftRadius: 0, // Canto superior esquerdo reto
    maxWidth: 268,
    alignSelf: "flex-start"
  },
  messageFromUser: {
    backgroundColor: "#1D2939", // Mesma cor para ambos no design
    borderRadius: 8,
    borderTopRightRadius: 0, // Canto superior direito reto
    maxWidth: 272,
    alignSelf: "flex-end"
  },
  messageText: {
    fontSize: 14,
    lineHeight: 20,
    color: "#DFE9F0", // Cor exata do texto no Motiff
    fontFamily: "Ubuntu",
    fontWeight: "400"
  },
  timeContainer: {
    marginTop: 0,
    alignItems: "flex-end"
  },
  timeText: {
    fontSize: 10,
    lineHeight: 18,
    color: "#DFE9F0",
    fontFamily: "Ubuntu",
    textAlign: "right",
    marginTop: 0
  },
  // Typing indicator
  typingContainer: {
    paddingHorizontal: 24,
    paddingVertical: 8
  },
  typingText: {
    fontSize: 14,
    color: "#DFE9F0",
    fontStyle: "italic",
    fontFamily: "Ubuntu"
  },

  // Input area styles
  inputContainer: {
    backgroundColor: "#111828",
    borderTopWidth: 1,
    borderTopColor: "#1D2939",
    paddingHorizontal: 24,
    paddingVertical: 16,
    gap: 16
  },
  inputRow: {
    flexDirection: "row",
    alignItems: "flex-end",
    gap: 12,
    minHeight: 44
  },
  textInputContainer: {
    backgroundColor: "#1C2230", // Cor exata do input no Motiff
    borderWidth: 1,
    borderColor: "#F2F4F7",
    borderRadius: 8,
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 14,
    paddingVertical: 12,
    flex: 1,
    gap: 8,
    shadowColor: "rgba(16, 24, 40, 0.05)",
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 1,
    shadowRadius: 2,
    elevation: 1
  },
  textInput: {
    flex: 1,
    fontSize: 14,
    lineHeight: 20,
    color: "#F2F4F7",
    fontFamily: "Ubuntu",
    minHeight: 20,
    maxHeight: 80, // 4 lines * 20 lineHeight
    padding: 0,
    margin: 0,
    textAlignVertical: "top"
  },
  sendButton: {
    width: 44,
    height: 44,
    borderRadius: 8,
    backgroundColor: "#0F7C4D", // Verde exato do Motiff
    borderWidth: 1,
    borderColor: "#0F7C4D",
    justifyContent: "center",
    alignItems: "center",
    shadowColor: "rgba(16, 24, 40, 0.05)",
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 1,
    shadowRadius: 2,
    elevation: 1
  },
  sendButtonDisabled: {
    backgroundColor: "#374151",
    borderColor: "#374151",
    opacity: 0.5
  }
});

export default styles;
