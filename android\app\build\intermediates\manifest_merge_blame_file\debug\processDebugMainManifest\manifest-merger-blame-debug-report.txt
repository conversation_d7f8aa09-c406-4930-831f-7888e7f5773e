1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="studio.takasaki.clubm"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
11-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:5:3-75
11-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:5:20-73
12    <uses-permission android:name="android.permission.INTERNET" />
12-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:2:3-64
12-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:2:20-62
13    <uses-permission
13-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:3:3-77
14        android:name="android.permission.READ_EXTERNAL_STORAGE"
14-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:3:20-75
15        android:maxSdkVersion="32" />
15-->[BareExpo:expo.modules.image:2.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bba4934fb137bea6ff6ee5ad91c7be7a\transformed\expo.modules.image-2.4.0\AndroidManifest.xml:17:9-35
16    <uses-permission android:name="android.permission.RECORD_AUDIO" />
16-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:4:3-68
16-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:4:20-66
17    <uses-permission android:name="android.permission.USE_BIOMETRIC" />
17-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:6:3-69
17-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:6:20-67
18    <uses-permission android:name="android.permission.USE_FINGERPRINT" />
18-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:7:3-71
18-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:7:20-69
19    <uses-permission android:name="android.permission.VIBRATE" />
19-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:8:3-63
19-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:8:20-61
20    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
20-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:9:3-78
20-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:9:20-76
21
22    <queries>
22-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:10:3-16:13
23        <intent>
23-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:11:5-15:14
24            <action android:name="android.intent.action.VIEW" />
24-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:12:7-58
24-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:12:15-56
25
26            <category android:name="android.intent.category.BROWSABLE" />
26-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:13:7-67
26-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:13:17-65
27
28            <data android:scheme="https" />
28-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:14:7-37
28-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:14:13-35
29        </intent>
30        <intent>
30-->[:react-native-webview] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-10:18
31            <action android:name="org.chromium.intent.action.PAY" />
31-->[:react-native-webview] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-69
31-->[:react-native-webview] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:21-66
32        </intent>
33        <intent>
33-->[:react-native-webview] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:9-13:18
34            <action android:name="org.chromium.intent.action.IS_READY_TO_PAY" />
34-->[:react-native-webview] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-81
34-->[:react-native-webview] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:21-78
35        </intent>
36        <intent>
36-->[:react-native-webview] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:9-16:18
37            <action android:name="org.chromium.intent.action.UPDATE_PAYMENT_DETAILS" />
37-->[:react-native-webview] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-88
37-->[:react-native-webview] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:21-85
38        </intent>
39
40        <package android:name="host.exp.exponent" /> <!-- Query open documents -->
40-->[:expo-dev-launcher] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-53
40-->[:expo-dev-launcher] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:18-50
41        <intent>
41-->[host.exp.exponent:expo.modules.documentpicker:13.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\ade9cb268361204a99fc2de1daca44d2\transformed\expo.modules.documentpicker-13.1.6\AndroidManifest.xml:10:9-17:18
42            <action android:name="android.intent.action.OPEN_DOCUMENT" />
42-->[host.exp.exponent:expo.modules.documentpicker:13.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\ade9cb268361204a99fc2de1daca44d2\transformed\expo.modules.documentpicker-13.1.6\AndroidManifest.xml:11:13-74
42-->[host.exp.exponent:expo.modules.documentpicker:13.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\ade9cb268361204a99fc2de1daca44d2\transformed\expo.modules.documentpicker-13.1.6\AndroidManifest.xml:11:21-71
43
44            <category android:name="android.intent.category.DEFAULT" />
44-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:28:9-67
44-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:28:19-65
45            <category android:name="android.intent.category.OPENABLE" />
45-->[host.exp.exponent:expo.modules.documentpicker:13.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\ade9cb268361204a99fc2de1daca44d2\transformed\expo.modules.documentpicker-13.1.6\AndroidManifest.xml:14:13-73
45-->[host.exp.exponent:expo.modules.documentpicker:13.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\ade9cb268361204a99fc2de1daca44d2\transformed\expo.modules.documentpicker-13.1.6\AndroidManifest.xml:14:23-70
46
47            <data android:mimeType="*/*" />
47-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:14:7-37
48        </intent> <!-- Query open documents -->
49        <intent>
49-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d42067affa37cf5106d40977269b7ff\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:15:9-17:18
50            <action android:name="android.intent.action.OPEN_DOCUMENT_TREE" />
50-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d42067affa37cf5106d40977269b7ff\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:16:13-79
50-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d42067affa37cf5106d40977269b7ff\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:16:21-76
51        </intent>
52        <intent>
52-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d200739c45c5eb15c894cf88f6414d1\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:15:9-19:18
53
54            <!-- Required for picking images from the camera roll if targeting API 30 -->
55            <action android:name="android.media.action.IMAGE_CAPTURE" />
55-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d200739c45c5eb15c894cf88f6414d1\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:18:13-73
55-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d200739c45c5eb15c894cf88f6414d1\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:18:21-70
56        </intent>
57        <intent>
57-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d200739c45c5eb15c894cf88f6414d1\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:20:9-24:18
58
59            <!-- Required for picking images from the camera if targeting API 30 -->
60            <action android:name="android.media.action.ACTION_VIDEO_CAPTURE" />
60-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d200739c45c5eb15c894cf88f6414d1\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:23:13-80
60-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d200739c45c5eb15c894cf88f6414d1\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:23:21-77
61        </intent>
62        <intent>
62-->[host.exp.exponent:expo.modules.sharing:13.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\26f9d9c35692a678d98a9a00db11c607\transformed\expo.modules.sharing-13.1.5\AndroidManifest.xml:8:9-14:18
63
64            <!-- Required for file sharing if targeting API 30 -->
65            <action android:name="android.intent.action.SEND" />
65-->[host.exp.exponent:expo.modules.sharing:13.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\26f9d9c35692a678d98a9a00db11c607\transformed\expo.modules.sharing-13.1.5\AndroidManifest.xml:11:13-65
65-->[host.exp.exponent:expo.modules.sharing:13.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\26f9d9c35692a678d98a9a00db11c607\transformed\expo.modules.sharing-13.1.5\AndroidManifest.xml:11:21-62
66
67            <data android:mimeType="*/*" />
67-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:14:7-37
68        </intent>
69        <intent>
69-->[host.exp.exponent:expo.modules.webbrowser:14.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\87c617bde6497fe706593a4fbb26372c\transformed\expo.modules.webbrowser-14.2.0\AndroidManifest.xml:8:9-12:18
70
71            <!-- Required for opening tabs if targeting API 30 -->
72            <action android:name="android.support.customtabs.action.CustomTabsService" />
72-->[host.exp.exponent:expo.modules.webbrowser:14.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\87c617bde6497fe706593a4fbb26372c\transformed\expo.modules.webbrowser-14.2.0\AndroidManifest.xml:11:13-90
72-->[host.exp.exponent:expo.modules.webbrowser:14.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\87c617bde6497fe706593a4fbb26372c\transformed\expo.modules.webbrowser-14.2.0\AndroidManifest.xml:11:21-87
73        </intent>
74        <intent>
74-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\da3d785accc181b5d8cd59ecc7f8a711\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:8:9-14:18
75            <action android:name="android.intent.action.GET_CONTENT" />
75-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\da3d785accc181b5d8cd59ecc7f8a711\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:9:13-72
75-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\da3d785accc181b5d8cd59ecc7f8a711\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:9:21-69
76
77            <category android:name="android.intent.category.OPENABLE" />
77-->[host.exp.exponent:expo.modules.documentpicker:13.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\ade9cb268361204a99fc2de1daca44d2\transformed\expo.modules.documentpicker-13.1.6\AndroidManifest.xml:14:13-73
77-->[host.exp.exponent:expo.modules.documentpicker:13.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\ade9cb268361204a99fc2de1daca44d2\transformed\expo.modules.documentpicker-13.1.6\AndroidManifest.xml:14:23-70
78
79            <data android:mimeType="*/*" />
79-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:14:7-37
80        </intent>
81    </queries>
82    <!--
83         Required to keep CPU alive while downloading files
84        (NOT to keep screen awake)
85    -->
86    <uses-permission android:name="android.permission.WAKE_LOCK" />
86-->[:react-native-blob-util] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\react-native-blob-util\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:5-68
86-->[:react-native-blob-util] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\react-native-blob-util\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:22-65
87    <!--
88         Required to poll the state of the network connection
89        and respond to changes
90    -->
91    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" /> <!-- Required to check whether Wi-Fi is enabled -->
91-->[:react-native-blob-util] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\react-native-blob-util\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:5-79
91-->[:react-native-blob-util] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\react-native-blob-util\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:22-76
92    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" /> <!-- Required to to download files without a notification -->
92-->[:react-native-blob-util] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\react-native-blob-util\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:5-76
92-->[:react-native-blob-util] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\react-native-blob-util\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:22-73
93    <uses-permission android:name="android.permission.DOWNLOAD_WITHOUT_NOTIFICATION" /> <!-- Required for picking images from camera directly -->
93-->[:react-native-blob-util] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\react-native-blob-util\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:32:5-88
93-->[:react-native-blob-util] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\react-native-blob-util\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:32:22-85
94    <uses-permission android:name="android.permission.CAMERA" />
94-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d200739c45c5eb15c894cf88f6414d1\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:8:5-65
94-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d200739c45c5eb15c894cf88f6414d1\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:8:22-62
95    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
95-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:7:5-81
95-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:7:22-78
96    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" /> <!-- Required by older versions of Google Play services to create IID tokens -->
96-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:8:5-77
96-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:8:22-74
97    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
97-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e82d33323417ccf10f84432cc6fb1bd\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:26:5-82
97-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e82d33323417ccf10f84432cc6fb1bd\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:26:22-79
98
99    <permission
99-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\af9f1036362c92a9109f915df9f93bd0\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
100        android:name="studio.takasaki.clubm.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
100-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\af9f1036362c92a9109f915df9f93bd0\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
101        android:protectionLevel="signature" />
101-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\af9f1036362c92a9109f915df9f93bd0\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
102
103    <uses-permission android:name="studio.takasaki.clubm.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
103-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\af9f1036362c92a9109f915df9f93bd0\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
103-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\af9f1036362c92a9109f915df9f93bd0\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
104    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" /> <!-- for android -->
104-->[com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\63044213529fbfda106c70808dc25156\transformed\installreferrer-2.2\AndroidManifest.xml:9:5-110
104-->[com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\63044213529fbfda106c70808dc25156\transformed\installreferrer-2.2\AndroidManifest.xml:9:22-107
105    <!-- <uses-permission android:name="com.android.launcher.permission.READ_SETTINGS"/> -->
106    <!-- <uses-permission android:name="com.android.launcher.permission.WRITE_SETTINGS"/> -->
107    <!-- <uses-permission android:name="com.android.launcher.permission.INSTALL_SHORTCUT" /> -->
108    <!-- <uses-permission android:name="com.android.launcher.permission.UNINSTALL_SHORTCUT" /> -->
109    <!-- for Samsung -->
110    <uses-permission android:name="com.sec.android.provider.badge.permission.READ" />
110-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:19:5-86
110-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:19:22-83
111    <uses-permission android:name="com.sec.android.provider.badge.permission.WRITE" /> <!-- for htc -->
111-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:20:5-87
111-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:20:22-84
112    <uses-permission android:name="com.htc.launcher.permission.READ_SETTINGS" />
112-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:23:5-81
112-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:23:22-78
113    <uses-permission android:name="com.htc.launcher.permission.UPDATE_SHORTCUT" /> <!-- for sony -->
113-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:24:5-83
113-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:24:22-80
114    <uses-permission android:name="com.sonyericsson.home.permission.BROADCAST_BADGE" />
114-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:27:5-88
114-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:27:22-85
115    <uses-permission android:name="com.sonymobile.home.permission.PROVIDER_INSERT_BADGE" /> <!-- for apex -->
115-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:28:5-92
115-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:28:22-89
116    <uses-permission android:name="com.anddoes.launcher.permission.UPDATE_COUNT" /> <!-- for solid -->
116-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:31:5-84
116-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:31:22-81
117    <uses-permission android:name="com.majeur.launcher.permission.UPDATE_BADGE" /> <!-- for huawei -->
117-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:34:5-83
117-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:34:22-80
118    <uses-permission android:name="com.huawei.android.launcher.permission.CHANGE_BADGE" />
118-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:37:5-91
118-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:37:22-88
119    <uses-permission android:name="com.huawei.android.launcher.permission.READ_SETTINGS" />
119-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:38:5-92
119-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:38:22-89
120    <uses-permission android:name="com.huawei.android.launcher.permission.WRITE_SETTINGS" /> <!-- for ZUK -->
120-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:39:5-93
120-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:39:22-90
121    <uses-permission android:name="android.permission.READ_APP_BADGE" /> <!-- for OPPO -->
121-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:42:5-73
121-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:42:22-70
122    <uses-permission android:name="com.oppo.launcher.permission.READ_SETTINGS" />
122-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:45:5-82
122-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:45:22-79
123    <uses-permission android:name="com.oppo.launcher.permission.WRITE_SETTINGS" /> <!-- for EvMe -->
123-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:46:5-83
123-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:46:22-80
124    <uses-permission android:name="me.everything.badger.permission.BADGE_COUNT_READ" />
124-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:49:5-88
124-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:49:22-85
125    <uses-permission android:name="me.everything.badger.permission.BADGE_COUNT_WRITE" />
125-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:50:5-89
125-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\951f10ecc3f3197c65372be7419a8fe9\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:50:22-86
126
127    <application
127-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:17:3-59:17
128        android:name="studio.takasaki.clubm.MainApplication"
128-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:17:16-47
129        android:allowBackup="true"
129-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:17:162-188
130        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
130-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\af9f1036362c92a9109f915df9f93bd0\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
131        android:dataExtractionRules="@xml/secure_store_data_extraction_rules"
131-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:17:307-376
132        android:debuggable="true"
133        android:extractNativeLibs="false"
134        android:fullBackupContent="@xml/secure_store_backup_rules"
134-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:17:248-306
135        android:icon="@mipmap/ic_launcher"
135-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:17:81-115
136        android:label="@string/app_name"
136-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:17:48-80
137        android:roundIcon="@mipmap/ic_launcher_round"
137-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:17:116-161
138        android:supportsRtl="true"
138-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:17:221-247
139        android:theme="@style/AppTheme"
139-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:17:189-220
140        android:usesCleartextTraffic="true" >
140-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\debug\AndroidManifest.xml:6:18-53
141        <meta-data
141-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:18:5-83
142            android:name="expo.modules.updates.ENABLED"
142-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:18:16-59
143            android:value="false" />
143-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:18:60-81
144        <meta-data
144-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:19:5-105
145            android:name="expo.modules.updates.EXPO_UPDATES_CHECK_ON_LAUNCH"
145-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:19:16-80
146            android:value="ALWAYS" />
146-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:19:81-103
147        <meta-data
147-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:20:5-99
148            android:name="expo.modules.updates.EXPO_UPDATES_LAUNCH_WAIT_MS"
148-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:20:16-79
149            android:value="0" />
149-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:20:80-97
150
151        <activity
151-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:21:5-58:16
152            android:name="studio.takasaki.clubm.MainActivity"
152-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:21:15-43
153            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|uiMode|locale|layoutDirection"
153-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:21:44-157
154            android:exported="true"
154-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:21:279-302
155            android:launchMode="singleTask"
155-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:21:158-189
156            android:screenOrientation="portrait"
156-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:21:303-339
157            android:theme="@style/Theme.App.SplashScreen"
157-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:21:233-278
158            android:windowSoftInputMode="adjustResize" >
158-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:21:190-232
159            <intent-filter>
159-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:22:7-25:23
160                <action android:name="android.intent.action.MAIN" />
160-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:23:9-60
160-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:23:17-58
161
162                <category android:name="android.intent.category.LAUNCHER" />
162-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:24:9-68
162-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:24:19-66
163            </intent-filter>
164            <intent-filter>
164-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:26:7-32:23
165                <action android:name="android.intent.action.VIEW" />
165-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:12:7-58
165-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:12:15-56
166
167                <category android:name="android.intent.category.DEFAULT" />
167-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:28:9-67
167-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:28:19-65
168                <category android:name="android.intent.category.BROWSABLE" />
168-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:13:7-67
168-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:13:17-65
169
170                <data android:scheme="clubm" />
170-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:14:7-37
170-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:14:13-35
171                <data android:scheme="exp+club-m-app" />
171-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:14:7-37
171-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:14:13-35
172            </intent-filter>
173            <!-- Universal Links -->
174            <intent-filter android:autoVerify="true" >
174-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:34:7-39:23
174-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:34:22-47
175                <action android:name="android.intent.action.VIEW" />
175-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:12:7-58
175-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:12:15-56
176
177                <category android:name="android.intent.category.DEFAULT" />
177-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:28:9-67
177-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:28:19-65
178                <category android:name="android.intent.category.BROWSABLE" />
178-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:13:7-67
178-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:13:17-65
179
180                <data
180-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:14:7-37
181                    android:host="clubm.app"
182                    android:scheme="https" />
182-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:14:13-35
183            </intent-filter>
184            <intent-filter android:autoVerify="true" >
184-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:40:7-45:23
184-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:40:22-47
185                <action android:name="android.intent.action.VIEW" />
185-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:12:7-58
185-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:12:15-56
186
187                <category android:name="android.intent.category.DEFAULT" />
187-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:28:9-67
187-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:28:19-65
188                <category android:name="android.intent.category.BROWSABLE" />
188-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:13:7-67
188-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:13:17-65
189
190                <data
190-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:14:7-37
191                    android:host="app.clubm.com"
192                    android:scheme="https" />
192-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:14:13-35
193            </intent-filter>
194            <intent-filter android:autoVerify="true" >
194-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:46:7-51:23
194-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:46:22-47
195                <action android:name="android.intent.action.VIEW" />
195-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:12:7-58
195-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:12:15-56
196
197                <category android:name="android.intent.category.DEFAULT" />
197-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:28:9-67
197-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:28:19-65
198                <category android:name="android.intent.category.BROWSABLE" />
198-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:13:7-67
198-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:13:17-65
199
200                <data
200-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:14:7-37
201                    android:host="www.clubm.app"
202                    android:scheme="https" />
202-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:14:13-35
203            </intent-filter>
204            <intent-filter android:autoVerify="true" >
204-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:52:7-57:23
204-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:52:22-47
205                <action android:name="android.intent.action.VIEW" />
205-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:12:7-58
205-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:12:15-56
206
207                <category android:name="android.intent.category.DEFAULT" />
207-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:28:9-67
207-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:28:19-65
208                <category android:name="android.intent.category.BROWSABLE" />
208-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:13:7-67
208-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:13:17-65
209
210                <data
210-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:14:7-37
211                    android:host="www.app.clubm.com"
212                    android:scheme="https" />
212-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:14:13-35
213            </intent-filter>
214        </activity>
215
216        <provider
216-->[:react-native-webview] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:9-28:20
217            android:name="com.reactnativecommunity.webview.RNCWebViewFileProvider"
217-->[:react-native-webview] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-83
218            android:authorities="studio.takasaki.clubm.fileprovider"
218-->[:react-native-webview] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-64
219            android:exported="false"
219-->[:react-native-webview] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:13-37
220            android:grantUriPermissions="true" >
220-->[:react-native-webview] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-47
221            <meta-data
221-->[:react-native-webview] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:13-27:63
222                android:name="android.support.FILE_PROVIDER_PATHS"
222-->[:react-native-webview] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:17-67
223                android:resource="@xml/file_provider_paths" />
223-->[:react-native-webview] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:17-60
224        </provider>
225        <provider
225-->[:react-native-blob-util] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\react-native-blob-util\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:35:9-43:20
226            android:name="com.ReactNativeBlobUtil.Utils.FileProvider"
226-->[:react-native-blob-util] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\react-native-blob-util\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:36:13-70
227            android:authorities="studio.takasaki.clubm.provider"
227-->[:react-native-blob-util] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\react-native-blob-util\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:37:13-60
228            android:exported="false"
228-->[:react-native-blob-util] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\react-native-blob-util\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:38:13-37
229            android:grantUriPermissions="true" >
229-->[:react-native-blob-util] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\react-native-blob-util\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:39:13-47
230            <meta-data
230-->[:react-native-webview] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:13-27:63
231                android:name="android.support.FILE_PROVIDER_PATHS"
231-->[:react-native-webview] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:17-67
232                android:resource="@xml/provider_paths" />
232-->[:react-native-webview] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:17-60
233        </provider>
234
235        <activity
235-->[:expo-dev-launcher] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-25:20
236            android:name="expo.modules.devlauncher.launcher.DevLauncherActivity"
236-->[:expo-dev-launcher] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-81
237            android:exported="true"
237-->[:expo-dev-launcher] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-36
238            android:launchMode="singleTask"
238-->[:expo-dev-launcher] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-44
239            android:theme="@style/Theme.DevLauncher.LauncherActivity" >
239-->[:expo-dev-launcher] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-70
240            <intent-filter>
240-->[:expo-dev-launcher] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-24:29
241                <action android:name="android.intent.action.VIEW" />
241-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:12:7-58
241-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:12:15-56
242
243                <category android:name="android.intent.category.DEFAULT" />
243-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:28:9-67
243-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:28:19-65
244                <category android:name="android.intent.category.BROWSABLE" />
244-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:13:7-67
244-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:13:17-65
245
246                <data android:scheme="expo-dev-launcher" />
246-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:14:7-37
246-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:14:13-35
247            </intent-filter>
248        </activity>
249        <activity
249-->[:expo-dev-launcher] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:9-29:70
250            android:name="expo.modules.devlauncher.launcher.errors.DevLauncherErrorActivity"
250-->[:expo-dev-launcher] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-93
251            android:screenOrientation="portrait"
251-->[:expo-dev-launcher] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-49
252            android:theme="@style/Theme.DevLauncher.ErrorActivity" />
252-->[:expo-dev-launcher] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-67
253        <activity
253-->[:expo-dev-menu] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-21:20
254            android:name="expo.modules.devmenu.DevMenuActivity"
254-->[:expo-dev-menu] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-64
255            android:exported="true"
255-->[:expo-dev-menu] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-36
256            android:launchMode="singleTask"
256-->[:expo-dev-menu] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-44
257            android:theme="@style/Theme.AppCompat.Transparent.NoActionBar" >
257-->[:expo-dev-menu] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-75
258            <intent-filter>
258-->[:expo-dev-menu] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-20:29
259                <action android:name="android.intent.action.VIEW" />
259-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:12:7-58
259-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:12:15-56
260
261                <category android:name="android.intent.category.DEFAULT" />
261-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:28:9-67
261-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:28:19-65
262                <category android:name="android.intent.category.BROWSABLE" />
262-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:13:7-67
262-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:13:17-65
263
264                <data android:scheme="expo-dev-menu" />
264-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:14:7-37
264-->C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\android\app\src\main\AndroidManifest.xml:14:13-35
265            </intent-filter>
266        </activity>
267
268        <meta-data
268-->[:expo-modules-core] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-11:89
269            android:name="org.unimodules.core.AppLoader#react-native-headless"
269-->[:expo-modules-core] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-79
270            android:value="expo.modules.adapters.react.apploader.RNHeadlessAppLoader" />
270-->[:expo-modules-core] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-86
271        <meta-data
271-->[:expo-modules-core] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-15:45
272            android:name="com.facebook.soloader.enabled"
272-->[:expo-modules-core] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-57
273            android:value="true" />
273-->[:expo-modules-core] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-33
274
275        <activity
275-->[com.facebook.react:react-android:0.79.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\a89efe01639eb7dad7f1852c2dc2010d\transformed\react-android-0.79.5-debug\AndroidManifest.xml:19:9-21:40
276            android:name="com.facebook.react.devsupport.DevSettingsActivity"
276-->[com.facebook.react:react-android:0.79.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\a89efe01639eb7dad7f1852c2dc2010d\transformed\react-android-0.79.5-debug\AndroidManifest.xml:20:13-77
277            android:exported="false" />
277-->[com.facebook.react:react-android:0.79.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\a89efe01639eb7dad7f1852c2dc2010d\transformed\react-android-0.79.5-debug\AndroidManifest.xml:21:13-37
278
279        <provider
279-->[host.exp.exponent:expo.modules.clipboard:7.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\ac521f0f9f44cf2974d5bc1f65def9f4\transformed\expo.modules.clipboard-7.1.5\AndroidManifest.xml:8:9-15:20
280            android:name="expo.modules.clipboard.ClipboardFileProvider"
280-->[host.exp.exponent:expo.modules.clipboard:7.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\ac521f0f9f44cf2974d5bc1f65def9f4\transformed\expo.modules.clipboard-7.1.5\AndroidManifest.xml:9:13-72
281            android:authorities="studio.takasaki.clubm.ClipboardFileProvider"
281-->[host.exp.exponent:expo.modules.clipboard:7.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\ac521f0f9f44cf2974d5bc1f65def9f4\transformed\expo.modules.clipboard-7.1.5\AndroidManifest.xml:10:13-73
282            android:exported="true" >
282-->[host.exp.exponent:expo.modules.clipboard:7.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\ac521f0f9f44cf2974d5bc1f65def9f4\transformed\expo.modules.clipboard-7.1.5\AndroidManifest.xml:11:13-36
283            <meta-data
283-->[host.exp.exponent:expo.modules.clipboard:7.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\ac521f0f9f44cf2974d5bc1f65def9f4\transformed\expo.modules.clipboard-7.1.5\AndroidManifest.xml:12:13-14:68
284                android:name="expo.modules.clipboard.CLIPBOARD_FILE_PROVIDER_PATHS"
284-->[host.exp.exponent:expo.modules.clipboard:7.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\ac521f0f9f44cf2974d5bc1f65def9f4\transformed\expo.modules.clipboard-7.1.5\AndroidManifest.xml:13:17-84
285                android:resource="@xml/clipboard_provider_paths" />
285-->[host.exp.exponent:expo.modules.clipboard:7.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\ac521f0f9f44cf2974d5bc1f65def9f4\transformed\expo.modules.clipboard-7.1.5\AndroidManifest.xml:14:17-65
286        </provider>
287        <provider
287-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d42067affa37cf5106d40977269b7ff\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:21:9-30:20
288            android:name="expo.modules.filesystem.FileSystemFileProvider"
288-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d42067affa37cf5106d40977269b7ff\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:22:13-74
289            android:authorities="studio.takasaki.clubm.FileSystemFileProvider"
289-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d42067affa37cf5106d40977269b7ff\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:23:13-74
290            android:exported="false"
290-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d42067affa37cf5106d40977269b7ff\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:24:13-37
291            android:grantUriPermissions="true" >
291-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d42067affa37cf5106d40977269b7ff\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:25:13-47
292            <meta-data
292-->[:react-native-webview] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:13-27:63
293                android:name="android.support.FILE_PROVIDER_PATHS"
293-->[:react-native-webview] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:17-67
294                android:resource="@xml/file_system_provider_paths" />
294-->[:react-native-webview] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:17-60
295        </provider>
296
297        <service
297-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d200739c45c5eb15c894cf88f6414d1\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:28:9-40:19
298            android:name="com.google.android.gms.metadata.ModuleDependencies"
298-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d200739c45c5eb15c894cf88f6414d1\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:29:13-78
299            android:enabled="false"
299-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d200739c45c5eb15c894cf88f6414d1\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:30:13-36
300            android:exported="false" >
300-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d200739c45c5eb15c894cf88f6414d1\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:31:13-37
301            <intent-filter>
301-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d200739c45c5eb15c894cf88f6414d1\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:33:13-35:29
302                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
302-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d200739c45c5eb15c894cf88f6414d1\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:34:17-94
302-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d200739c45c5eb15c894cf88f6414d1\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:34:25-91
303            </intent-filter>
304
305            <meta-data
305-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d200739c45c5eb15c894cf88f6414d1\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:37:13-39:36
306                android:name="photopicker_activity:0:required"
306-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d200739c45c5eb15c894cf88f6414d1\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:38:17-63
307                android:value="" />
307-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d200739c45c5eb15c894cf88f6414d1\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:39:17-33
308        </service>
309
310        <activity
310-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d200739c45c5eb15c894cf88f6414d1\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:42:9-44:59
311            android:name="com.canhub.cropper.CropImageActivity"
311-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d200739c45c5eb15c894cf88f6414d1\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:43:13-64
312            android:exported="true"
312-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\da3d785accc181b5d8cd59ecc7f8a711\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:33:13-36
313            android:theme="@style/Base.Theme.AppCompat" /> <!-- https://developer.android.com/guide/topics/manifest/provider-element.html -->
313-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d200739c45c5eb15c894cf88f6414d1\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:44:13-56
314        <provider
314-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d200739c45c5eb15c894cf88f6414d1\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:46:9-54:20
315            android:name="expo.modules.imagepicker.fileprovider.ImagePickerFileProvider"
315-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d200739c45c5eb15c894cf88f6414d1\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:47:13-89
316            android:authorities="studio.takasaki.clubm.ImagePickerFileProvider"
316-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d200739c45c5eb15c894cf88f6414d1\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:48:13-75
317            android:exported="false"
317-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d200739c45c5eb15c894cf88f6414d1\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:49:13-37
318            android:grantUriPermissions="true" >
318-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d200739c45c5eb15c894cf88f6414d1\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:50:13-47
319            <meta-data
319-->[:react-native-webview] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:13-27:63
320                android:name="android.support.FILE_PROVIDER_PATHS"
320-->[:react-native-webview] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:17-67
321                android:resource="@xml/image_picker_provider_paths" />
321-->[:react-native-webview] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:17-60
322        </provider>
323
324        <service
324-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:11:9-17:19
325            android:name="expo.modules.notifications.service.ExpoFirebaseMessagingService"
325-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:12:13-91
326            android:exported="false" >
326-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:13:13-37
327            <intent-filter android:priority="-1" >
327-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:14:13-16:29
327-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:14:28-49
328                <action android:name="com.google.firebase.MESSAGING_EVENT" />
328-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:15:17-78
328-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:15:25-75
329            </intent-filter>
330        </service>
331
332        <receiver
332-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:19:9-31:20
333            android:name="expo.modules.notifications.service.NotificationsService"
333-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:20:13-83
334            android:enabled="true"
334-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:21:13-35
335            android:exported="false" >
335-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:22:13-37
336            <intent-filter android:priority="-1" >
336-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:23:13-30:29
336-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:23:28-49
337                <action android:name="expo.modules.notifications.NOTIFICATION_EVENT" />
337-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:24:17-88
337-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:24:25-85
338                <action android:name="android.intent.action.BOOT_COMPLETED" />
338-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:25:17-79
338-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:25:25-76
339                <action android:name="android.intent.action.REBOOT" />
339-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:26:17-71
339-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:26:25-68
340                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
340-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:27:17-82
340-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:27:25-79
341                <action android:name="com.htc.intent.action.QUICKBOOT_POWERON" />
341-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:28:17-82
341-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:28:25-79
342                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
342-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:29:17-84
342-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:29:25-81
343            </intent-filter>
344        </receiver>
345
346        <activity
346-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:33:9-40:75
347            android:name="expo.modules.notifications.service.NotificationForwarderActivity"
347-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:34:13-92
348            android:excludeFromRecents="true"
348-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:35:13-46
349            android:exported="false"
349-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:36:13-37
350            android:launchMode="standard"
350-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:37:13-42
351            android:noHistory="true"
351-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:38:13-37
352            android:taskAffinity=""
352-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:39:13-36
353            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
353-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:40:13-72
354
355        <provider
355-->[host.exp.exponent:expo.modules.sharing:13.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\26f9d9c35692a678d98a9a00db11c607\transformed\expo.modules.sharing-13.1.5\AndroidManifest.xml:18:9-26:20
356            android:name="expo.modules.sharing.SharingFileProvider"
356-->[host.exp.exponent:expo.modules.sharing:13.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\26f9d9c35692a678d98a9a00db11c607\transformed\expo.modules.sharing-13.1.5\AndroidManifest.xml:19:13-68
357            android:authorities="studio.takasaki.clubm.SharingFileProvider"
357-->[host.exp.exponent:expo.modules.sharing:13.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\26f9d9c35692a678d98a9a00db11c607\transformed\expo.modules.sharing-13.1.5\AndroidManifest.xml:20:13-71
358            android:exported="false"
358-->[host.exp.exponent:expo.modules.sharing:13.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\26f9d9c35692a678d98a9a00db11c607\transformed\expo.modules.sharing-13.1.5\AndroidManifest.xml:21:13-37
359            android:grantUriPermissions="true" >
359-->[host.exp.exponent:expo.modules.sharing:13.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\26f9d9c35692a678d98a9a00db11c607\transformed\expo.modules.sharing-13.1.5\AndroidManifest.xml:22:13-47
360            <meta-data
360-->[:react-native-webview] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:13-27:63
361                android:name="android.support.FILE_PROVIDER_PATHS"
361-->[:react-native-webview] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:17-67
362                android:resource="@xml/sharing_provider_paths" />
362-->[:react-native-webview] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:17-60
363        </provider>
364
365        <receiver
365-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e82d33323417ccf10f84432cc6fb1bd\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:29:9-40:20
366            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
366-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e82d33323417ccf10f84432cc6fb1bd\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:30:13-78
367            android:exported="true"
367-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e82d33323417ccf10f84432cc6fb1bd\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:31:13-36
368            android:permission="com.google.android.c2dm.permission.SEND" >
368-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e82d33323417ccf10f84432cc6fb1bd\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:32:13-73
369            <intent-filter>
369-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e82d33323417ccf10f84432cc6fb1bd\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:33:13-35:29
370                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
370-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e82d33323417ccf10f84432cc6fb1bd\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:34:17-81
370-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e82d33323417ccf10f84432cc6fb1bd\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:34:25-78
371            </intent-filter>
372
373            <meta-data
373-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e82d33323417ccf10f84432cc6fb1bd\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:37:13-39:40
374                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
374-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e82d33323417ccf10f84432cc6fb1bd\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:38:17-92
375                android:value="true" />
375-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e82d33323417ccf10f84432cc6fb1bd\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:39:17-37
376        </receiver>
377        <!--
378             FirebaseMessagingService performs security checks at runtime,
379             but set to not exported to explicitly avoid allowing another app to call it.
380        -->
381        <service
381-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e82d33323417ccf10f84432cc6fb1bd\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:46:9-53:19
382            android:name="com.google.firebase.messaging.FirebaseMessagingService"
382-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e82d33323417ccf10f84432cc6fb1bd\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:47:13-82
383            android:directBootAware="true"
383-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e82d33323417ccf10f84432cc6fb1bd\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:48:13-43
384            android:exported="false" >
384-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e82d33323417ccf10f84432cc6fb1bd\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:49:13-37
385            <intent-filter android:priority="-500" >
385-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:14:13-16:29
385-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:14:28-49
386                <action android:name="com.google.firebase.MESSAGING_EVENT" />
386-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:15:17-78
386-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\729440f9a5bfafac425dc06ccd65cb71\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:15:25-75
387            </intent-filter>
388        </service>
389        <service
389-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e82d33323417ccf10f84432cc6fb1bd\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:54:9-63:19
390            android:name="com.google.firebase.components.ComponentDiscoveryService"
390-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e82d33323417ccf10f84432cc6fb1bd\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:55:13-84
391            android:directBootAware="true"
391-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2eb7072148c9b18ee694522fce2385fc\transformed\firebase-common-21.0.0\AndroidManifest.xml:32:13-43
392            android:exported="false" >
392-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e82d33323417ccf10f84432cc6fb1bd\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:56:13-37
393            <meta-data
393-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e82d33323417ccf10f84432cc6fb1bd\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:57:13-59:85
394                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
394-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e82d33323417ccf10f84432cc6fb1bd\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:58:17-122
395                android:value="com.google.firebase.components.ComponentRegistrar" />
395-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e82d33323417ccf10f84432cc6fb1bd\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:59:17-82
396            <meta-data
396-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e82d33323417ccf10f84432cc6fb1bd\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:60:13-62:85
397                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
397-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e82d33323417ccf10f84432cc6fb1bd\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:61:17-119
398                android:value="com.google.firebase.components.ComponentRegistrar" />
398-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e82d33323417ccf10f84432cc6fb1bd\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:62:17-82
399            <meta-data
399-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\08ee1b762a233fa3f03006bdd2922187\transformed\firebase-installations-17.2.0\AndroidManifest.xml:15:13-17:85
400                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
400-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\08ee1b762a233fa3f03006bdd2922187\transformed\firebase-installations-17.2.0\AndroidManifest.xml:16:17-130
401                android:value="com.google.firebase.components.ComponentRegistrar" />
401-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\08ee1b762a233fa3f03006bdd2922187\transformed\firebase-installations-17.2.0\AndroidManifest.xml:17:17-82
402            <meta-data
402-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\08ee1b762a233fa3f03006bdd2922187\transformed\firebase-installations-17.2.0\AndroidManifest.xml:18:13-20:85
403                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
403-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\08ee1b762a233fa3f03006bdd2922187\transformed\firebase-installations-17.2.0\AndroidManifest.xml:19:17-127
404                android:value="com.google.firebase.components.ComponentRegistrar" />
404-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\08ee1b762a233fa3f03006bdd2922187\transformed\firebase-installations-17.2.0\AndroidManifest.xml:20:17-82
405            <meta-data
405-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\657aa62dd9cca6049dc6e877325905c7\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
406                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
406-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\657aa62dd9cca6049dc6e877325905c7\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
407                android:value="com.google.firebase.components.ComponentRegistrar" />
407-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\657aa62dd9cca6049dc6e877325905c7\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
408            <meta-data
408-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2eb7072148c9b18ee694522fce2385fc\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
409                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
409-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2eb7072148c9b18ee694522fce2385fc\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
410                android:value="com.google.firebase.components.ComponentRegistrar" />
410-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2eb7072148c9b18ee694522fce2385fc\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
411            <meta-data
411-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b61fe4659edf31486af315a9283e7795\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:25:13-27:85
412                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
412-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b61fe4659edf31486af315a9283e7795\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:26:17-115
413                android:value="com.google.firebase.components.ComponentRegistrar" />
413-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b61fe4659edf31486af315a9283e7795\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:27:17-82
414        </service>
415
416        <provider
416-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\da3d785accc181b5d8cd59ecc7f8a711\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:21:9-29:20
417            android:name="com.canhub.cropper.CropFileProvider"
417-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\da3d785accc181b5d8cd59ecc7f8a711\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:22:13-63
418            android:authorities="studio.takasaki.clubm.cropper.fileprovider"
418-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\da3d785accc181b5d8cd59ecc7f8a711\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:23:13-72
419            android:exported="false"
419-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\da3d785accc181b5d8cd59ecc7f8a711\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:24:13-37
420            android:grantUriPermissions="true" >
420-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\da3d785accc181b5d8cd59ecc7f8a711\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:25:13-47
421            <meta-data
421-->[:react-native-webview] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:13-27:63
422                android:name="android.support.FILE_PROVIDER_PATHS"
422-->[:react-native-webview] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:17-67
423                android:resource="@xml/library_file_paths" />
423-->[:react-native-webview] C:\Users\<USER>\OneDrive\Documentos\Projeto\club-m-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:17-60
424        </provider>
425
426        <meta-data
426-->[com.github.bumptech.glide:okhttp3-integration:4.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e348df7ba08fc962cdad2072296ea0d\transformed\okhttp3-integration-4.11.0\AndroidManifest.xml:11:9-13:43
427            android:name="com.bumptech.glide.integration.okhttp3.OkHttpGlideModule"
427-->[com.github.bumptech.glide:okhttp3-integration:4.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e348df7ba08fc962cdad2072296ea0d\transformed\okhttp3-integration-4.11.0\AndroidManifest.xml:12:13-84
428            android:value="GlideModule" />
428-->[com.github.bumptech.glide:okhttp3-integration:4.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e348df7ba08fc962cdad2072296ea0d\transformed\okhttp3-integration-4.11.0\AndroidManifest.xml:13:13-40
429
430        <activity
430-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\22d1bdfca510dffa95f9466f4e112b1d\transformed\play-services-base-18.0.1\AndroidManifest.xml:20:9-22:45
431            android:name="com.google.android.gms.common.api.GoogleApiActivity"
431-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\22d1bdfca510dffa95f9466f4e112b1d\transformed\play-services-base-18.0.1\AndroidManifest.xml:20:19-85
432            android:exported="false"
432-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\22d1bdfca510dffa95f9466f4e112b1d\transformed\play-services-base-18.0.1\AndroidManifest.xml:22:19-43
433            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
433-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\22d1bdfca510dffa95f9466f4e112b1d\transformed\play-services-base-18.0.1\AndroidManifest.xml:21:19-78
434
435        <provider
435-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2eb7072148c9b18ee694522fce2385fc\transformed\firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
436            android:name="com.google.firebase.provider.FirebaseInitProvider"
436-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2eb7072148c9b18ee694522fce2385fc\transformed\firebase-common-21.0.0\AndroidManifest.xml:24:13-77
437            android:authorities="studio.takasaki.clubm.firebaseinitprovider"
437-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2eb7072148c9b18ee694522fce2385fc\transformed\firebase-common-21.0.0\AndroidManifest.xml:25:13-72
438            android:directBootAware="true"
438-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2eb7072148c9b18ee694522fce2385fc\transformed\firebase-common-21.0.0\AndroidManifest.xml:26:13-43
439            android:exported="false"
439-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2eb7072148c9b18ee694522fce2385fc\transformed\firebase-common-21.0.0\AndroidManifest.xml:27:13-37
440            android:initOrder="100" />
440-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2eb7072148c9b18ee694522fce2385fc\transformed\firebase-common-21.0.0\AndroidManifest.xml:28:13-36
441        <provider
441-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\34317f65008edeee7a1b865842f33bd5\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
442            android:name="androidx.startup.InitializationProvider"
442-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\34317f65008edeee7a1b865842f33bd5\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
443            android:authorities="studio.takasaki.clubm.androidx-startup"
443-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\34317f65008edeee7a1b865842f33bd5\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
444            android:exported="false" >
444-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\34317f65008edeee7a1b865842f33bd5\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
445            <meta-data
445-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\34317f65008edeee7a1b865842f33bd5\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
446                android:name="androidx.emoji2.text.EmojiCompatInitializer"
446-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\34317f65008edeee7a1b865842f33bd5\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
447                android:value="androidx.startup" />
447-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\34317f65008edeee7a1b865842f33bd5\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
448            <meta-data
448-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\9199512315edfb2a12b21ef60be4e11d\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
449                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
449-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\9199512315edfb2a12b21ef60be4e11d\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
450                android:value="androidx.startup" />
450-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\9199512315edfb2a12b21ef60be4e11d\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
451            <meta-data
451-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\157248c89b6348d2fd007effe30e715a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
452                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
452-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\157248c89b6348d2fd007effe30e715a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
453                android:value="androidx.startup" />
453-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\157248c89b6348d2fd007effe30e715a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
454        </provider>
455
456        <meta-data
456-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\03b3dfb7e6b29424b14ebc5db8bcef20\transformed\play-services-basement-18.3.0\AndroidManifest.xml:21:9-23:69
457            android:name="com.google.android.gms.version"
457-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\03b3dfb7e6b29424b14ebc5db8bcef20\transformed\play-services-basement-18.3.0\AndroidManifest.xml:22:13-58
458            android:value="@integer/google_play_services_version" />
458-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\03b3dfb7e6b29424b14ebc5db8bcef20\transformed\play-services-basement-18.3.0\AndroidManifest.xml:23:13-66
459
460        <receiver
460-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\157248c89b6348d2fd007effe30e715a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
461            android:name="androidx.profileinstaller.ProfileInstallReceiver"
461-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\157248c89b6348d2fd007effe30e715a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
462            android:directBootAware="false"
462-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\157248c89b6348d2fd007effe30e715a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
463            android:enabled="true"
463-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\157248c89b6348d2fd007effe30e715a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
464            android:exported="true"
464-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\157248c89b6348d2fd007effe30e715a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
465            android:permission="android.permission.DUMP" >
465-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\157248c89b6348d2fd007effe30e715a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
466            <intent-filter>
466-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\157248c89b6348d2fd007effe30e715a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
467                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
467-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\157248c89b6348d2fd007effe30e715a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
467-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\157248c89b6348d2fd007effe30e715a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
468            </intent-filter>
469            <intent-filter>
469-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\157248c89b6348d2fd007effe30e715a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
470                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
470-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\157248c89b6348d2fd007effe30e715a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
470-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\157248c89b6348d2fd007effe30e715a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
471            </intent-filter>
472            <intent-filter>
472-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\157248c89b6348d2fd007effe30e715a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
473                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
473-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\157248c89b6348d2fd007effe30e715a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
473-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\157248c89b6348d2fd007effe30e715a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
474            </intent-filter>
475            <intent-filter>
475-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\157248c89b6348d2fd007effe30e715a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
476                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
476-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\157248c89b6348d2fd007effe30e715a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
476-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\157248c89b6348d2fd007effe30e715a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
477            </intent-filter>
478        </receiver>
479
480        <service
480-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\c5d9a5a1fd2d11ffd7b923bf43e8631b\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
481            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
481-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\c5d9a5a1fd2d11ffd7b923bf43e8631b\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
482            android:exported="false" >
482-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\c5d9a5a1fd2d11ffd7b923bf43e8631b\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
483            <meta-data
483-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\c5d9a5a1fd2d11ffd7b923bf43e8631b\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
484                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
484-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\c5d9a5a1fd2d11ffd7b923bf43e8631b\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
485                android:value="cct" />
485-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\c5d9a5a1fd2d11ffd7b923bf43e8631b\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
486        </service>
487        <service
487-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\5dc4f5f8b0dc1ef7781f7127321a883b\transformed\transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
488            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
488-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\5dc4f5f8b0dc1ef7781f7127321a883b\transformed\transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
489            android:exported="false"
489-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\5dc4f5f8b0dc1ef7781f7127321a883b\transformed\transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
490            android:permission="android.permission.BIND_JOB_SERVICE" >
490-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\5dc4f5f8b0dc1ef7781f7127321a883b\transformed\transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
491        </service>
492
493        <receiver
493-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\5dc4f5f8b0dc1ef7781f7127321a883b\transformed\transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
494            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
494-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\5dc4f5f8b0dc1ef7781f7127321a883b\transformed\transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
495            android:exported="false" />
495-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\5dc4f5f8b0dc1ef7781f7127321a883b\transformed\transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
496    </application>
497
498</manifest>
