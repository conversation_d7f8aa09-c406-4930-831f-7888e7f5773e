import React from "react";
import {useTranslation} from "react-i18next";
import {View, Text, ScrollView} from "react-native";
import CategoryButton from "./category-button";
import BookIcon from "../icons/book-icon";
import GraduationIcon from "../icons/graduation-icon";
import HandshakeIcon from "../icons/handshake-icon";
import GroupIcon from "../icons/group-icon";
import StandIcon from "../icons/stand-icon";
import PenIcon from "../icons/pen-icon";
import styles from "../../styles/components/products/product-categories.style";
import {ProductCategory} from "@/models/product";

export interface ProductCategoriesProps {
  selectedCategory?: ProductCategory;
  onCategorySelect?: (category: ProductCategory) => void;
}

const ProductCategories: React.FC<ProductCategoriesProps> = ({
  selectedCategory,
  onCategorySelect
}) => {
  const {t} = useTranslation();

  return (
    <View style={styles.container}>
      <Text style={styles.title}>{t("products.categories.title")}</Text>
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.categoriesContainer}
        style={styles.scrollContainer}
      >
        <CategoryButton
          title={t("products.categories.ebooks")}
          icon={<BookIcon width={20} height={20} />}
          category={ProductCategory.Category0}
          isSelected={selectedCategory === ProductCategory.Category0}
          onPress={onCategorySelect}
        />
        <CategoryButton
          title={t("products.categories.courses")}
          icon={<GraduationIcon width={20} height={20} />}
          category={ProductCategory.Category1}
          isSelected={selectedCategory === ProductCategory.Category1}
          onPress={onCategorySelect}
        />
        <CategoryButton
          title={t("products.categories.meetings")}
          icon={<HandshakeIcon width={20} height={20} />}
          category={ProductCategory.Category2}
          isSelected={selectedCategory === ProductCategory.Category2}
          onPress={onCategorySelect}
        />
        <CategoryButton
          title={t("products.categories.groupMeetings")}
          icon={<GroupIcon width={20} height={20} />}
          category={ProductCategory.Category3}
          isSelected={selectedCategory === ProductCategory.Category3}
          onPress={onCategorySelect}
        />
        <CategoryButton
          title={t("products.categories.mentoring")}
          icon={<StandIcon width={20} height={20} />}
          category={ProductCategory.Category4}
          isSelected={selectedCategory === ProductCategory.Category4}
          onPress={onCategorySelect}
        />
        <CategoryButton
          title={t("products.categories.services")}
          icon={<PenIcon width={20} height={20} />}
          category={ProductCategory.Category5}
          isSelected={selectedCategory === ProductCategory.Category5}
          onPress={onCategorySelect}
        />
      </ScrollView>
    </View>
  );
};

export default React.memo(ProductCategories);
