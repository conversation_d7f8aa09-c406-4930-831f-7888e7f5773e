import {
  useMutation,
  useQuery,
  useQueryClient,
  UseQueryOptions
} from "@tanstack/react-query";
import {useTranslation} from "react-i18next";
import {showErrorToast} from "@/utils/error-handler";

import {
  CreateIndicationRequest,
  IndicationsListParams
} from "@/models/api/indications.models";
import {IndicationsService} from "@/services/api/indications.service";

/**
 * Hook para listar indicações
 */
export function useIndications(
  params?: IndicationsListParams,
  options?: UseQueryOptions<any>
) {
  return useQuery({
    queryKey: ["indications", "list", params],
    queryFn: () => IndicationsService.getIndications(params),
    select: (response) => {
      console.log("🔍 [USE-INDICATIONS] Response completa:", response);
      console.log("🔍 [USE-INDICATIONS] Response.data:", response.data);
      return response.data;
    },
    staleTime: 5 * 60 * 1000, // 5 minutos
    ...options
  });
}

/**
 * Hook para buscar uma indicação específica
 */
export function useIndication(id: string) {
  return useQuery({
    queryKey: ["indications", "detail", id],
    queryFn: () => IndicationsService.getIndicationById(id),
    select: (response) => response.data,
    enabled: !!id,
    staleTime: 5 * 60 * 1000 // 5 minutos
  });
}

/**
 * Hook para criar uma nova indicação
 */
export function useCreateIndication() {
  const queryClient = useQueryClient();
  const {t} = useTranslation();

  return useMutation({
    mutationFn: (data: CreateIndicationRequest) =>
      IndicationsService.createIndication(data),
    onSuccess: (response) => {
      if (response.success) {
        // Invalida as queries de indicações para recarregar a lista
        queryClient.invalidateQueries({
          queryKey: ["indications"]
        });

        console.log(
          "✅ [INDICATIONS] Indicação criada com sucesso:",
          response.data
        );
      } else {
        console.error("❌ [INDICATIONS] Erro na resposta:", response.message);
        showErrorToast(
          new Error(response.message || "Erro"),
          t("indications.error.generic", "Erro ao criar indicação")
        );
      }
    },
    onError: (error: any) => {
      console.error("❌ [INDICATIONS] Erro ao criar indicação:", error);
      showErrorToast(
        error,
        t("indications.error.generic", "Erro ao criar indicação")
      );
    }
  });
}

/**
 * Hook para buscar estatísticas de indicações
 */
export function useIndicationStats(options?: UseQueryOptions<any>) {
  return useQuery({
    queryKey: ["indications", "stats"],
    queryFn: () => IndicationsService.getIndicationStats(),
    select: (response) => response.data,
    staleTime: 5 * 60 * 1000, // 5 minutos
    ...options
  });
}

/**
 * Hook para invalidar cache de indicações
 */
export function useInvalidateIndications() {
  const queryClient = useQueryClient();

  return () => {
    queryClient.invalidateQueries({
      queryKey: ["indications"]
    });
  };
}
