import React from "react";
import {View, Text} from "react-native";
import Svg, {Path} from "react-native-svg";
import styles from "@/styles/registration/registration-status.style";

export interface TimelineItem {
  date: string;
  time: string;
  status: string;
  description: string;
  isActive?: boolean;
  isCompleted?: boolean;
}

export interface RegistrationStatusTimelineProps {
  items: TimelineItem[];
  currentStatus: "queue" | "analysis" | "approved";
}

const TimelineDot: React.FC<{isActive?: boolean; isCompleted?: boolean}> = ({
  isActive,
  isCompleted
}) => {
  if (isCompleted) {
    return (
      <Svg width={12} height={12} viewBox="0 0 12 12" fill="none">
        <Path d="M6 12A6 6 0 1 0 6 0a6 6 0 0 0 0 12Z" fill="#17B169" />
      </Svg>
    );
  } else if (isActive) {
    return (
      <Svg width={12} height={12} viewBox="0 0 12 12" fill="none">
        <Path d="M6 12A6 6 0 1 0 6 0a6 6 0 0 0 0 12Z" fill="#E36A1B" />
      </Svg>
    );
  } else {
    // Inactive - outline only
    return (
      <Svg width={12} height={12} viewBox="0 0 12 12" fill="none">
        <Path
          d="M6 11A5 5 0 1 0 6 1a5 5 0 0 0 0 10Z"
          stroke="#EAECF0"
          strokeWidth={2}
          fill="none"
        />
      </Svg>
    );
  }
};

const TimelineLine: React.FC<{
  height: number;
  isActive?: boolean;
  isCompleted?: boolean;
}> = ({height, isActive, isCompleted}) => {
  let color = "#EAECF0"; // Default inactive color

  if (isCompleted) {
    color = "#17B169"; // Green for completed
  } else if (isActive) {
    color = "#E36A1B"; // Orange for active
  }

  return (
    <View
      style={{
        width: 2,
        height,
        backgroundColor: color,
        marginLeft: 5,
        marginRight: 5
      }}
    />
  );
};

const RegistrationStatusTimeline: React.FC<RegistrationStatusTimelineProps> = ({
  items,
  currentStatus
}) => {
  const currentIndex =
    currentStatus === "queue" ? 0 : currentStatus === "analysis" ? 1 : 2;

  const normalized = items.map((it, idx) => ({
    ...it,
    isActive: it.isActive ?? idx === currentIndex,
    isCompleted: it.isCompleted ?? idx < currentIndex
  }));

  return (
    <View style={styles.timelineContainer}>
      {normalized.map((item, idx) => {
        const isLast = idx === normalized.length - 1;
        const lineHeight = idx === 0 ? 83 : 64;
        const titleStyle = item.isCompleted
          ? styles.statusTitleCompleted
          : item.isActive
          ? styles.statusTitleActive
          : styles.statusTitleInactive;
        const descStyle = item.isCompleted
          ? styles.statusDescriptionCompleted
          : item.isActive
          ? styles.statusDescriptionActive
          : styles.statusDescriptionInactive;

        const Container = idx === 0 ? View : (View as any);
        const containerStyle =
          idx === 0 ? styles.timelineItem : styles.timelineItemWithMargin;

        return (
          <Container key={`${item.status}-${idx}`} style={containerStyle}>
            <View style={styles.timelineLeft}>
              {!!item.date && (
                <Text style={styles.timelineDate}>{item.date}</Text>
              )}
              {!!item.time && (
                <Text style={styles.timelineTime}>{item.time}</Text>
              )}
            </View>

            <View style={styles.timelineCenter}>
              <TimelineDot
                isActive={item.isActive}
                isCompleted={item.isCompleted}
              />
              {!isLast && (
                <TimelineLine
                  height={lineHeight}
                  isActive={normalized[idx + 1]?.isActive}
                  isCompleted={normalized[idx + 1]?.isCompleted}
                />
              )}
            </View>

            <View style={styles.timelineRight}>
              <Text style={[styles.statusTitle, titleStyle]}>
                {item.status}
              </Text>
              {!!item.description && (
                <Text style={[styles.statusDescription, descStyle]}>
                  {item.description}
                </Text>
              )}
            </View>
          </Container>
        );
      })}
    </View>
  );
};

export default RegistrationStatusTimeline;
