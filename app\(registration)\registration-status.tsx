import React, {useCallback} from "react";
import {
  Text,
  View,
  TouchableOpacity,
  ScrollView,
  RefreshControl
} from "react-native";
import {useTranslation} from "react-i18next";
import {useRouter} from "expo-router";
import AlertCircleIcon from "../../components/icons/alert-circle-icon";
import AnalysisIcon from "../../components/icons/analysis-icon";
import CheckCircleIcon from "../../components/icons/check-circle-icon";
import RegistrationStatusTimeline, {
  TimelineItem
} from "../../components/registration-status-timeline";
import styles from "@/styles/registration/registration-status.style";

import {useRegistrationAnalysis} from "@/contexts/registration-analysis-context";
import {useCurrentUser as useAppCurrentUser} from "@/hooks/api/use-users";

export interface RegistrationStatusProps {
  status?: "queue" | "analysis" | "approved";
}

const RegistrationStatus: React.FC<RegistrationStatusProps> = ({
  status = "queue"
}) => {
  const {t} = useTranslation();
  const router = useRouter();
  const {isUnderAnalysis, deactivate} = useRegistrationAnalysis();
  const {
    data: user,
    refetch,
    isFetching
  } = useAppCurrentUser({staleTime: 0, gcTime: 0, retry: false});

  // Derive status from API when possible
  const approvedAt = (user as any)?.approvedAt as string | undefined;
  const underAnalysisAt = (user as any)?.underAnalysisAt as string | undefined;

  let currentStatus: "queue" | "analysis" | "approved" = status;
  if (approvedAt) {
    currentStatus = "approved";
  } else if (underAnalysisAt) {
    currentStatus = "analysis";
  } else if (isUnderAnalysis) {
    currentStatus = "queue";
  }

  const fmtDate = (iso?: string) =>
    iso ? new Date(iso).toLocaleDateString("pt-BR") : "";
  const fmtTime = (iso?: string) =>
    iso
      ? new Date(iso).toLocaleTimeString("pt-BR", {
          hour: "2-digit",
          minute: "2-digit"
        })
      : "";

  const getStatusConfig = () => {
    switch (currentStatus) {
      case "queue":
        return {
          icon: <AlertCircleIcon />,
          title: t(
            "registrationStatus.queueTitle",
            "Sua solicitação já está na fila de análise"
          ),
          description: t(
            "registrationStatus.queueDescription",
            "Você será notificado assim que avançar para a próxima etapa."
          ),
          timelineItems: [
            {
              date: new Date().toLocaleDateString("pt-BR"),
              time: new Date().toLocaleTimeString("pt-BR", {
                hour: "2-digit",
                minute: "2-digit"
              }),
              status: t("registrationStatus.queue", "Na fila"),
              description: t(
                "registrationStatus.queueDesc",
                "Seu pedido de associação foi recebido e está aguardando para ser analisado."
              ),
              isActive: true
            },
            {
              date: "",
              time: "",
              status: t("registrationStatus.analysis", "Em análise"),
              description: "",
              isActive: false
            },
            {
              date: "",
              time: "",
              status: t("registrationStatus.approval", "Aprovação"),
              description: "",
              isActive: false
            }
          ] as TimelineItem[]
        };
      case "analysis":
        return {
          icon: <AnalysisIcon />,
          title: t(
            "registrationStatus.analysisTitle",
            "A análise do seu perfil já está em andamento"
          ),
          description: t(
            "registrationStatus.analysisDescription",
            "Esse processo pode levar alguns dias. Fique atento ao e-mail e notificações."
          ),
          timelineItems: [
            {
              date: "",
              time: "",
              status: t("registrationStatus.queue", "Na fila"),
              description: t(
                "registrationStatus.queueDesc",
                "Seu pedido de associação foi recebido e em breve será analisado."
              )
            },
            {
              date: fmtDate(underAnalysisAt),
              time: fmtTime(underAnalysisAt),
              status: t("registrationStatus.analysis", "Em análise"),
              description: t(
                "registrationStatus.analysisDesc",
                "Nossa equipe já está verificando suas informações."
              )
            },
            {
              date: fmtDate(approvedAt),
              time: fmtTime(approvedAt),
              status: t("registrationStatus.approval", "Aprovação"),
              description: ""
            }
          ] as TimelineItem[]
        };
      case "approved":
        return {
          icon: <CheckCircleIcon />,
          title: t(
            "registrationStatus.approvedTitle",
            "Bem-vindo ao Clube, seu cadastro foi aprovado!"
          ),
          description: t(
            "registrationStatus.approvedDescription",
            "Agora você tem acesso exclusivo aos recursos e benefícios do nosso app, aproveite."
          ),
          timelineItems: [
            {
              date: "",
              time: "",
              status: t("registrationStatus.queue", "Na fila"),
              description: t(
                "registrationStatus.queueDesc",
                "Seu pedido de associação foi recebido e em breve será analisado."
              )
            },
            {
              date: fmtDate(underAnalysisAt),
              time: fmtTime(underAnalysisAt),
              status: t("registrationStatus.analysis", "Em análise"),
              description: t(
                "registrationStatus.analysisDesc",
                "Nossa equipe já está verificando suas informações."
              )
            },
            {
              date: fmtDate(approvedAt),
              time: fmtTime(approvedAt),
              status: t("registrationStatus.approval", "Aprovação"),
              description: t(
                "registrationStatus.approvedDesc",
                "Parabéns! Agora você é um membro oficial Club M."
              )
            }
          ] as TimelineItem[]
        };
    }
  };

  const statusConfig = getStatusConfig();
  const isApproved = currentStatus === "approved";

  const handleGoToLogin = async () => {
    if (!isApproved) return;
    try {
      await deactivate();
    } catch {}
    router.replace("/(auth)/login");
  };

  return (
    <View style={styles.mainContainer}>
      <View style={styles.statusBar} />

      <ScrollView
        contentContainerStyle={styles.contentContainer}
        refreshControl={
          <RefreshControl
            refreshing={!!isFetching}
            onRefresh={() => refetch()}
          />
        }
      >
        <View style={styles.statusCard}>
          <View style={styles.iconContainer}>{statusConfig.icon}</View>

          <Text style={styles.titleText}>{statusConfig.title}</Text>

          <Text style={styles.descriptionText}>{statusConfig.description}</Text>

          <View style={styles.divider} />

          <Text style={styles.statusUpdatesTitle}>
            {t("registrationStatus.statusUpdates", "Atualizações de status")}
          </Text>

          <RegistrationStatusTimeline
            items={statusConfig.timelineItems}
            currentStatus={currentStatus}
          />
        </View>

        <View style={{marginTop: 40}} />

        <TouchableOpacity
          style={[styles.primaryButton, {opacity: isApproved ? 1 : 0.5}]}
          disabled={!isApproved}
          onPress={handleGoToLogin}
        >
          <Text style={styles.primaryButtonText}>
            {t("registrationStatus.goToLogin", "Ir para login")}
          </Text>
        </TouchableOpacity>
      </ScrollView>

      <View style={styles.homeIndicator} />
    </View>
  );
};

export default RegistrationStatus;
