import {useEffect, useMemo, useState} from "react";
import {capitalize} from "../utils/string";
import {getCurrentLocalization} from "../utils/i18n";

function useMonth(month: number) {
  const [locale, setLocale] = useState<string>();

  useEffect(() => {
    getCurrentLocalization().then((locale) => setLocale(locale));
  }, []);

  return useMemo(() => {
    const formatedMonth = new Intl.DateTimeFormat("pt-BR", {
      month: "long"
    }).format(new Date(2025, month - 1, 1));

    return capitalize(formatedMonth);
  }, [month, locale]);
}

export default useMonth;
