import React, {useState, useCallback} from "react";
import {Text, View, TouchableOpacity, ScrollView} from "react-native";
import Screen from "@/components/screen";
import BackButton from "@/components/back-button";

import PaymentInputField from "@/components/payment-input-field";
import PaymentCheckbox from "@/components/payment-checkbox";
import InstallmentSelector from "@/components/installment-selector";
import {useTitleInstallmentOptions} from "@/hooks/api/use-titles";
import {usePlansPublic} from "@/hooks/api";
import UserEditIcon from "@/components/icons/user-edit-icon";
import CreditCardIcon from "@/components/icons/credit-card-icon";
import CreditCardShieldIcon from "@/components/icons/credit-card-shield-icon";
import SwitchHorizontalIcon from "@/components/icons/switch-horizontal-icon";
import styles from "@/styles/registration/payment-card-registration.style";
import {usePaymentCardValidation} from "@/hooks/use-registration-validation";
import {useRouter, useLocalSearchParams} from "expo-router";
import {useCreatePayment} from "@/hooks/api/use-payments";
import {
  PaymentEntity,
  PaymentType,
  CreatePaymentRequest
} from "@/models/api/payments.models";
import {
  useCreateSubscription,
  CreateSubscriptionRequest
} from "@/hooks/api/use-subscriptions";
import {useCurrentUser} from "@/hooks/api/use-auth";

const PaymentCardRegistration: React.FC = () => {
  const router = useRouter();
  const params = useLocalSearchParams();
  const [selectedInstallments, setSelectedInstallments] = useState<number>(1);

  // Hooks de backend
  const {data: currentUser} = useCurrentUser();
  const createPaymentMutation = useCreatePayment();
  const createSubscriptionMutation = useCreateSubscription();

  // Fetch plans list and filter to get authoritative monthly price
  const planId = params.selectedPlanId
    ? parseInt(params.selectedPlanId as string)
    : 0;
  const {data: plansResponse} = usePlansPublic({page: 1, pageSize: 50});
  const planPrice = React.useMemo(() => {
    const plan = plansResponse?.data?.find((p) => p.id === planId);
    return typeof plan?.value === "number" ? plan.value : 0;
  }, [plansResponse?.data, planId]);

  // Helper function to format currency
  const formatCurrency = useCallback((value: number) => {
    return `R$ ${value.toFixed(2).replace(".", ",")}`;
  }, []);

  // Fetch title installment options for authoritative values
  const {data: titleOptions} = useTitleInstallmentOptions(true);

  const parsedSelectedOption = React.useMemo(() => {
    if (!titleOptions || titleOptions.length === 0) return null;
    // pick the server option matching current selectedInstallments, fallback to first
    return (
      titleOptions.find((o) => o.installments === selectedInstallments) ||
      titleOptions[0]
    );
  }, [titleOptions, selectedInstallments]);

  const currentInstallmentValue = React.useMemo(() => {
    if (!parsedSelectedOption) return 0;
    return parsedSelectedOption.value / 100;
  }, [parsedSelectedOption]);

  const currentTotalValue = React.useMemo(() => {
    const titleTotal = parsedSelectedOption
      ? parsedSelectedOption.total / 100
      : 0;
    const membershipMonthly = (planPrice || 0) / 100;
    return titleTotal + membershipMonthly;
  }, [parsedSelectedOption, planPrice]);

  // Initialize validation
  const validation = usePaymentCardValidation(
    {
      adhesionCardName: "",
      adhesionCardNumber: "",
      adhesionInstallments: "1", // Initialize with default value
      adhesionExpiryDate: "",
      adhesionCvv: "",
      membershipCardName: "",
      membershipCardNumber: "",
      membershipExpiryDate: "",
      membershipCvv: "",
      useSameCard: false
    },

    async () => {
      // 1) Create Title payment with card #1 (Adesão)
      const {data: currentUserData} = {data: currentUser};
      if (!currentUserData) {
        // Rely on existing flows to have a user; if missing, just stop silently
        return;
      }

      const installments = selectedInstallments || 1;

      const [adhMonth, adhYear] = (
        validation.data.adhesionExpiryDate || ""
      ).split("/");
      const [membMonth, membYear] = (
        validation.data.membershipExpiryDate || ""
      ).split("/");

      // Fallbacks to keep robust if optional fields aren't provided
      const cpfCnpj = (params.document as string) || "";
      const phoneNumber = (params.phoneNumber as string) || "";
      const postalCode = (params.addressZip as string) || "";
      const addressNumber = (params.addressNumber as string) || "";
      const addressComplement = (params.addressComplement as string) || "";

      const titlePayment: CreatePaymentRequest = {
        entity: PaymentEntity.Title,
        entityId: Number(currentUserData.id),
        type: PaymentType.CreditCard,
        creditCard: {
          installmentCount: installments,
          creditCard: {
            holderName: validation.data.adhesionCardName,
            number: validation.data.adhesionCardNumber.replace(/\s/g, ""),
            expiryMonth: adhMonth || "",
            expiryYear:
              (adhYear?.length === 2 ? `20${adhYear}` : adhYear) || "",
            ccv: validation.data.adhesionCvv,
            name: validation.data.adhesionCardName,
            cpfCnpj,
            postalCode,
            addressNumber,
            addressComplement,
            phoneNumber
          }
        }
      };

      await createPaymentMutation.mutateAsync(titlePayment);

      // 2) Create Plan subscription with selected due day and membership card (or same card)
      const parsedDay = parseInt((params.selectedDueDay as string) || "");
      const selectedDay = [10, 20, 30].includes(parsedDay) ? parsedDay : 10;

      const usingSameCard = validation.data.useSameCard;

      // Precompute expiry year values to avoid nested ternaries flagged by linter
      const adhYearFull =
        adhYear?.length === 2 ? `20${adhYear}` : adhYear || "";
      const membYearFull =
        membYear?.length === 2 ? `20${membYear}` : membYear || "";

      const subscriptionRequest: CreateSubscriptionRequest = {
        entity: 1, // Plan
        entityId: parseInt(params.selectedPlanId as string) || 0,
        paymentType: PaymentType.CreditCard,
        creditCard: {
          holderName: usingSameCard
            ? validation.data.adhesionCardName
            : validation.data.membershipCardName,
          number: (usingSameCard
            ? validation.data.adhesionCardNumber
            : validation.data.membershipCardNumber
          ).replace(/\s/g, ""),
          expiryMonth: usingSameCard ? adhMonth || "" : membMonth || "",
          expiryYear: usingSameCard ? adhYearFull : membYearFull,
          ccv: usingSameCard
            ? validation.data.adhesionCvv
            : validation.data.membershipCvv,
          name: usingSameCard
            ? validation.data.adhesionCardName
            : validation.data.membershipCardName,
          cpfCnpj,
          postalCode,
          addressNumber,
          addressComplement,
          phoneNumber
        },
        day: selectedDay
      };

      await createSubscriptionMutation.mutateAsync(subscriptionRequest);

      // 3) Navigate to success
      router.push({
        pathname: "/(registration)/registration-confirmation",
        params: {...params}
      });
    }
  );
  // Live-sync: while useSameCard is true, mirror adhesion fields into membership
  React.useEffect(() => {
    if (!validation.data.useSameCard) return;

    validation.updateData({
      membershipCardName: validation.data.adhesionCardName,
      membershipCardNumber: validation.data.adhesionCardNumber,
      membershipExpiryDate: validation.data.adhesionExpiryDate,
      membershipCvv: validation.data.adhesionCvv
    });
  }, [
    validation.data.useSameCard,
    validation.data.adhesionCardName,
    validation.data.adhesionCardNumber,
    validation.data.adhesionExpiryDate,
    validation.data.adhesionCvv
  ]);

  const handleInputChange = useCallback(
    (field: keyof typeof validation.data) => (value: string) => {
      let processedValue = value;
      const fieldName = String(field);

      if (fieldName.includes("CardNumber")) {
        processedValue = value
          .replace(/\D/g, "")
          .slice(0, 16)
          .replace(/(\d{4})(?=\d)/g, "$1 ");
      }

      if (fieldName.includes("ExpiryDate")) {
        processedValue = value
          .replace(/\D/g, "")
          .replace(/(\d{2})(\d{2})/, "$1/$2");
      }

      if (fieldName.includes("Cvv")) {
        processedValue = value.replace(/\D/g, "").slice(0, 4);
      }

      validation.updateField(field, processedValue);
    },
    [validation]
  );

  const handleCheckboxChange = useCallback(() => {
    const newValue = !validation.data.useSameCard;
    validation.updateField("useSameCard", newValue);

    if (newValue) {
      validation.updateData({
        membershipCardName: validation.data.adhesionCardName,
        membershipCardNumber: validation.data.adhesionCardNumber,
        membershipExpiryDate: validation.data.adhesionExpiryDate,
        membershipCvv: validation.data.adhesionCvv
      });
    }
  }, [validation]);

  const handleInstallmentSelect = useCallback(
    (installments: number) => {
      setSelectedInstallments(installments);

      validation.updateField("adhesionInstallments", installments.toString());
    },
    [validation]
  );

  const handlePaymentPress = useCallback(async () => {
    // Use async handler to await API calls and prevent double submit
    const success = await validation.handleSubmitAsync();
    if (!success) {
      // You may show a toast or alert here using your existing UI system
    }
  }, [validation]);

  const handleAlternativePaymentPress = useCallback(() => {
    router.push({
      pathname: "/(business)/payment-selection",
      params: {
        selectedPlanId: params.selectedPlanId,
        returnTo: "registration"
      }
    });
  }, [router, params]);

  return (
    <Screen disableScrollView>
      <ScrollView
        style={styles.container}
        contentContainerStyle={{flexGrow: 1}}
        keyboardShouldPersistTaps="handled"
      >
        <View style={styles.contentContainer}>
          {/* Header */}
          <View style={styles.headerContainer}>
            <BackButton style={styles.backButton} />
            <Text style={styles.headerTitle}>Dados de pagamento</Text>
            <View style={styles.backButton} />
          </View>

          {/* Adhesion Price */}
          <View style={styles.priceContainer}>
            <Text style={styles.priceLabel}>Título de adesão</Text>
            <Text style={styles.priceValue}>
              {formatCurrency(currentInstallmentValue * selectedInstallments)}
            </Text>
          </View>

          {/* Adhesion Payment Form */}
          <View style={styles.inputContainer}>
            <PaymentInputField
              placeholder="Nome do titular"
              value={validation.data.adhesionCardName}
              onChangeText={handleInputChange("adhesionCardName")}
              icon={<UserEditIcon />}
            />
            {!!validation.errors.adhesionCardName && (
              <Text style={styles.errorText}>
                {validation.errors.adhesionCardName}
              </Text>
            )}
          </View>

          <View style={styles.inputContainer}>
            <PaymentInputField
              placeholder="Número do cartão"
              value={validation.data.adhesionCardNumber}
              onChangeText={handleInputChange("adhesionCardNumber")}
              icon={<CreditCardIcon />}
              inputMode="numeric"
              maxLength={19} // 16 digits + 3 spaces
            />
            {!!validation.errors.adhesionCardNumber && (
              <Text style={styles.errorText}>
                {validation.errors.adhesionCardNumber}
              </Text>
            )}
          </View>

          <View style={styles.inputContainer}>
            <InstallmentSelector
              selectedInstallments={selectedInstallments}
              onInstallmentSelect={handleInstallmentSelect}
              planValue={planPrice / 100}
              maxInstallments={12}
              customOptions={(titleOptions || []).map((o) => ({
                id: String(o.installments),
                installments: o.installments,
                amount: `R$ ${(o.value / 100).toFixed(2).replace(".", ",")}`,
                label:
                  o.installments === 1
                    ? "À vista"
                    : o.withFee
                    ? "Com juros"
                    : "Sem juros"
              }))}
            />
          </View>

          <View style={styles.inputRow}>
            <View style={styles.inputHalf}>
              <PaymentInputField
                placeholder="Expira em"
                value={validation.data.adhesionExpiryDate}
                onChangeText={handleInputChange("adhesionExpiryDate")}
                icon={<CreditCardIcon />}
                inputMode="numeric"
                maxLength={5} // MM/AA format
              />
              {!!validation.errors.adhesionExpiryDate && (
                <Text style={styles.errorText}>
                  {validation.errors.adhesionExpiryDate}
                </Text>
              )}
            </View>
            <View style={styles.inputHalf}>
              <PaymentInputField
                placeholder="CVV"
                value={validation.data.adhesionCvv}
                onChangeText={handleInputChange("adhesionCvv")}
                icon={<CreditCardShieldIcon />}
                inputMode="numeric"
                maxLength={4}
              />
              {!!validation.errors.adhesionCvv && (
                <Text style={styles.errorText}>
                  {validation.errors.adhesionCvv}
                </Text>
              )}
            </View>
          </View>

          {/* Membership Price */}
          <View style={styles.membershipContainer}>
            <Text style={styles.membershipLabel}>Mensalidade de membro</Text>
            <View style={styles.membershipPrice}>
              <Text>
                <Text style={styles.membershipPriceMain}>
                  {formatCurrency(planPrice / 100)}
                </Text>
                <Text style={styles.membershipPriceSuffix}>/mês</Text>
              </Text>
            </View>
          </View>

          {/* Membership Payment Form */}
          <View style={styles.inputContainer}>
            <PaymentInputField
              placeholder="Nome do titular"
              value={validation.data.membershipCardName}
              onChangeText={handleInputChange("membershipCardName")}
              icon={<UserEditIcon />}
              editable={!validation.data.useSameCard}
            />
            {!!validation.errors.membershipCardName && (
              <Text style={styles.errorText}>
                {validation.errors.membershipCardName}
              </Text>
            )}
          </View>

          <View style={styles.inputContainer}>
            <PaymentInputField
              placeholder="Número do cartão"
              value={validation.data.membershipCardNumber}
              onChangeText={handleInputChange("membershipCardNumber")}
              icon={<CreditCardIcon />}
              inputMode="numeric"
              maxLength={19} // 16 digits + 3 spaces
              editable={!validation.data.useSameCard}
            />
            {!!validation.errors.membershipCardNumber && (
              <Text style={styles.errorText}>
                {validation.errors.membershipCardNumber}
              </Text>
            )}
          </View>

          <View style={styles.inputRow}>
            <View style={styles.inputHalf}>
              <PaymentInputField
                placeholder="Expira em"
                value={validation.data.membershipExpiryDate}
                onChangeText={handleInputChange("membershipExpiryDate")}
                icon={<CreditCardIcon />}
                inputMode="numeric"
                maxLength={5} // MM/AA format
                editable={!validation.data.useSameCard}
              />
              {!!validation.errors.membershipExpiryDate && (
                <Text style={styles.errorText}>
                  {validation.errors.membershipExpiryDate}
                </Text>
              )}
            </View>
            <View style={styles.inputHalf}>
              <PaymentInputField
                placeholder="CVV"
                value={validation.data.membershipCvv}
                onChangeText={handleInputChange("membershipCvv")}
                icon={<CreditCardShieldIcon />}
                inputMode="numeric"
                maxLength={4}
                editable={!validation.data.useSameCard}
              />
              {!!validation.errors.membershipCvv && (
                <Text style={styles.errorText}>
                  {validation.errors.membershipCvv}
                </Text>
              )}
            </View>
          </View>

          {/* Checkbox */}
          <View style={styles.checkboxContainer}>
            <PaymentCheckbox
              checked={validation.data.useSameCard}
              onPress={handleCheckboxChange}
              text="Usar mesmo cartão de pagamento de adesão para preencher as informações de pagamento de mensalidade de membro."
            />
          </View>
        </View>

        {/* Bottom Container - Outside content container for proper positioning */}
        <View style={styles.bottomContainer}>
          <View style={styles.totalContainer}>
            <Text style={styles.totalLabel}>Valor total</Text>
            <Text style={styles.totalValue}>
              {formatCurrency(currentTotalValue)}
            </Text>
          </View>

          <TouchableOpacity
            style={[
              styles.paymentButton,
              validation.isSubmitting && {opacity: 0.6}
            ]}
            onPress={validation.isSubmitting ? undefined : handlePaymentPress}
            activeOpacity={0.7}
            disabled={validation.isSubmitting}
          >
            <Text style={styles.paymentButtonText}>
              {validation.isSubmitting ? "Processando..." : "Efetuar pagamento"}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.alternativePaymentContainer}
            onPress={handleAlternativePaymentPress}
            activeOpacity={0.7}
          >
            <SwitchHorizontalIcon />
            <Text style={styles.alternativePaymentText}>
              Selecionar outro método de pagamento
            </Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </Screen>
  );
};

export default PaymentCardRegistration;
