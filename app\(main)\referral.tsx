import React, {useState, useCallback} from "react";
import {
  Text,
  View,
  ImageBackground,
  ScrollView,
  TouchableOpacity
} from "react-native";
import Screen from "../../components/screen";
import {useTranslation} from "react-i18next";
import FullSizeButton from "../../components/full-size-button";
import InvisibleFullSizeButton from "../../components/invisible-full-size-button";
import InviteFriendsModal from "../../components/modals/invite-friends-modal";
import InvitedContactsList from "../../components/invited-contacts-list";
import ReferralStats from "../../components/referral-stats";
import styles from "@/styles/main/referral.style";
import UsersPlusIcon from "../../components/icons/users-plus-icon";
import ChevronLeftIcon from "../../components/icons/chevron-left-icon";
import {useRouter} from "expo-router";
import {useUpsellDrawer} from "@/hooks/use-upsell-drawer";
import UpsellDrawer from "@/components/modals/upsell-drawer";

const Referral: React.FC = () => {
  const {t} = useTranslation();
  const router = useRouter();
  const [showInviteModal, setShowInviteModal] = useState(false);
  const [showContactsList, setShowContactsList] = useState(false);
  const {
    isVisible: isUpsellDrawerVisible,
    config: upsellConfig,
    interceptGuestAction,
    hideUpsellDrawer
  } = useUpsellDrawer();

  const handleBackPress = useCallback(() => {
    router.back();
  }, [router]);

  const handleInviteFriends = useCallback(() => {
    // Interceptar ação para usuários guest e customizar o texto do drawer para convidados nesta tela
    const wasIntercepted = interceptGuestAction(
      () => {
        setShowInviteModal(true);
      },
      {
        title: "Quase lá!",
        description:
          "Faça seu cadastro, compartilhe oportunidades e acompanhe quem você convidou diretamente pelo aplicativo."
      }
    );

    if (wasIntercepted) {
      console.log(
        "🚫 [REFERRAL] Convite de amigos interceptado para usuário guest"
      );
    }
  }, [interceptGuestAction]);

  const handleViewContactsList = useCallback(() => {
    setShowContactsList(true);
  }, []);

  const handleCloseModal = useCallback(() => {
    setShowInviteModal(false);
  }, []);

  const handleCloseContactsList = useCallback(() => {
    setShowContactsList(false);
  }, []);

  if (showContactsList) {
    return <InvitedContactsList onClose={handleCloseContactsList} />;
  }

  return (
    <>
      <Screen>
        <ImageBackground
          source={require("../../assets/images/referral-background.png")}
          style={styles.backgroundImage}
          imageStyle={styles.backgroundImageStyle}
        >
          <View style={styles.overlay} />
          <View style={styles.container}>
            <View style={styles.topSection}>
              <TouchableOpacity
                style={styles.backButton}
                onPress={handleBackPress}
              >
                <ChevronLeftIcon />
              </TouchableOpacity>
              <View style={styles.header}>
                <Text style={styles.headerText}>Indique o app</Text>
              </View>
            </View>
            <ScrollView
              style={styles.content}
              contentContainerStyle={styles.contentContainer}
              showsVerticalScrollIndicator={false}
            >
              <Text style={styles.title}>
                {t("referral.title", "Ganhe prêmios e descontos exclusivos!")}
              </Text>
              <Text style={styles.subtitle}>
                {t(
                  "referral.subtitle",
                  "Indique um amigo para o clube e concorra a prêmios e ganhe até 20% OFF na sua próxima compra ou assinatura no app."
                )}
              </Text>
              <Text style={styles.description}>
                {t(
                  "referral.description",
                  "É só pronto pra expandir seu networking?"
                )}
              </Text>

              <ReferralStats compact />
            </ScrollView>
            <View style={styles.buttonContainer}>
              <FullSizeButton
                text={t("referral.inviteFriends", "Convidar amigos")}
                onPress={handleInviteFriends}
                icon={<UsersPlusIcon />}
              />
              <InvisibleFullSizeButton
                text={t("referral.viewContactsList", "Ver lista de convidados")}
                onPress={handleViewContactsList}
              />
            </View>
          </View>
        </ImageBackground>
      </Screen>

      {showInviteModal && <InviteFriendsModal onClose={handleCloseModal} />}

      {/* Upsell Drawer for Guest Users */}
      <UpsellDrawer
        visible={isUpsellDrawerVisible}
        onClose={hideUpsellDrawer}
        title={upsellConfig.title}
        description={upsellConfig.description}
      />
    </>
  );
};

export default Referral;
