import {StyleSheet} from "react-native";
import stylesConstants from "../styles-constants";

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#1E293B"
  },
  contentContainer: {
    flex: 1,
    paddingHorizontal: 24,
    paddingTop: 60,
    paddingBottom: 20
  },
  headerContainer: {
    flexDirection: "row" as const,
    alignItems: "center" as const,
    justifyContent: "space-between" as const,
    marginBottom: 32,
    gap: 60
  },
  backButton: {
    width: 24,
    height: 24
  },
  headerTitle: {
    color: "#DFE9F0",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "600" as const,
    lineHeight: 20,
    textAlign: "center" as const,
    width: 159
  },
  priceContainer: {
    flexDirection: "row" as const,
    justifyContent: "space-between" as const,
    alignItems: "center" as const,
    marginBottom: 16
  },
  priceLabel: {
    color: "#DFE9F0",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "700" as const,
    lineHeight: 20,
    width: 115
  },
  priceValue: {
    color: "#E36A1B",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "700" as const,
    lineHeight: 20,
    width: 77
  },
  inputContainer: {
    marginBottom: 12
  },
  inputRow: {
    flexDirection: "row" as const,
    gap: 16,
    marginBottom: 12
  },
  inputHalf: {
    flex: 1
  },
  membershipContainer: {
    flexDirection: "row" as const,
    justifyContent: "space-between" as const,
    alignItems: "center" as const,
    marginTop: 16,
    marginBottom: 16
  },
  membershipLabel: {
    color: "#DFE9F0",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "700" as const,
    lineHeight: 20,
    width: 174
  },
  membershipPrice: {
    width: 94
  },
  membershipPriceMain: {
    color: "#E36A1B",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "700" as const,
    lineHeight: 20
  },
  membershipPriceSuffix: {
    color: "#E36A1B",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: "600" as const,
    lineHeight: 18
  },
  checkboxContainer: {
    flexDirection: "row" as const,
    alignItems: "flex-start" as const,
    gap: 8,
    marginTop: 12,
    marginBottom: 20
  },
  checkbox: {
    width: 16,
    height: 16,
    backgroundColor: "#FFFFFF",
    borderWidth: 1,
    borderColor: "#D0D5DD",
    borderRadius: 4,
    marginTop: 2
  },
  checkboxText: {
    color: "#FCFCFD",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "400" as const,
    lineHeight: 20,
    width: 303,
    textShadowColor: "rgba(0, 0, 0, 0.25)",
    textShadowOffset: {width: 0, height: 4},
    textShadowRadius: 4
  },
  bottomContainer: {
    backgroundColor: "#111828",
    borderTopWidth: 1,
    borderTopColor: "#1D2939",
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    paddingTop: 20,
    paddingHorizontal: 24,
    paddingBottom: 34,
    gap: 16
  },
  totalContainer: {
    flexDirection: "row" as const,
    justifyContent: "space-between" as const,
    alignItems: "center" as const,
    gap: 16
  },
  totalLabel: {
    color: "#DFE9F0",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "400" as const,
    lineHeight: 20,
    flex: 1
  },
  totalValue: {
    color: "#DFE9F0",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: "700" as const,
    lineHeight: 24,
    textAlign: "right" as const,
    flex: 1
  },
  paymentButton: {
    backgroundColor: "#0F7C4D",
    borderWidth: 1,
    borderColor: "#0F7C4D",
    borderRadius: 8,
    height: 48,
    justifyContent: "center" as const,
    alignItems: "center" as const,
    paddingHorizontal: 86,
    paddingVertical: 12,
    shadowColor: "rgba(16, 24, 40, 0.05)",
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 1,
    shadowRadius: 2
  },
  paymentButtonText: {
    color: "#FCFCFD",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: "700" as const,
    lineHeight: 24
  },
  alternativePaymentContainer: {
    flexDirection: "row" as const,
    alignItems: "center" as const,
    gap: 8,
    paddingHorizontal: 19.5
  },
  alternativePaymentText: {
    color: "#FCFCFD",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: "600" as const,
    lineHeight: 24,
    width: 308
  },
  errorText: {
    color: "#FF3B30",
    fontSize: 11,
    marginTop: 4
  }
});

export default styles;
