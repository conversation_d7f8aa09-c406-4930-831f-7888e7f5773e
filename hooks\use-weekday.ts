import {useEffect, useMemo, useState} from "react";
import {getCurrentLocalization} from "../utils/i18n";
import {capitalize} from "../utils/string";

function useWeekday(date: Date) {
  const [locale, setLocale] = useState<string>();

  useEffect(() => {
    getCurrentLocalization().then((locale) => setLocale(locale));
  }, []);

  return useMemo(() => {
    if (!date || isNaN(date.getTime())) return "";

    const formattedWeekday = new Intl.DateTimeFormat("pt-BR", {
      weekday: "long"
    }).format(date);

    return capitalize(formattedWeekday?.slice(0, 3));
  }, [date, locale]);
}

export default useWeekday;
