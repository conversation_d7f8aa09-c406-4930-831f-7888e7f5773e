import React, {useState} from "react";
import {
  Text,
  View,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  ActivityIndicator
} from "react-native";
import {useTranslation} from "react-i18next";
import Svg, {<PERSON>, De<PERSON>, <PERSON><PERSON><PERSON>, G} from "react-native-svg";
import styles from "@/styles/wallet/wallet-card-registration.style";
import {useBottomModal} from "../../contexts/bottom-modal-context";
import {useCreateCreditCard} from "../../hooks/api/use-credit-cards";
import {CreateCreditCardRequest} from "../../models/api/credit-cards.models";
import {CreditCardsService} from "../../services/api/credit-cards/credit-cards.service";
import {useCreditCardErrorHandler} from "../../hooks/api/use-credit-card-error-handler";

// Icon components using SVG
const UserIcon = () => (
  <Svg
    width={20}
    height={20}
    viewBox="0 0 24 24"
    fill="none"
    style={styles.inputIcon}
  >
    <Path
      d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"
      stroke="#9CA3AF"
      strokeWidth={2}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <Path
      d="M12 11a4 4 0 1 0 0-8 4 4 0 0 0 0 8z"
      stroke="#9CA3AF"
      strokeWidth={2}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </Svg>
);

const CreditCardIcon = () => (
  <Svg
    width={20}
    height={20}
    viewBox="0 0 24 24"
    fill="none"
    style={styles.inputIcon}
  >
    <Path
      d="M21 4H3c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h18c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2z"
      stroke="#9CA3AF"
      strokeWidth={2}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <Path
      d="M1 10h22"
      stroke="#9CA3AF"
      strokeWidth={2}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </Svg>
);

const ShieldIcon = () => (
  <Svg width={20} height={20} viewBox="0 0 20 20" style={styles.inputIcon}>
    <Defs>
      <ClipPath id="clipPath1500375230">
        <Path d="M0 0L20 0L20 20L0 20L0 0Z" fillRule="nonzero" />
      </ClipPath>
    </Defs>
    <G clipPath="url(#clipPath1500375230)">
      <Path
        d="M2.66667 12.5L7.91667 12.5C8.3769 12.5 8.75 12.1269 8.75 11.6667C8.75 11.2064 8.3769 10.8333 7.91667 10.8333L2.66667 10.8333Q1.88391 10.8333 1.63986 10.8134Q1.3869 10.7927 1.28834 10.7425Q1.04709 10.6196 0.924162 10.3783Q0.87394 10.2798 0.853273 10.0268Q0.833333 9.78275 0.833333 9L0.833333 5L15.8333 5C15.8333 5.46023 16.2064 5.83333 16.6667 5.83333C17.1269 5.83333 17.5 5.46023 17.5 5L17.5 2.66667Q17.5 1.81594 17.4745 1.50414Q17.4271 0.923341 17.2275 0.531693Q16.8587 -0.19207 16.135 -0.560847Q15.7433 -0.760405 15.1625 -0.807859Q14.8507 -0.833333 14 -0.833333L2.66667 -0.833333Q1.81594 -0.833333 1.50414 -0.807859Q0.923341 -0.760405 0.531691 -0.560849Q-0.192073 -0.192074 -0.560849 0.53169Q-0.760405 0.923341 -0.807859 1.50414Q-0.833333 1.81594 -0.833333 2.66667L-0.833333 9Q-0.833333 9.85073 -0.807859 10.1625Q-0.760405 10.7433 -0.56085 11.135Q-0.192075 11.8587 0.53169 12.2275Q0.923341 12.4271 1.50414 12.4745Q1.81594 12.5 2.66667 12.5ZM15.8333 3.33333L0.833333 3.33333L0.833333 2.66667Q0.833333 1.88391 0.853273 1.63986Q0.87394 1.38691 0.924161 1.28834Q1.04709 1.04709 1.28834 0.924161Q1.38691 0.87394 1.63986 0.853273Q1.88391 0.833333 2.66667 0.833333L14 0.833333Q14.7828 0.833333 15.0268 0.853273Q15.2798 0.87394 15.3783 0.924161Q15.6196 1.04709 15.7425 1.28834Q15.7927 1.38691 15.8134 1.63986Q15.8333 1.88391 15.8333 2.66667L15.8333 3.33333ZM12.5162 13.8351Q12.7837 13.9945 12.9748 14.0856Q13.0588 14.1256 13.1495 14.1461Q13.2403 14.1667 13.3333 14.1667Q13.4264 14.1667 13.5171 14.1461Q13.6079 14.1256 13.6919 14.0856Q13.883 13.9945 14.1505 13.8351Q14.6759 13.5221 15.1171 13.1541Q16.6667 11.8615 16.6667 10.3544L16.6667 8.26907Q16.6667 8.20501 16.6569 8.1417Q16.6471 8.07839 16.6277 8.01731Q16.6084 7.95624 16.5799 7.89885Q16.5515 7.84146 16.5146 7.78909Q16.4777 7.73672 16.4332 7.6906Q16.3887 7.64449 16.3377 7.60571Q16.2867 7.56694 16.2304 7.53641Q16.1741 7.50589 16.1137 7.48433L14.2907 6.83293Q13.3328 6.4898 12.3743 6.83315L10.5528 7.48438Q10.4925 7.50595 10.4362 7.53647Q10.3799 7.567 10.3289 7.60578Q10.2779 7.64456 10.2334 7.69067Q10.189 7.73678 10.1521 7.78914Q10.1152 7.84151 10.0867 7.8989Q10.0583 7.95629 10.0389 8.01735Q10.0196 8.07841 10.0098 8.14172Q10 8.20502 10 8.26907L10 10.3544Q10 11.8616 11.5496 13.1541Q11.9908 13.5221 12.5162 13.8351ZM13.3333 12.3818Q12.9422 12.1454 12.6171 11.8742Q11.6667 11.0814 11.6667 10.3544L11.6667 8.85613L12.9364 8.40219Q13.3328 8.26017 13.7299 8.40241L15 8.85624L15 10.3544Q15 11.0814 14.0496 11.8742Q13.7244 12.1454 13.3333 12.3818Z"
        fillRule="evenodd"
        transform="translate(1.66667, 4.16667)"
        fill="#9CA3AF"
      />
    </G>
  </Svg>
);

interface WalletCardRegistrationProps {
  onSave?: () => void;
  onCancel?: () => void;
}

// Formatting functions for input masks
const formatCPFCNPJ = (value: string): string => {
  const cleanValue = value.replace(/\D/g, "");

  if (cleanValue.length <= 11) {
    // CPF format: XXX.XXX.XXX-XX
    return cleanValue
      .replace(/(\d{3})(\d)/, "$1.$2")
      .replace(/(\d{3})(\d)/, "$1.$2")
      .replace(/(\d{3})(\d{1,2})/, "$1-$2");
  } else {
    // CNPJ format: XX.XXX.XXX/XXXX-XX
    return cleanValue
      .replace(/(\d{2})(\d)/, "$1.$2")
      .replace(/(\d{3})(\d)/, "$1.$2")
      .replace(/(\d{3})(\d)/, "$1/$2")
      .replace(/(\d{4})(\d{1,2})/, "$1-$2");
  }
};

const formatPhoneNumber = (value: string): string => {
  const cleanValue = value.replace(/\D/g, "");

  if (cleanValue.length <= 10) {
    // Fixed phone: (XX) XXXX-XXXX
    return cleanValue
      .replace(/(\d{2})(\d)/, "($1) $2")
      .replace(/(\d{4})(\d{1,4})/, "$1-$2");
  } else {
    // Mobile phone: (XX) XXXXX-XXXX
    return cleanValue
      .replace(/(\d{2})(\d)/, "($1) $2")
      .replace(/(\d{5})(\d{1,4})/, "$1-$2");
  }
};

const formatCEP = (value: string): string => {
  const cleanValue = value.replace(/\D/g, "");
  return cleanValue.replace(/(\d{5})(\d{1,3})/, "$1-$2");
};

const WalletCardRegistration: React.FC<WalletCardRegistrationProps> = ({
  onSave,
  onCancel
}) => {
  const {t} = useTranslation();
  const modal = useBottomModal();

  // Error handling
  const {handleCreditCardError} = useCreditCardErrorHandler();

  // Form state
  const [formData, setFormData] = useState<CreateCreditCardRequest>({
    holderName: "",
    number: "",
    expiryMonth: "1",
    expiryYear: new Date().getFullYear().toString(),
    ccv: "",
    name: "",
    cpfCnpj: "",
    phoneNumber: "",
    postalCode: "",
    addressNumber: "",
    addressComplement: ""
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [expiryDate, setExpiryDate] = useState("");

  // API mutation
  const createCreditCardMutation = useCreateCreditCard({
    onSuccess: () => {
      Alert.alert(
        t("walletCardRegistration.success", "Sucesso"),
        t(
          "walletCardRegistration.successMessage",
          "Cartão adicionado com sucesso!"
        ),
        [
          {
            text: t("common.ok", "OK"),
            onPress: () => {
              onSave?.();
              modal.closeModal();
            }
          }
        ]
      );
    },
    onError: (error) => {
      handleCreditCardError(error, {
        context: {
          operation: "create",
          cardData: {
            holderName: formData.holderName,
            lastFourDigits: formData.number.slice(-4)
          }
        },
        onRetry: () => {
          // Clear form errors and allow retry
          setErrors({});
        }
      });
    }
  });

  const handleInputChange = (
    field: keyof CreateCreditCardRequest,
    value: string
  ) => {
    setFormData((prev) => ({...prev, [field]: value}));

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors((prev) => ({...prev, [field]: ""}));
    }

    // Real-time validation for specific fields
    if (field === "holderName") {
      const words = value.trim().split(/\s+/).filter(Boolean);
      if (words.length > 0 && words.length < 2) {
        setErrors((prev) => ({
          ...prev,
          holderName: "Informe nome e sobrenome (pelo menos 2 nomes)"
        }));
      }
    } else if (field === "number" && value.length > 0) {
      if (value.length < 16) {
        setErrors((prev) => ({
          ...prev,
          number: "Número do cartão deve ter 16 dígitos"
        }));
      } else if (!/^\d+$/.test(value)) {
        setErrors((prev) => ({
          ...prev,
          number: "Número do cartão deve conter apenas dígitos"
        }));
      }
    } else if (field === "ccv" && value.length > 0) {
      if (value.length < 3) {
        setErrors((prev) => ({
          ...prev,
          ccv: "CVV deve ter pelo menos 3 dígitos"
        }));
      } else if (!/^\d+$/.test(value)) {
        setErrors((prev) => ({
          ...prev,
          ccv: "CVV deve conter apenas dígitos"
        }));
      }
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    // Validate holder name
    // Holder name precisa de pelo menos dois nomes
    const holderWords = formData.holderName.trim().split(/\s+/).filter(Boolean);
    if (holderWords.length < 2) {
      newErrors.holderName = "Informe nome e sobrenome (pelo menos 2 nomes)";
    }

    // Validate card number
    if (!formData.number || formData.number.length !== 16) {
      newErrors.number = "Número do cartão deve ter 16 dígitos";
    }

    // Validate expiry month
    const expiryMonth = parseInt(formData.expiryMonth);
    if (!formData.expiryMonth || expiryMonth < 1 || expiryMonth > 12) {
      newErrors.expiryMonth = "Mês deve ser entre 1 e 12";
    }

    // Validate expiry year
    const currentYear = new Date().getFullYear();
    const expiryYear = parseInt(formData.expiryYear);
    if (!formData.expiryYear || expiryYear < currentYear) {
      newErrors.expiryYear = "Ano não pode ser no passado";
    }

    // Validate CVV
    if (!formData.ccv || formData.ccv.length < 3) {
      newErrors.ccv = "CVV deve ter pelo menos 3 dígitos";
    }

    // Validate full name
    if (!formData.name || formData.name.trim().length < 2) {
      newErrors.name = "Nome completo é obrigatório";
    }

    // Validate CPF/CNPJ
    if (!formData.cpfCnpj || formData.cpfCnpj.length < 11) {
      newErrors.cpfCnpj = "CPF/CNPJ deve ter pelo menos 11 dígitos";
    }

    // Validate phone number
    if (!formData.phoneNumber || formData.phoneNumber.length < 10) {
      newErrors.phoneNumber = "Telefone deve ter pelo menos 10 dígitos";
    }

    // Validate postal code
    if (!formData.postalCode || formData.postalCode.length !== 8) {
      newErrors.postalCode = "CEP deve ter 8 dígitos";
    }

    // Validate address number
    if (!formData.addressNumber || formData.addressNumber.trim().length === 0) {
      newErrors.addressNumber = "Número do endereço é obrigatório";
    }

    // Additional validation using service
    const serviceErrors = CreditCardsService.validateCreditCardData(formData);
    serviceErrors.forEach((error) => {
      if (!newErrors.general) {
        newErrors.general = error;
      }
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = async () => {
    if (!validateForm()) {
      return;
    }

    try {
      // Use the form data directly since all fields are now collected from the user
      await createCreditCardMutation.mutateAsync(formData);
    } catch (error) {
      // Error is handled by the mutation's onError callback
      console.error("Failed to create credit card:", error);
    }
  };

  const handleBack = () => {
    onCancel?.();
    modal.closeModal();
  };

  return (
    <ScrollView
      style={{flex: 1}}
      contentContainerStyle={{
        flexGrow: 1,
        paddingVertical: 20
      }}
      showsVerticalScrollIndicator={false}
    >
      <View
        style={{
          alignItems: "center",
          width: "100%"
        }}
      >
        {/* Header Title - removed header indicator as it's handled by the modal */}

        {/* Card Image */}
        <View style={styles.cardImageContainer}>
          <View style={styles.cardImage}>
            {/* Chip icon in the top right */}
            <View style={styles.chipIcon} />
          </View>
        </View>

        {/* Section Title */}
        <Text style={styles.sectionTitle}>
          {t("walletCardRegistration.cardData", "Dados do cartão")}
        </Text>

        {/* Form Fields */}
        <View style={newStyles.formContainer}>
          {/* Name Field */}
          <View>
            <View
              style={[
                newStyles.inputContainer,
                !!errors.holderName && newStyles.inputContainerError
              ]}
            >
              <TextInput
                style={newStyles.textInput}
                placeholder={t(
                  "walletCardRegistration.cardNamePlaceholder",
                  "Nome do titular"
                )}
                placeholderTextColor="#98A2B3"
                value={formData.holderName}
                onChangeText={(value) => handleInputChange("holderName", value)}
                autoCapitalize="words"
                autoCorrect={false}
              />
            </View>
            {!!errors.holderName && (
              <Text style={newStyles.errorText}>{errors.holderName}</Text>
            )}
          </View>

          {/* Card Number Field */}
          <View>
            <View
              style={[
                newStyles.inputContainer,
                !!errors.number && newStyles.inputContainerError
              ]}
            >
              <TextInput
                style={newStyles.textInput}
                placeholder={t(
                  "walletCardRegistration.cardNumberPlaceholder",
                  "Número do cartão"
                )}
                placeholderTextColor="#98A2B3"
                value={CreditCardsService.formatCardNumber(formData.number)}
                onChangeText={(value) => {
                  const cleanValue = value.replace(/\D/g, "");
                  // Limit to 16 digits maximum
                  const limitedValue = cleanValue.substring(0, 16);
                  handleInputChange("number", limitedValue);
                }}
                keyboardType="numeric"
                maxLength={19} // 16 digits + 3 spaces
              />
            </View>
            {!!errors.number && (
              <Text style={newStyles.errorText}>{errors.number}</Text>
            )}
          </View>

          {/* Two Column Layout for Expiry Date and CVV */}
          <View style={newStyles.twoColumnContainer}>
            {/* Expiry Date Field */}
            <View style={newStyles.halfWidthField}>
              <View
                style={[
                  newStyles.halfWidthContainer,
                  (() => {
                    // Validação simples do campo MM/AA
                    const cleanValue = expiryDate.replace(/\D/g, "");
                    if (cleanValue.length >= 2) {
                      const month = parseInt(cleanValue.substring(0, 2), 10);
                      if (month < 1 || month > 12) {
                        return true;
                      }
                    }
                    if (cleanValue.length >= 4) {
                      const year =
                        parseInt(cleanValue.substring(2, 4), 10) + 2000;
                      const currentYear = new Date().getFullYear();
                      if (year < currentYear) {
                        return true;
                      }
                    }
                    // Também verifica erros do estado se existirem
                    if (errors.expiryMonth || errors.expiryYear) {
                      return true;
                    }
                    return false;
                  })() && newStyles.inputContainerError
                ]}
              >
                <TextInput
                  style={newStyles.textInput}
                  placeholder="MM/AA"
                  placeholderTextColor="#98A2B3"
                  value={expiryDate}
                  onChangeText={(value) => {
                    // Handle complete deletion first
                    if (value === "") {
                      setExpiryDate("");
                      setErrors((prev) => ({
                        ...prev,
                        expiryMonth: "",
                        expiryYear: ""
                      }));
                      setFormData((prev) => ({
                        ...prev,
                        expiryMonth: "1",
                        expiryYear: new Date().getFullYear().toString()
                      }));
                      return;
                    }

                    // Handle backspace/deletion when there's a trailing slash
                    if (
                      value.length < expiryDate.length &&
                      value.endsWith("/")
                    ) {
                      // User is deleting and we have a trailing slash, remove it
                      value = value.slice(0, -1);
                    }

                    // Clean the input - only allow digits
                    const cleanValue = value.replace(/\D/g, "");

                    // Limit to 4 digits maximum (MMYY)
                    const limitedValue = cleanValue.substring(0, 4);

                    // Format the value
                    let formattedValue = limitedValue;
                    if (limitedValue.length >= 3) {
                      // Add slash after 2 digits: MM/YY
                      formattedValue =
                        limitedValue.substring(0, 2) +
                        "/" +
                        limitedValue.substring(2);
                    }

                    // Set the formatted value
                    setExpiryDate(formattedValue);

                    // Clear errors when user types
                    setErrors((prev) => ({
                      ...prev,
                      expiryMonth: "",
                      expiryYear: ""
                    }));

                    // Update form data
                    if (limitedValue.length >= 2) {
                      const month = parseInt(limitedValue.substring(0, 2), 10);
                      const year =
                        limitedValue.length >= 4
                          ? parseInt(limitedValue.substring(2, 4), 10) + 2000
                          : new Date().getFullYear();

                      setFormData((prev) => ({
                        ...prev,
                        expiryMonth: month.toString(),
                        expiryYear: year.toString()
                      }));
                    } else if (limitedValue.length === 1) {
                      // Only one digit entered, set month to that digit and reset year
                      const month = parseInt(limitedValue, 10);
                      setFormData((prev) => ({
                        ...prev,
                        expiryMonth: month.toString(),
                        expiryYear: new Date().getFullYear().toString()
                      }));
                    }
                  }}
                  keyboardType="numeric"
                  maxLength={5}
                />
              </View>
              {(() => {
                // Validação simples do campo MM/AA
                const cleanValue = expiryDate.replace(/\D/g, "");
                if (cleanValue.length >= 2) {
                  const month = parseInt(cleanValue.substring(0, 2), 10);
                  if (month < 1 || month > 12) {
                    return (
                      <Text style={newStyles.errorText}>
                        Mês deve ser entre 1 e 12
                      </Text>
                    );
                  }
                }
                if (cleanValue.length >= 4) {
                  const year = parseInt(cleanValue.substring(2, 4), 10) + 2000;
                  const currentYear = new Date().getFullYear();
                  if (year < currentYear) {
                    return (
                      <Text style={newStyles.errorText}>
                        Ano não pode ser no passado
                      </Text>
                    );
                  }
                }
                // Também mostra erros do estado se existirem
                if (errors.expiryMonth || errors.expiryYear) {
                  return (
                    <Text style={newStyles.errorText}>
                      {errors.expiryMonth || errors.expiryYear}
                    </Text>
                  );
                }
                return null;
              })()}
            </View>

            {/* CVV Field */}
            <View style={newStyles.halfWidthField}>
              <View
                style={[
                  newStyles.halfWidthContainer,
                  !!errors.ccv && newStyles.inputContainerError
                ]}
              >
                <TextInput
                  style={newStyles.textInput}
                  placeholder="CVV"
                  placeholderTextColor="#98A2B3"
                  value={formData.ccv}
                  onChangeText={(value) => {
                    const cleanValue = value.replace(/\D/g, "");
                    handleInputChange("ccv", cleanValue);
                  }}
                  keyboardType="numeric"
                  maxLength={4}
                  secureTextEntry
                />
              </View>
              {!!errors.ccv && (
                <Text style={newStyles.errorText}>{errors.ccv}</Text>
              )}
            </View>
          </View>

          {/* Full Name Field */}
          <View>
            <View
              style={[
                newStyles.inputContainer,
                !!errors.name && newStyles.inputContainerError
              ]}
            >
              <TextInput
                style={newStyles.textInput}
                placeholder={t(
                  "walletCardRegistration.fullNamePlaceholder",
                  "Nome completo"
                )}
                placeholderTextColor="#98A2B3"
                value={formData.name}
                onChangeText={(value) => handleInputChange("name", value)}
                autoCapitalize="words"
                autoCorrect={false}
              />
            </View>
            {!!errors.name && (
              <Text style={newStyles.errorText}>{errors.name}</Text>
            )}
          </View>

          {/* CPF/CNPJ Field */}
          <View>
            <View
              style={[
                newStyles.inputContainer,
                !!errors.cpfCnpj && newStyles.inputContainerError
              ]}
            >
              <TextInput
                style={newStyles.textInput}
                placeholder={t(
                  "walletCardRegistration.cpfCnpjPlaceholder",
                  "CPF/CNPJ"
                )}
                placeholderTextColor="#98A2B3"
                value={formatCPFCNPJ(formData.cpfCnpj)}
                onChangeText={(value) => {
                  const cleanValue = value.replace(/\D/g, "");
                  handleInputChange("cpfCnpj", cleanValue);
                }}
                keyboardType="numeric"
                maxLength={18} // XX.XXX.XXX/XXXX-XX (18 characters)
              />
            </View>
            {!!errors.cpfCnpj && (
              <Text style={newStyles.errorText}>{errors.cpfCnpj}</Text>
            )}
          </View>

          {/* Phone Number Field */}
          <View>
            <View
              style={[
                newStyles.inputContainer,
                !!errors.phoneNumber && newStyles.inputContainerError
              ]}
            >
              <TextInput
                style={newStyles.textInput}
                placeholder={t(
                  "walletCardRegistration.phoneNumberPlaceholder",
                  "Telefone"
                )}
                placeholderTextColor="#98A2B3"
                value={formatPhoneNumber(formData.phoneNumber)}
                onChangeText={(value) => {
                  const cleanValue = value.replace(/\D/g, "");
                  handleInputChange("phoneNumber", cleanValue);
                }}
                keyboardType="phone-pad"
                returnKeyType="done"
                maxLength={15} // (XX) XXXXX-XXXX (15 characters)
              />
            </View>
            {!!errors.phoneNumber && (
              <Text style={newStyles.errorText}>{errors.phoneNumber}</Text>
            )}
          </View>

          {/* Postal Code Field */}
          <View>
            <View
              style={[
                newStyles.inputContainer,
                !!errors.postalCode && newStyles.inputContainerError
              ]}
            >
              <TextInput
                style={newStyles.textInput}
                placeholder={t(
                  "walletCardRegistration.postalCodePlaceholder",
                  "CEP"
                )}
                placeholderTextColor="#98A2B3"
                value={formatCEP(formData.postalCode)}
                onChangeText={(value) => {
                  const cleanValue = value.replace(/\D/g, "");
                  handleInputChange("postalCode", cleanValue);
                }}
                keyboardType="numeric"
                maxLength={9} // XXXXX-XXX (9 characters)
              />
            </View>
            {!!errors.postalCode && (
              <Text style={newStyles.errorText}>{errors.postalCode}</Text>
            )}
          </View>

          {/* Two Column Layout for Address Number and Complement */}
          <View style={newStyles.twoColumnContainer}>
            {/* Address Number Field */}
            <View style={newStyles.halfWidthField}>
              <View
                style={[
                  newStyles.halfWidthContainer,
                  !!errors.addressNumber && newStyles.inputContainerError
                ]}
              >
                <TextInput
                  style={newStyles.textInput}
                  placeholder={t(
                    "walletCardRegistration.addressNumberPlaceholder",
                    "Número"
                  )}
                  placeholderTextColor="#98A2B3"
                  value={formData.addressNumber}
                  onChangeText={(value) =>
                    handleInputChange("addressNumber", value)
                  }
                  keyboardType="numeric"
                />
              </View>
              {!!errors.addressNumber && (
                <Text style={newStyles.errorText}>{errors.addressNumber}</Text>
              )}
            </View>

            {/* Address Complement Field */}
            <View style={newStyles.halfWidthField}>
              <View
                style={[
                  newStyles.halfWidthContainer,
                  !!errors.addressComplement && newStyles.inputContainerError
                ]}
              >
                <TextInput
                  style={newStyles.textInput}
                  placeholder={t(
                    "walletCardRegistration.addressComplementPlaceholder",
                    "Complemento"
                  )}
                  placeholderTextColor="#98A2B3"
                  value={formData.addressComplement}
                  onChangeText={(value) =>
                    handleInputChange("addressComplement", value)
                  }
                  autoCapitalize="words"
                />
              </View>
              {!!errors.addressComplement && (
                <Text style={newStyles.errorText}>
                  {errors.addressComplement}
                </Text>
              )}
            </View>
          </View>

          {/* General error */}
          {!!errors.general && (
            <View style={newStyles.generalErrorContainer}>
              <Text style={newStyles.generalErrorText}>{errors.general}</Text>
            </View>
          )}
        </View>

        {/* Action Buttons */}
        <View style={newStyles.buttonContainer}>
          {/* Primary Button */}
          <TouchableOpacity
            style={[
              newStyles.primaryButton,
              createCreditCardMutation.isPending &&
                newStyles.primaryButtonDisabled
            ]}
            onPress={handleSave}
            disabled={createCreditCardMutation.isPending}
          >
            {createCreditCardMutation.isPending ? (
              <ActivityIndicator size="small" color="#FCFCFD" />
            ) : (
              <Text style={newStyles.primaryButtonText}>
                {t(
                  "walletCardRegistration.addCard",
                  "Adicionar cartão a carteira"
                )}
              </Text>
            )}
          </TouchableOpacity>

          {/* Secondary Button */}
          <TouchableOpacity
            style={newStyles.secondaryButton}
            onPress={handleBack}
            disabled={createCreditCardMutation.isPending}
          >
            <Text style={newStyles.secondaryButtonText}>
              {t("walletCardRegistration.back", "Voltar")}
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </ScrollView>
  );
};

// New styles based on Motiff design
const newStyles = {
  formContainer: {
    gap: 20,
    marginTop: 18,
    paddingHorizontal: 4
  },
  inputContainer: {
    borderWidth: 1,
    borderColor: "#F2F4F7",
    borderRadius: 8,
    backgroundColor: "transparent",
    shadowColor: "rgba(16, 24, 40, 0.05)",
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 1,
    shadowRadius: 2,
    elevation: 1,
    width: "100%" as const
  },
  inputContainerError: {
    borderColor: "#FF3B30",
    borderWidth: 1.5
  },
  textInput: {
    color: "#F2F4F7",
    fontSize: 14,
    lineHeight: 20,
    paddingHorizontal: 12,
    paddingVertical: 10,
    minHeight: 40,
    fontFamily: "Open Sans",
    width: "100%" as const
  },
  twoColumnContainer: {
    flexDirection: "row" as const,
    gap: 16,
    width: "100%" as const
  },
  halfWidthField: {
    flex: 1
  },
  halfWidthContainer: {
    width: "100%" as const,
    borderWidth: 1,
    borderColor: "#F2F4F7",
    borderRadius: 8,
    backgroundColor: "transparent",
    shadowColor: "rgba(16, 24, 40, 0.05)",
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 1,
    shadowRadius: 2,
    elevation: 1
  },
  buttonContainer: {
    gap: 4,
    marginTop: 18,
    alignItems: "center" as const,
    paddingHorizontal: 12
  },
  primaryButton: {
    backgroundColor: "#0F7C4D",
    borderWidth: 1,
    borderColor: "#0F7C4D",
    borderRadius: 8,
    paddingVertical: 12,
    height: 48,
    justifyContent: "center" as const,
    alignItems: "center" as const,
    shadowColor: "rgba(16, 24, 40, 0.05)",
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 1,
    shadowRadius: 2,
    elevation: 1,
    width: 350
  },
  primaryButtonText: {
    color: "#FCFCFD",
    fontWeight: "700" as const,
    lineHeight: 24,
    fontSize: 16,
    textAlign: "center" as const
  },
  secondaryButton: {
    borderRadius: 8,
    paddingVertical: 12,
    height: 48,
    justifyContent: "center" as const,
    alignItems: "center" as const,
    backgroundColor: "transparent",
    width: "100%" as const
  },
  secondaryButtonText: {
    color: "#FCFCFD",
    fontWeight: "600" as const,
    lineHeight: 24,
    fontSize: 16,
    textAlign: "center" as const
  },
  errorText: {
    color: "#FF3B30",
    fontSize: 11,
    marginTop: 4,
    marginLeft: 4,
    fontWeight: "400" as const,
    lineHeight: 14
  },
  generalErrorContainer: {
    backgroundColor: "rgba(255, 59, 48, 0.1)",
    borderWidth: 1,
    borderColor: "rgba(255, 59, 48, 0.3)",
    borderRadius: 8,
    padding: 12,
    marginTop: 8
  },
  generalErrorText: {
    color: "#FF3B30",
    fontSize: 12,
    fontWeight: "500" as const,
    lineHeight: 16,
    textAlign: "center" as const
  },
  primaryButtonDisabled: {
    backgroundColor: "#9CA3AF",
    borderColor: "#9CA3AF"
  }
};

export default WalletCardRegistration;
