/**
 * Gerenciador de tokens JWT com renovação automática
 */

import * as SecureStore from "expo-secure-store";
import {ApiLogger} from "../base/api-logger";
import {TokenData} from "../base/api-types";
import {RefreshTokenResponse} from "@/models/api/auth.models";
import {AuthenticationError, RefreshTokenError} from "../base/api-errors";
import {apiClient} from "../base/api-client";

export interface TokenManagerConfig {
  refreshThreshold: number; // Minutos antes da expiração para renovar
  maxRetryAttempts: number;
  retryDelay: number;
  enableAutoRefresh: boolean;
}

export class TokenManager {
  private static instance: TokenManager;
  private tokenData: TokenData | null = null;
  private refreshPromise: Promise<string> | null = null;
  private refreshTimer: ReturnType<typeof setTimeout> | null = null;
  private config: TokenManagerConfig;
  private onTokenExpired: (() => void) | null = null;
  private onTokenRefreshed: ((token: TokenData) => void) | null = null;

  // Chaves para armazenamento seguro
  private static readonly ACCESS_TOKEN_KEY = "clubm_access_token";
  private static readonly REFRESH_TOKEN_KEY = "clubm_refresh_token";
  private static readonly TOKEN_DATA_KEY = "clubm_token_data";

  private constructor() {
    this.config = {
      refreshThreshold: 5, // 5 minutos antes da expiração
      maxRetryAttempts: 3,
      retryDelay: 1000,
      enableAutoRefresh: true
    };
  }

  /**
   * Singleton instance
   */
  static getInstance(): TokenManager {
    if (!this.instance) {
      this.instance = new TokenManager();
    }
    return this.instance;
  }

  /**
   * Configurar o gerenciador de tokens
   */
  configure(config: Partial<TokenManagerConfig>): void {
    this.config = {...this.config, ...config};
    ApiLogger.info("TokenManager configurado", this.config);
  }

  /**
   * Definir dados do token
   */
  async setTokenData(tokenData: TokenData): Promise<void> {
    try {
      // Garantir que expiresAt seja sempre um objeto Date
      let expiresAt = tokenData.expiresAt;
      if (expiresAt && typeof expiresAt === "string") {
        expiresAt = new Date(expiresAt);
      } else if (!expiresAt) {
        expiresAt = new Date(Date.now() + tokenData.expiresIn * 1000);
      }

      this.tokenData = {
        ...tokenData,
        expiresAt
      };

      // Configurar token no apiClient
      apiClient.setAuthToken(this.tokenData);
      console.log("🔧 [TOKEN-MANAGER] Token configurado no apiClient");

      // Salvar no armazenamento seguro
      await this.saveTokenToStorage(this.tokenData);

      // Configurar renovação automática
      if (this.config.enableAutoRefresh) {
        this.scheduleTokenRefresh();
      }

      ApiLogger.info("Token data configurado", {
        tokenType: tokenData.tokenType,
        expiresAt: this.tokenData.expiresAt,
        hasRefreshToken: !!tokenData.refreshToken
      });
    } catch (error) {
      ApiLogger.error("Erro ao configurar token data", error as Error);
      throw error;
    }
  }

  /**
   * Obter token de acesso atual
   */
  getAccessToken(): string | null {
    return this.tokenData?.accessToken || null;
  }

  /**
   * Obter dados completos do token
   */
  getTokenData(): TokenData | null {
    return this.tokenData ? {...this.tokenData} : null;
  }

  /**
   * Verificar se o token está expirado
   */
  isTokenExpired(): boolean {
    if (!this.tokenData?.expiresAt) return true;

    const now = new Date();
    return now >= this.tokenData.expiresAt;
  }

  /**
   * Verificar se o token precisa ser renovado
   */
  needsRefresh(): boolean {
    if (!this.tokenData?.expiresAt) return false;

    const now = new Date();
    const refreshTime = new Date(
      this.tokenData.expiresAt.getTime() -
        this.config.refreshThreshold * 60 * 1000
    );

    return now >= refreshTime;
  }

  /**
   * Renovar token automaticamente
   */
  async refreshToken(): Promise<string> {
    // Se já existe uma renovação em andamento, aguardar
    if (this.refreshPromise) {
      return this.refreshPromise;
    }

    if (!this.tokenData?.accessToken) {
      throw new RefreshTokenError("Token de acesso não disponível");
    }

    this.refreshPromise = this.performTokenRefresh();

    try {
      const newAccessToken = await this.refreshPromise;
      this.refreshPromise = null;
      return newAccessToken;
    } catch (error) {
      this.refreshPromise = null;
      throw error;
    }
  }

  /**
   * Executar renovação do token
   */
  private async performTokenRefresh(): Promise<string> {
    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= this.config.maxRetryAttempts; attempt++) {
      try {
        ApiLogger.info(`Tentativa ${attempt} de renovação de token`);

        const response = await this.makeRefreshRequest();

        // Atualizar token data
        await this.updateTokenFromRefresh(response);

        ApiLogger.info("Token renovado com sucesso");
        return this.tokenData!.accessToken;
      } catch (error) {
        lastError = error as Error;
        ApiLogger.warn(`Falha na tentativa ${attempt} de renovação`, error);

        if (attempt < this.config.maxRetryAttempts) {
          await this.delay(this.config.retryDelay * attempt);
        }
      }
    }

    // Se chegou aqui, todas as tentativas falharam
    ApiLogger.error(
      "Falha em todas as tentativas de renovação",
      lastError || undefined
    );
    await this.handleRefreshFailure();
    throw new RefreshTokenError(
      "Falha ao renovar token após múltiplas tentativas"
    );
  }

  /**
   * Fazer requisição de renovação
   */
  private async makeRefreshRequest(): Promise<RefreshTokenResponse> {
    if (!this.tokenData?.accessToken) {
      throw new RefreshTokenError(
        "Token de acesso não disponível para renovação"
      );
    }

    const response = await fetch(
      `${process.env.EXPO_PUBLIC_API_BASE_URL}/api/auth/refresh`,
      {
        method: "GET",
        headers: {
          Authorization: `Bearer ${this.tokenData.accessToken}`,
          Accept: "application/json",
          "Accept-Language": "pt-br"
        }
      }
    );

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new RefreshTokenError(
        errorData.message || `HTTP ${response.status}: ${response.statusText}`
      );
    }

    return response.json();
  }

  /**
   * Atualizar token data após renovação
   */
  private async updateTokenFromRefresh(
    response: RefreshTokenResponse
  ): Promise<void> {
    if (!this.tokenData) return;

    this.tokenData = {
      ...this.tokenData,
      accessToken: response.accessToken,
      tokenType: response.tokenType,
      expiresIn: response.expiresIn,
      expiresAt: new Date(Date.now() + response.expiresIn * 1000)
      // Manter refreshToken existente, pois a API não retorna um novo
    };

    await this.saveTokenToStorage(this.tokenData);

    // Reagendar próxima renovação
    if (this.config.enableAutoRefresh) {
      this.scheduleTokenRefresh();
    }

    // Notificar listeners
    this.onTokenRefreshed?.(this.tokenData);
  }

  /**
   * Agendar próxima renovação automática
   */
  private scheduleTokenRefresh(): void {
    // Limpar timer anterior
    if (this.refreshTimer) {
      clearTimeout(this.refreshTimer);
    }

    if (!this.tokenData?.expiresAt) return;

    const now = new Date();
    const refreshTime = new Date(
      this.tokenData.expiresAt.getTime() -
        this.config.refreshThreshold * 60 * 1000
    );

    const delay = Math.max(0, refreshTime.getTime() - now.getTime());

    this.refreshTimer = setTimeout(async () => {
      try {
        await this.refreshToken();
      } catch (error) {
        ApiLogger.error(
          "Erro na renovação automática de token",
          error as Error
        );
      }
    }, delay);

    ApiLogger.debug(
      `Próxima renovação agendada para: ${refreshTime.toISOString()}`
    );
  }

  /**
   * Lidar com falha na renovação
   */
  private async handleRefreshFailure(): Promise<void> {
    await this.clearTokenData();
    this.onTokenExpired?.();
  }

  /**
   * Limpar dados do token
   */
  async clearTokenData(): Promise<void> {
    try {
      this.tokenData = null;
      this.refreshPromise = null;

      // Limpar token do apiClient
      apiClient.clearAuthToken();
      console.log("🗑️ [TOKEN-MANAGER] Token removido do apiClient");

      if (this.refreshTimer) {
        clearTimeout(this.refreshTimer);
        this.refreshTimer = null;
      }

      // Remover do armazenamento seguro
      await Promise.all([
        SecureStore.deleteItemAsync(TokenManager.ACCESS_TOKEN_KEY).catch(
          () => {}
        ),
        SecureStore.deleteItemAsync(TokenManager.REFRESH_TOKEN_KEY).catch(
          () => {}
        ),
        SecureStore.deleteItemAsync(TokenManager.TOKEN_DATA_KEY).catch(() => {})
      ]);

      ApiLogger.info("Token data limpo");
    } catch (error) {
      ApiLogger.error("Erro ao limpar token data", error as Error);
    }
  }

  /**
   * Carregar token do armazenamento seguro
   */
  async loadTokenFromStorage(): Promise<TokenData | null> {
    try {
      console.log(
        "🔍 loadTokenFromStorage - Iniciando carregamento do storage"
      );
      const tokenDataJson = await SecureStore.getItemAsync(
        TokenManager.TOKEN_DATA_KEY
      );
      console.log("🔍 loadTokenFromStorage - Dados do storage:", {
        hasData: !!tokenDataJson,
        dataLength: tokenDataJson?.length
      });

      if (!tokenDataJson) {
        console.log(
          "❌ loadTokenFromStorage - Nenhum dado encontrado no storage"
        );
        return null;
      }

      const tokenData = JSON.parse(tokenDataJson) as TokenData;
      console.log("🔍 loadTokenFromStorage - Token parseado:", {
        hasAccessToken: !!tokenData.accessToken,
        tokenType: tokenData.tokenType,
        expiresAt: tokenData.expiresAt
      });

      // Converter string de data para Date
      if (tokenData.expiresAt) {
        tokenData.expiresAt = new Date(tokenData.expiresAt);
      }

      // Verificar se o token não está expirado
      if (tokenData.expiresAt && new Date() >= tokenData.expiresAt) {
        console.log("⏰ loadTokenFromStorage - Token expirado, limpando");
        ApiLogger.info("Token carregado do storage está expirado");
        await this.clearTokenData();
        return null;
      }

      this.tokenData = tokenData;

      // Configurar token no apiClient
      apiClient.setAuthToken(this.tokenData);
      console.log(
        "🔧 [TOKEN-MANAGER] Token do storage configurado no apiClient"
      );

      // Configurar renovação automática
      if (this.config.enableAutoRefresh) {
        this.scheduleTokenRefresh();
      }

      console.log("✅ loadTokenFromStorage - Token carregado com sucesso");
      ApiLogger.info("Token carregado do storage com sucesso");
      return tokenData;
    } catch (error) {
      console.log("❌ loadTokenFromStorage - Erro:", error);
      ApiLogger.error("Erro ao carregar token do storage", error as Error);
      await this.clearTokenData();
      return null;
    }
  }

  /**
   * Salvar token no armazenamento seguro
   */
  private async saveTokenToStorage(tokenData: TokenData): Promise<void> {
    try {
      console.log("💾 saveTokenToStorage - Salvando token:", {
        hasAccessToken: !!tokenData.accessToken,
        tokenType: tokenData.tokenType,
        hasRefreshToken: !!tokenData.refreshToken,
        expiresAt: tokenData.expiresAt
      });

      await Promise.all([
        SecureStore.setItemAsync(
          TokenManager.ACCESS_TOKEN_KEY,
          tokenData.accessToken
        ),
        tokenData.refreshToken
          ? SecureStore.setItemAsync(
              TokenManager.REFRESH_TOKEN_KEY,
              tokenData.refreshToken
            )
          : Promise.resolve(),
        SecureStore.setItemAsync(
          TokenManager.TOKEN_DATA_KEY,
          JSON.stringify(tokenData)
        )
      ]);

      console.log("✅ saveTokenToStorage - Token salvo com sucesso no storage");
    } catch (error) {
      console.log("❌ saveTokenToStorage - Erro ao salvar:", error);
      ApiLogger.error("Erro ao salvar token no storage", error as Error);
      throw error;
    }
  }

  /**
   * Configurar callbacks
   */
  setOnTokenExpired(callback: (() => void) | null): void {
    this.onTokenExpired = callback;
  }

  setOnTokenRefreshed(callback: ((token: TokenData) => void) | null): void {
    this.onTokenRefreshed = callback;
  }

  /**
   * Delay helper
   */
  private delay(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  /**
   * Obter status do gerenciador
   */
  getStatus() {
    return {
      hasToken: !!this.tokenData,
      isExpired: this.isTokenExpired(),
      needsRefresh: this.needsRefresh(),
      expiresAt: this.tokenData?.expiresAt,
      hasRefreshToken: !!this.tokenData?.refreshToken,
      autoRefreshEnabled: this.config.enableAutoRefresh
    };
  }
}

// Exportar instância singleton
export const tokenManager = TokenManager.getInstance();
