import React from "react";
import {
  Text,
  View,
  ScrollView,
  TouchableOpacity,
  Alert,
  ActivityIndicator
} from "react-native";
import ScreenWithHeader from "../../components/screen-with-header";
import {useTranslation} from "react-i18next";
import {useRouter} from "expo-router";
import NewChevronRightIcon from "../../components/icons/new-chevron-right-icon";
import ManagePermissionsIcon from "../../components/icons/manage-permissions-icon";
import PrivacyOptionsIcon from "../../components/icons/privacy-options-icon";
import PurchaseHistoryIcon from "../../components/icons/purchase-history-icon";
import MyProductsIcon from "../../components/icons/my-products-icon";
import GeneralNotificationsIcon from "../../components/icons/general-notifications-icon";
import MyOpportunitiesIcon from "../../components/icons/my-opportunities-icon";
import SupportHelpIcon from "../../components/icons/support-help-icon";
import AppRatingIcon from "../../components/icons/app-rating-icon";
import AppInfoIcon from "../../components/icons/app-info-icon";
import NewLogoutIcon from "../../components/icons/new-logout-icon";
import {useAuth} from "../../contexts/AuthContext";
import {useGuestUser} from "../../contexts/guest-user-context";
import styles from "../../styles/settings/general-settings.style";

const GeneralSettings: React.FC = () => {
  const {t} = useTranslation();
  const router = useRouter();
  const {logout, isLoading} = useAuth();
  const {isGuest} = useGuestUser();

  // Redirect guest users back - they shouldn't access this screen
  React.useEffect(() => {
    if (isGuest) {
      router.back();
    }
  }, [isGuest, router]);

  const handleManagePermissions = () => {
    router.push("/(settings)/manage-permissions");
  };

  const handlePrivacyOptions = () => {
    router.push("/(settings)/privacy");
  };

  const handleGeneralNotifications = () => {
    router.push("/(settings)/general-notifications");
  };

  const handleMyOpportunities = () => {
    router.push("/(settings)/my-opportunities");
  };

  const handlePurchaseHistory = () => {
    router.push("/(settings)/history-purchases");
  };

  const handleMyProducts = () => {
    router.push("/(settings)/my-products");
  };

  const handleSupportAndHelp = () => {
    router.push("/(settings)/support");
  };

  const handleRateApp = () => {
    router.push("/(settings)/rate-app");
  };

  const handleAppInfo = () => {
    router.push("/(settings)/about-app");
  };

  const handleLogout = () => {
    Alert.alert(
      t("settings.logout", "Sair da conta"),
      t("settings.logoutConfirm", "Tem certeza que deseja sair da sua conta?"),
      [
        {
          text: t("settings.cancel", "Cancelar"),
          style: "cancel"
        },
        {
          text: t("settings.logout", "Sair"),
          style: "destructive",
          onPress: async () => {
            try {
              // Executar logout completo usando o AuthContext
              await logout();
              // A navegação será feita automaticamente pelo AuthContext
            } catch (error) {
              console.error("Erro durante logout:", error);
              // Em caso de erro, ainda assim redirecionar para login
              router.replace("/(auth)/login");
            }
          }
        }
      ]
    );
  };

  return (
    <ScreenWithHeader
      screenTitle={t("settings.generalSettings", "Ajustes")}
      backButton
    >
      <ScrollView contentContainerStyle={styles.contentContainer}>
        {/* Configurações de sistema */}
        <View style={styles.sectionContainer}>
          <Text style={styles.sectionTitle}>
            {t("settings.systemSettings", "Configurações de sistema")}
          </Text>
          <View style={styles.settingsList}>
            <TouchableOpacity
              style={[styles.settingItem]}
              onPress={handleManagePermissions}
            >
              <ManagePermissionsIcon width={24} height={24} />
              <View style={styles.settingContent}>
                <Text style={styles.settingTitle}>
                  {t("settings.managePermissions", "Gerenciar permissões")}
                </Text>
              </View>
              <NewChevronRightIcon width={16} height={16} />
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.settingItem]}
              onPress={handlePrivacyOptions}
            >
              <PrivacyOptionsIcon width={24} height={24} />
              <View style={styles.settingContent}>
                <Text style={styles.settingTitle}>
                  {t("settings.privacyOptions", "Opções de privacidade")}
                </Text>
              </View>
              <NewChevronRightIcon width={16} height={16} />
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.settingItem, styles.settingItemLast]}
              onPress={handleGeneralNotifications}
            >
              <GeneralNotificationsIcon width={24} height={24} />
              <View style={styles.settingContent}>
                <Text style={styles.settingTitle}>
                  {t("settings.generalNotifications", "Notificações gerais")}
                </Text>
              </View>
              <NewChevronRightIcon width={16} height={16} />
            </TouchableOpacity>
          </View>
        </View>

        {/* Históricos e compras */}
        <View style={styles.sectionContainer}>
          <Text style={styles.sectionTitle}>
            {t("settings.historyAndPurchases", "Históricos e compras")}
          </Text>
          <View style={styles.settingsList}>
            <TouchableOpacity
              style={[styles.settingItem]}
              onPress={handleMyOpportunities}
            >
              <MyOpportunitiesIcon width={24} height={24} />
              <View style={styles.settingContent}>
                <Text style={styles.settingTitle}>
                  {t("settings.myOpportunities", "Minhas oportunidades")}
                </Text>
              </View>
              <NewChevronRightIcon width={16} height={16} />
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.settingItem]}
              onPress={handlePurchaseHistory}
            >
              <PurchaseHistoryIcon width={24} height={24} />
              <View style={styles.settingContent}>
                <Text style={styles.settingTitle}>
                  {t("settings.purchaseHistory", "Histórico de compras")}
                </Text>
              </View>
              <NewChevronRightIcon width={16} height={16} />
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.settingItem, styles.settingItemLast]}
              onPress={handleMyProducts}
            >
              <MyProductsIcon width={24} height={24} />
              <View style={styles.settingContent}>
                <Text style={styles.settingTitle}>
                  {t("settings.myProducts", "Meus produtos")}
                </Text>
              </View>
              <NewChevronRightIcon width={16} height={16} />
            </TouchableOpacity>
          </View>
        </View>

        {/* Sobre o aplicativo */}
        <View style={styles.sectionContainer}>
          <Text style={styles.sectionTitle}>
            {t("settings.aboutApp", "Sobre o aplicativo")}
          </Text>
          <View style={styles.settingsList}>
            <TouchableOpacity
              style={[styles.settingItem]}
              onPress={handleSupportAndHelp}
            >
              <SupportHelpIcon width={24} height={24} />
              <View style={styles.settingContent}>
                <Text style={styles.settingTitle}>
                  {t("settings.supportAndHelp", "Suporte e ajuda")}
                </Text>
              </View>
              <NewChevronRightIcon width={16} height={16} />
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.settingItem]}
              onPress={handleRateApp}
            >
              <AppRatingIcon width={24} height={24} />
              <View style={styles.settingContent}>
                <Text style={styles.settingTitle}>
                  {t("settings.rateApp", "Avaliar o aplicativo")}
                </Text>
              </View>
              <NewChevronRightIcon width={16} height={16} />
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.settingItem, styles.settingItemLast]}
              onPress={handleAppInfo}
            >
              <AppInfoIcon width={24} height={24} />
              <View style={styles.settingContent}>
                <Text style={styles.settingTitle}>
                  {t("settings.appInfo", "Informações do aplicativo")}
                </Text>
              </View>
              <NewChevronRightIcon width={16} height={16} />
            </TouchableOpacity>
          </View>
        </View>

        {/* Logout Button */}
        <TouchableOpacity
          style={[
            styles.logoutButton,
            isLoading && styles.logoutButtonDisabled
          ]}
          onPress={handleLogout}
          disabled={isLoading}
        >
          {isLoading ? (
            <ActivityIndicator size="small" color="#F97066" />
          ) : (
            <NewLogoutIcon width={24} height={24} replaceColor="#F97066" />
          )}
          <Text style={styles.logoutText}>
            {isLoading
              ? t("settings.loggingOut", "Saindo...")
              : t("settings.logout", "Sair da conta")}
          </Text>
        </TouchableOpacity>
      </ScrollView>
    </ScreenWithHeader>
  );
};

export default GeneralSettings;
