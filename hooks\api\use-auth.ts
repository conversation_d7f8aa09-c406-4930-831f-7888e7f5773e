/**
 * Hooks React Query para autenticação
 */

import {
  useMutation,
  useQuery,
  useQueryClient,
  UseQueryOptions,
  UseMutationOptions
} from "@tanstack/react-query";
import {useRouter} from "expo-router";
import {useCallback} from "react";
import AuthService from "@/services/api/auth/auth.service";
import {
  LoginRequest,
  LoginResponse,
  LogoutRequest,
  LogoutResponse,
  ForgotPasswordRequest,
  ForgotPasswordResponse,
  ResetPasswordRequest,
  ResetPasswordResponse,
  ChangePasswordRequest,
  ChangePasswordResponse,
  TokenValidationResponse,
  UserInfo,
  BiometricAuthRequest,
  BiometricAuthResponse,
  DeviceRegistrationRequest,
  DeviceRegistrationResponse
} from "@/models/api/auth.models";
import {BaseApiError} from "@/services/api/base/api-errors";

// Chaves para cache do React Query
export const authKeys = {
  all: ["auth"] as const,
  user: () => [...authKeys.all, "user"] as const,
  validation: () => [...authKeys.all, "validation"] as const,
  status: () => [...authKeys.all, "status"] as const
} as const;

/**
 * Hook para obter informações do usuário atual
 */
export const useCurrentUser = (
  options?: UseQueryOptions<UserInfo, BaseApiError>
) => {
  return useQuery({
    queryKey: authKeys.user(),
    queryFn: () => AuthService.getCurrentUser(),
    staleTime: 5 * 60 * 1000, // 5 minutos
    gcTime: 10 * 60 * 1000, // 10 minutos
    retry: (failureCount, error) => {
      // Não tentar novamente se for erro de autenticação
      if (error?.status === 401) return false;
      return failureCount < 2;
    },
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    enabled: true, // Habilitado para debug do fluxo de pagamentos
    ...options
  });
};

/**
 * Hook para validar token atual
 */
export const useTokenValidation = (
  options?: UseQueryOptions<TokenValidationResponse, BaseApiError>
) => {
  return useQuery({
    queryKey: authKeys.validation(),
    queryFn: () => AuthService.validateToken(),
    staleTime: 2 * 60 * 1000, // 2 minutos
    gcTime: 5 * 60 * 1000, // 5 minutos
    retry: false, // Não tentar novamente para validação
    ...options
  });
};

/**
 * Hook para verificar sessão ativa
 */
export const useActiveSession = (
  options?: UseQueryOptions<boolean, BaseApiError>
) => {
  return useQuery({
    queryKey: authKeys.status(),
    queryFn: () => AuthService.hasActiveSession(),
    staleTime: 1 * 60 * 1000, // 1 minuto
    gcTime: 2 * 60 * 1000, // 2 minutos
    retry: false,
    ...options
  });
};

/**
 * Hook para login
 */
export const useLogin = (
  options?: UseMutationOptions<LoginResponse, BaseApiError, LoginRequest>
) => {
  const queryClient = useQueryClient();
  const router = useRouter();

  return useMutation({
    mutationFn: (request: LoginRequest) => AuthService.login(request),
    onSuccess: (data, variables) => {
      // Invalidar e refetch dados do usuário
      queryClient.invalidateQueries({queryKey: authKeys.user()});
      queryClient.invalidateQueries({queryKey: authKeys.validation()});
      queryClient.invalidateQueries({queryKey: authKeys.status()});

      // Navegar para home se login for bem-sucedido
      if (data.accessToken) {
        router.replace("/(tabs)/home");
      }
    },
    onError: (error) => {
      // Limpar dados de autenticação em caso de erro
      queryClient.removeQueries({queryKey: authKeys.all});
    },
    ...options
  });
};

/**
 * Hook para logout
 */
export const useLogout = (
  options?: UseMutationOptions<
    LogoutResponse,
    BaseApiError,
    LogoutRequest | void
  >
) => {
  const queryClient = useQueryClient();
  const router = useRouter();

  return useMutation({
    mutationFn: (request?: LogoutRequest) => AuthService.logout(request),
    onSuccess: () => {
      // Limpar todos os dados do cache
      queryClient.clear();

      // Navegar para tela de login
      router.replace("/(auth)/login");
    },
    onSettled: () => {
      // Sempre limpar dados de autenticação, mesmo em caso de erro
      queryClient.removeQueries({queryKey: authKeys.all});
    },
    ...options
  });
};

/**
 * Hook para renovação de token
 */
export const useRefreshToken = (
  options?: UseMutationOptions<any, BaseApiError, void>
) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: () => AuthService.refreshToken(),
    onSuccess: () => {
      // Invalidar dados de autenticação para refetch
      queryClient.invalidateQueries({queryKey: authKeys.validation()});
      queryClient.invalidateQueries({queryKey: authKeys.status()});
    },
    ...options
  });
};

/**
 * Hook para recuperação de senha
 */
export const useForgotPassword = (
  options?: UseMutationOptions<
    ForgotPasswordResponse,
    BaseApiError,
    ForgotPasswordRequest
  >
) => {
  return useMutation({
    mutationFn: (request: ForgotPasswordRequest) =>
      AuthService.forgotPassword(request),
    ...options
  });
};

/**
 * Hook para redefinição de senha
 */
export const useResetPassword = (
  options?: UseMutationOptions<
    ResetPasswordResponse,
    BaseApiError,
    ResetPasswordRequest
  >
) => {
  const router = useRouter();

  return useMutation({
    mutationFn: (request: ResetPasswordRequest) =>
      AuthService.resetPassword(request),
    onSuccess: () => {
      // Navegar para login após redefinir senha
      router.replace("/(auth)/login");
    },
    ...options
  });
};

/**
 * Hook para alteração de senha
 */
export const useChangePassword = (
  options?: UseMutationOptions<
    ChangePasswordResponse,
    BaseApiError,
    ChangePasswordRequest
  >
) => {
  return useMutation({
    mutationFn: (request: ChangePasswordRequest) =>
      AuthService.changePassword(request),
    ...options
  });
};

/**
 * Hook para autenticação biométrica
 */
export const useBiometricAuth = (
  options?: UseMutationOptions<
    BiometricAuthResponse,
    BaseApiError,
    BiometricAuthRequest
  >
) => {
  const queryClient = useQueryClient();
  const router = useRouter();

  return useMutation({
    mutationFn: (request: BiometricAuthRequest) =>
      AuthService.biometricAuth(request),
    onSuccess: (data) => {
      if (data.success) {
        // Invalidar dados de autenticação
        queryClient.invalidateQueries({queryKey: authKeys.user()});
        queryClient.invalidateQueries({queryKey: authKeys.validation()});
        queryClient.invalidateQueries({queryKey: authKeys.status()});

        // Navegar para home
        router.replace("/(tabs)/home");
      }
    },
    ...options
  });
};

/**
 * Hook para registro de dispositivo
 */
export const useRegisterDevice = (
  options?: UseMutationOptions<
    DeviceRegistrationResponse,
    BaseApiError,
    DeviceRegistrationRequest
  >
) => {
  return useMutation({
    mutationFn: (request: DeviceRegistrationRequest) =>
      AuthService.registerDevice(request),
    ...options
  });
};

/**
 * Hook para obter status de autenticação (síncrono)
 */
export const useAuthStatus = () => {
  return useCallback(() => {
    return AuthService.getAuthStatus();
  }, []);
};

/**
 * Hook para ações de autenticação (combinado)
 */
export const useAuthActions = () => {
  const loginMutation = useLogin();
  const logoutMutation = useLogout();
  const refreshMutation = useRefreshToken();
  const forgotPasswordMutation = useForgotPassword();
  const resetPasswordMutation = useResetPassword();
  const changePasswordMutation = useChangePassword();
  const biometricAuthMutation = useBiometricAuth();
  const registerDeviceMutation = useRegisterDevice();

  return {
    // Mutations
    login: loginMutation.mutate,
    logout: logoutMutation.mutate,
    refreshToken: refreshMutation.mutate,
    forgotPassword: forgotPasswordMutation.mutate,
    resetPassword: resetPasswordMutation.mutate,
    changePassword: changePasswordMutation.mutate,
    biometricAuth: biometricAuthMutation.mutate,
    registerDevice: registerDeviceMutation.mutate,

    // Estados
    isLoggingIn: loginMutation.isPending,
    isLoggingOut: logoutMutation.isPending,
    isRefreshing: refreshMutation.isPending,
    isSendingForgotPassword: forgotPasswordMutation.isPending,
    isResettingPassword: resetPasswordMutation.isPending,
    isChangingPassword: changePasswordMutation.isPending,
    isBiometricAuth: biometricAuthMutation.isPending,
    isRegisteringDevice: registerDeviceMutation.isPending,

    // Erros
    loginError: loginMutation.error,
    logoutError: logoutMutation.error,
    refreshError: refreshMutation.error,
    forgotPasswordError: forgotPasswordMutation.error,
    resetPasswordError: resetPasswordMutation.error,
    changePasswordError: changePasswordMutation.error,
    biometricAuthError: biometricAuthMutation.error,
    registerDeviceError: registerDeviceMutation.error,

    // Reset functions
    resetLoginError: loginMutation.reset,
    resetLogoutError: logoutMutation.reset,
    resetRefreshError: refreshMutation.reset,
    resetForgotPasswordError: forgotPasswordMutation.reset,
    resetResetPasswordError: resetPasswordMutation.reset,
    resetChangePasswordError: changePasswordMutation.reset,
    resetBiometricAuthError: biometricAuthMutation.reset,
    resetRegisterDeviceError: registerDeviceMutation.reset
  };
};

/**
 * Hook para verificar se usuário está autenticado
 */
export const useIsAuthenticated = () => {
  const {data: hasActiveSession, isLoading} = useActiveSession();
  const authStatus = useAuthStatus();

  return {
    isAuthenticated: hasActiveSession ?? authStatus().isAuthenticated,
    isLoading,
    tokenStatus: authStatus().tokenStatus
  };
};
