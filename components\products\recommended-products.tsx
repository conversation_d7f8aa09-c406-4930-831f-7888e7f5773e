import React, {useCallback, useState, useEffect, useMemo} from "react";
import RecommendedProductCard from "./recommended-product-card";
import ProductDetailModal from "./product-detail-modal";
import {
  View,
  Text,
  FlatList,
  ActivityIndicator,
  ColorValue,
  ListRenderItemInfo,
  TouchableOpacity
} from "react-native";
import styles from "@/styles/components/products/recomended-products.style";
import BookIcon from "@/components/icons/book-icon";
import {Product, ProductCategory} from "@/models/product";
import InputField from "@/components/input-field";
import SearchIcon from "@/components/icons/search-icon";
import Partners from "./partners";
import ProductCategories from "./product-categories";
import {useTranslation} from "react-i18next";
import {useRouter} from "expo-router";
import useCatalog, {useProductSearch} from "@/hooks/use-catalog";
import {useUpsellDrawer} from "@/hooks/use-upsell-drawer";

export interface RecommendedProductsProps {}

/**
 * RecommendedProducts component that displays a list of products with search,
 * filtering, and pagination functionality. Includes product detail modal.
 *
 * Features:
 * - Debounced search functionality
 * - Category filtering
 * - Infinite scrolling with pagination
 * - Product detail modal
 * - Error handling and retry mechanisms
 * - Performance optimizations
 */
const RecommendedProducts: React.FC<RecommendedProductsProps> = () => {
  const {t} = useTranslation();
  const router = useRouter();
  const {interceptGuestAction} = useUpsellDrawer();

  // Search state management
  const [searchTerm, setSearchTerm] = useState("");
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<
    ProductCategory | undefined
  >(undefined);

  // Product detail modal state
  const [selectedProductId, setSelectedProductId] = useState<number | null>(
    null
  );
  const [isModalVisible, setIsModalVisible] = useState(false);

  // Debounce search term
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
    }, 500); // 500ms debounce

    return () => clearTimeout(timer);
  }, [searchTerm]);

  // Use search hook when there's a search term, otherwise use catalog
  const searchQuery = useProductSearch(
    debouncedSearchTerm,
    selectedCategory,
    10,
    debouncedSearchTerm.length >= 2
  );

  const {catalogQuery} = useCatalog(
    1,
    10,
    debouncedSearchTerm,
    selectedCategory
  );

  // Use search results if searching, otherwise use catalog
  const activeQuery =
    debouncedSearchTerm.length >= 2 ? searchQuery : catalogQuery;

  // Memoize flattened data for performance
  const flattenedData = useMemo(() => {
    const data = activeQuery.data?.pages?.flatMap((x) => x.data) || [];
    // Debug: Log product IDs to understand the data structure
    if (data.length > 0) {
      console.log(
        "🔍 Product IDs from catalog:",
        data.map((p) => ({id: p.id, name: p.name}))
      );
    }
    return data;
  }, [activeQuery.data]);

  // Memoize key extractor for performance
  const keyExtractor = useCallback((item: Product) => item.id.toString(), []);

  // Optimize getItemLayout for better scrolling performance
  const getItemLayout = useCallback(
    (_: any, index: number) => ({
      length: 200, // Approximate item height (adjust based on your design)
      offset: 200 * index,
      index
    }),
    []
  );

  // Modal handlers
  const handleProductPress = useCallback((productId: number) => {
    setSelectedProductId(productId);
    setIsModalVisible(true);
  }, []);

  const handleModalClose = useCallback(() => {
    setIsModalVisible(false);
    setSelectedProductId(null);
  }, []);

  const handlePurchase = useCallback(
    (productId: number) => {
      console.log(
        "🛒 [RECOMMENDED-PRODUCTS] Iniciando compra do produto:",
        productId
      );

      // Buscar dados do produto para navegação
      const product = flattenedData.find((p) => p.id === productId);

      if (!product) {
        console.error("❌ Produto não encontrado para compra:", productId);
        console.log(
          "📋 Produtos disponíveis:",
          flattenedData.map((p) => ({id: p.id, name: p.name}))
        );
        return;
      }

      // Interceptar ação para usuários guest
      const wasIntercepted = interceptGuestAction(() => {
        // Parâmetros para a tela de pagamento (corrigido para produtos)
        const paymentParams = {
          entityId: productId.toString(), // Usando entityId correto para produtos
          entity: "0", // PaymentEntity.Product = 0
          entityTitle: product.name || "Produto",
          entityDescription: product.description || "",
          entityImageUrl: "",
          amount: ((product.value || 0) / 100).toString() // Converter de centavos para reais
        };

        console.log(
          "💰 [RECOMMENDED-PRODUCTS] Navegando para pagamento com parâmetros:",
          paymentParams
        );

        // Fechar modal e navegar para pagamento
        handleModalClose();

        router.push({
          pathname: "/(logged-stack)/payment",
          params: paymentParams
        });
      });

      if (wasIntercepted) {
        console.log(
          "🚫 [RECOMMENDED-PRODUCTS] Ação interceptada para usuário guest"
        );
        // Fechar modal mesmo quando interceptado
        handleModalClose();
      }
    },
    [handleModalClose, flattenedData, router, interceptGuestAction]
  );

  // Category selection handler with toggle functionality
  const handleCategorySelect = useCallback(
    (category: ProductCategory) => {
      // If the same category is selected, deselect it (toggle functionality)
      if (selectedCategory === category) {
        setSelectedCategory(undefined);
      } else {
        setSelectedCategory(category);
      }
    },
    [selectedCategory]
  );

  const handleLoadMore = useCallback(async () => {
    if (activeQuery.hasNextPage && !activeQuery.isLoading) {
      await activeQuery.fetchNextPage();
    }
  }, [activeQuery]);

  const handleRefresh = useCallback(async () => {
    await activeQuery.refetch();
  }, [activeQuery]);

  const renderItem = useCallback((item: ListRenderItemInfo<Product>) => {
    const product = item.item;
    return (
      <RecommendedProductCard
        key={product.id}
        productId={product.id}
        title={product.name || ""}
        description={product.description || ""}
        price={product.value}
        category={product.category}
        periodicity={product.periodicity}
        isActive={product.isActive}
        icon={<BookIcon />}
        onPress={handleProductPress}
      />
    );
  }, []);

  const renderFooter = useCallback(() => {
    if (activeQuery.isFetchingNextPage) {
      return (
        <View style={styles.loadingFooter}>
          <ActivityIndicator size="small" color="#ffffff" />
          <Text style={styles.loadingText}>
            {t("recommendedProducts.loadingMore")}
          </Text>
        </View>
      );
    }

    if (activeQuery.hasNextPage && activeQuery.error) {
      return (
        <View style={styles.loadingFooter}>
          <TouchableOpacity style={styles.retryButton} onPress={handleLoadMore}>
            <Text style={styles.retryButtonText}>
              {t("recommendedProducts.loadMoreRetry")}
            </Text>
          </TouchableOpacity>
        </View>
      );
    }

    return null;
  }, [activeQuery, t, handleLoadMore]);

  const renderEmpty = useCallback(() => {
    if (activeQuery.isLoading) {
      return (
        <View style={styles.emptyContainer}>
          <ActivityIndicator size="large" color="#ffffff" />
          <Text style={styles.emptyText}>
            {debouncedSearchTerm.length >= 2
              ? t("recommendedProducts.searchingProducts")
              : t("recommendedProducts.loadingProducts")}
          </Text>
        </View>
      );
    }

    if (activeQuery.error) {
      return (
        <View style={styles.emptyContainer}>
          <Text style={styles.errorTitle}>
            {t("recommendedProducts.error.title")}
          </Text>
          <Text style={styles.errorMessage}>
            {t("recommendedProducts.error.message")}
          </Text>
          <TouchableOpacity style={styles.retryButton} onPress={handleRefresh}>
            <Text style={styles.retryButtonText}>
              {t("recommendedProducts.retry")}
            </Text>
          </TouchableOpacity>
        </View>
      );
    }

    return (
      <View style={styles.emptyContainer}>
        <Text style={styles.emptyText}>
          {debouncedSearchTerm.length >= 2
            ? t("recommendedProducts.noSearchResults")
            : t("recommendedProducts.noProducts")}
        </Text>
        {debouncedSearchTerm.length >= 2 && (
          <TouchableOpacity
            style={styles.clearSearchButton}
            onPress={() => setSearchTerm("")}
          >
            <Text style={styles.clearSearchButtonText}>
              {t("recommendedProducts.clearSearch")}
            </Text>
          </TouchableOpacity>
        )}
      </View>
    );
  }, [activeQuery, debouncedSearchTerm, t, handleRefresh, setSearchTerm]);

  const searchIcon = useCallback((_?: ColorValue) => <SearchIcon />, []);

  const renderHeader = useCallback(
    () => (
      <View style={styles.headerContent}>
        <InputField
          placeholder={t("recommendedProducts.searchPlaceholder")}
          value={searchTerm}
          onChangeText={setSearchTerm}
          icon={searchIcon}
        />
        <Partners />
        <ProductCategories
          selectedCategory={selectedCategory}
          onCategorySelect={handleCategorySelect}
        />
        <View style={styles.sectionHeader}>
          <Text style={styles.title}>
            {debouncedSearchTerm.length >= 2
              ? t("recommendedProducts.searchResults")
              : t("recommendedProducts.recommended")}
          </Text>
        </View>
      </View>
    ),
    [
      searchIcon,
      searchTerm,
      setSearchTerm,
      debouncedSearchTerm,
      selectedCategory,
      handleCategorySelect,
      t
    ]
  );

  return (
    <>
      <FlatList
        data={flattenedData}
        renderItem={renderItem}
        keyExtractor={keyExtractor}
        getItemLayout={getItemLayout}
        contentContainerStyle={styles.fullListContentContainer}
        showsVerticalScrollIndicator={false}
        ListHeaderComponent={renderHeader}
        onEndReached={handleLoadMore}
        onEndReachedThreshold={0.1}
        refreshing={activeQuery.isLoading}
        onRefresh={handleRefresh}
        ListFooterComponent={renderFooter}
        ListEmptyComponent={renderEmpty}
        removeClippedSubviews={true}
        maxToRenderPerBatch={8}
        windowSize={8}
        initialNumToRender={6}
        updateCellsBatchingPeriod={50}
        disableVirtualization={false}
      />

      <ProductDetailModal
        productId={selectedProductId}
        visible={isModalVisible}
        onClose={handleModalClose}
        onPurchase={handlePurchase}
      />
    </>
  );
};

export default RecommendedProducts;
