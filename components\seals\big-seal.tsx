import {ImageSource} from "expo-image";
import React from "react";
import {LinearGradient} from "expo-linear-gradient";
import styles from "../../styles/components/seals/big-seal.style";
import SmallSeal from "./small-seal";
import SealSVG from "./seal-svg";
import {BadgeType} from "@/models/api/user-badges.models";

export interface BigSealProps {
  source?: ImageSource;
  badgeType?: BadgeType;
  variant?: "square" | "circle";
  level?: number; // Novo prop para controlar o nível do badge
}

const BigSeal: React.FC<BigSealProps> = (props) => {
  const {source, badgeType, variant = "square", level = 0} = props;

  const renderContent = () => {
    if (badgeType) {
      return (
        <SealSVG
          badgeType={badgeType}
          size={48}
          variant={variant}
          level={level}
        />
      );
    }
    if (source) {
      return <SmallSeal source={source} />;
    }
    return (
      <SealSVG
        badgeType={BadgeType.DIAMOND}
        size={48}
        variant={variant}
        level={level}
      />
    );
  };

  return (
    <LinearGradient
      colors={["rgba(255,255,255,0)", "rgba(255,255,255,0.15)"]}
      locations={[0.6, 1]}
      start={{x: 0.5, y: 0}}
      end={{x: 0.5, y: 1}}
      style={[styles.container]}
    >
      {renderContent()}
    </LinearGradient>
  );
};

export default BigSeal;
