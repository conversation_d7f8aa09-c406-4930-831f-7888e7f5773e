import React, {useState, useCallback, useEffect, useMemo} from "react";
import {
  Text,
  View,
  TouchableOpacity,
  ImageBackground,
  Alert,
  ScrollView
} from "react-native";
import * as DocumentPicker from "expo-document-picker";
import * as ImageManipulator from "expo-image-manipulator";
import Screen from "../../components/screen";
import BackButton from "../../components/back-button";
import {useTranslation} from "react-i18next";
import {useRouter} from "expo-router";
import FullSizeButton from "../../components/full-size-button";
import CheckIcon from "../../components/icons/check-icon";
import PdfIcon from "../../components/icons/pdf-icon";
import TrashIcon from "../../components/icons/trash-icon";
import styles from "@/styles/registration/document-upload-terms.style";
import {useDocumentUploadTermsValidation} from "@/hooks/use-registration-validation";
import {useRegistrationContext} from "@/contexts/RegistrationContext";
import {useTitleTerms} from "@/hooks/api/use-title-terms";
import {useTermById} from "@/hooks/api/use-term-by-id";
import {useApplicationUseTerm} from "@/hooks/api/use-application-use-term";
import BottomModal from "@/components/bottom-modal";
import {useBottomModal} from "@/contexts/bottom-modal-context";
import {ApiTerm, getLatestVersionFromApiTerm} from "@/models/api/terms.models";

interface SelectedDocument {
  uri: string;
  name: string;
  type: string;
  size: number;
  processedUri?: string;
}

const convertImageToWebP = async (imageUri: string): Promise<string> => {
  try {
    const manipulatedImage = await ImageManipulator.manipulateAsync(
      imageUri,
      [{resize: {width: 800}}],
      {
        compress: 0.8,
        format: ImageManipulator.SaveFormat.WEBP,
        base64: false
      }
    );

    if (!manipulatedImage.uri) {
      throw new Error("Falha ao obter URI da imagem WebP");
    }

    return manipulatedImage.uri;
  } catch (error) {
    throw new Error("Falha ao converter imagem para WebP");
  }
};

const DocumentUploadTerms: React.FC = () => {
  const {t} = useTranslation();
  const router = useRouter();
  const {getAllData} = useRegistrationContext();

  const contextData = getAllData();

  // Buscar termos do título
  const {
    data: titleTerms,
    isLoading: isLoadingTerms,
    error: termsError
  } = useTitleTerms(true);

  // Termos adicionais: Plano selecionado e Termo de Uso do Aplicativo
  const selectedPlanTermId = contextData?.selectedPlanTermId
    ? parseInt(contextData.selectedPlanTermId as string)
    : null;

  // Carregar termo do plano pelo termId recebido do primeiro passo
  const {data: planTerm} = useTermById(selectedPlanTermId);

  const {data: applicationUseTerm} = useApplicationUseTerm(true);

  // Drawer de leitura dos termos (BottomModal)
  const modal = useBottomModal();
  const cleanHtmlContent = useCallback((html: string) => {
    return html
      .replace(/<[^>]*>/g, "")
      .replace(/&nbsp;/g, " ")
      .replace(/&amp;/g, "&")
      .replace(/&lt;/g, "<")
      .replace(/&gt;/g, ">")
      .replace(/&quot;/g, '"')
      .trim();
  }, []);
  const openTermDrawer = useCallback(
    (term: ApiTerm) => {
      const latest = getLatestVersionFromApiTerm(term);
      const raw = latest?.content || (term as any)?.content || "";
      const content = cleanHtmlContent(raw || "");

      modal.openModal({
        title: term.name,
        children: (
          <BottomModal.Container>
            <ScrollView
              style={{maxHeight: 400, paddingHorizontal: 16}}
              showsVerticalScrollIndicator={false}
            >
              <Text
                style={{
                  color: "#FFFFFF",
                  fontSize: 14,
                  lineHeight: 20,
                  fontFamily: "Ubuntu",
                  marginBottom: 16
                }}
              >
                {content}
              </Text>
            </ScrollView>
          </BottomModal.Container>
        )
      });
    },
    [modal, cleanHtmlContent]
  );

  // Unificar todos os termos exigidos nesta etapa
  const allTerms: ApiTerm[] = useMemo(() => {
    const list: ApiTerm[] = [];
    if (Array.isArray(titleTerms) && titleTerms.length)
      list.push(...titleTerms);
    if (planTerm) list.push(planTerm as ApiTerm);
    if (applicationUseTerm) list.push(applicationUseTerm as ApiTerm);
    const unique = new Map<number, ApiTerm>();
    list.forEach((t) => unique.set(t.id, t));
    return Array.from(unique.values());
  }, [titleTerms, planTerm, applicationUseTerm]);

  // Mapear aceitações dinâmicas por termoId
  const [acceptedTermIds, setAcceptedTermIds] = useState<
    Record<number, boolean>
  >({});

  const validation = useDocumentUploadTermsValidation(
    {
      acceptedTerms: true,
      acceptedPrivacy: true,
      acceptedAdesao: true,
      documentUploaded: false
    },
    (validData) => {
      const allRegistrationData = getAllData();

      // Preparar lista de termos aceitos com id e version
      const acceptedTermsList = allTerms
        .map((term) => {
          const latest = getLatestVersionFromApiTerm(term);
          return acceptedTermIds[term.id] && latest
            ? {id: term.id, version: latest.version}
            : null;
        })
        .filter(Boolean);

      // Primeiro criar o usuário, depois navegar para pagamento do título
      router.push({
        pathname: "/(registration)/user-creation",
        params: {
          ...allRegistrationData,
          selectedDocuments: JSON.stringify(selectedDocuments),
          acceptedTermsList: JSON.stringify(acceptedTermsList)
        }
      });
    }
  );

  // Removido o trio de checkboxes estáticos: deixaremos apenas os termos dinâmicos
  const [acceptedTerms] = useState(true);
  const [acceptedPrivacy] = useState(true);
  const [acceptedAdesao] = useState(true);
  const [selectedDocuments, setSelectedDocuments] = useState<
    SelectedDocument[]
  >([]);

  useEffect(() => {}, []);

  // Sem dependência dos checkboxes estáticos (foram removidos)
  useEffect(() => {}, []);

  const handleDocumentUpload = useCallback(async () => {
    try {
      const result = await DocumentPicker.getDocumentAsync({
        type: ["image/*", "application/pdf"],
        multiple: true,
        copyToCacheDirectory: true
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        const processedDocuments: SelectedDocument[] = [];

        for (let i = 0; i < result.assets.length; i++) {
          const asset = result.assets[i];

          const document: SelectedDocument = {
            uri: asset.uri,
            name: asset.name,
            type: asset.mimeType || "application/octet-stream",
            size: asset.size || 0
          };

          if (asset.mimeType && asset.mimeType.startsWith("image/")) {
            try {
              const processedUri = await convertImageToWebP(asset.uri);
              document.processedUri = processedUri;
              document.type = "image/webp";
            } catch (error) {}
          }

          processedDocuments.push(document);
        }

        setSelectedDocuments((prev) => {
          const updated = [...prev, ...processedDocuments];
          return updated;
        });

        Alert.alert(
          "Sucesso",
          `${processedDocuments.length} documento(s) processado(s) com sucesso!`
        );
      }
    } catch (error) {
      Alert.alert("Erro", "Falha ao selecionar documentos. Tente novamente.");
    }
  }, []);

  const handleRemoveDocument = useCallback((index: number) => {
    setSelectedDocuments((prev) => {
      const updated = prev.filter((_, i) => i !== index);
      return updated;
    });
  }, []);

  const handleNext = useCallback(() => {
    // Validar termos dinâmicos: todos devem ser aceitos
    if (allTerms && allTerms.length > 0) {
      const notAccepted = allTerms.filter((t) => !acceptedTermIds[t.id]);
      if (notAccepted.length > 0) {
        Alert.alert(
          t("documentUploadTerms.error.title", "Erro de validação"),
          t(
            "documentUploadTerms.error.acceptAllTerms",
            "Você deve aceitar todos os termos exibidos para continuar"
          )
        );
        return;
      }
    }

    validation.updateData({
      acceptedTerms,
      acceptedPrivacy,
      acceptedAdesao,
      documentUploaded: selectedDocuments.length > 0
    });

    const isValid = validation.handleSubmit();
    if (!isValid) {
      const errorMessages = Object.values(validation.errors).join("\n");
      Alert.alert(
        t("documentUploadTerms.error.title", "Erro de validação"),
        errorMessages ||
          t(
            "documentUploadTerms.error.acceptTerms",
            "Você deve aceitar todos os termos para continuar"
          )
      );
    }
  }, [
    titleTerms,
    acceptedTermIds,
    validation,
    acceptedTerms,
    acceptedPrivacy,
    acceptedAdesao,
    selectedDocuments,
    t
  ]);

  const handleBack = useCallback(() => {
    router.back();
  }, [router]);

  return (
    <Screen>
      <ImageBackground
        source={require("../../assets/images/backgroundregistration.png")}
        style={styles.backgroundImage}
        imageStyle={styles.backgroundImageStyle}
      >
        <View style={styles.overlay} />
        <View style={styles.contentContainer}>
          <BackButton />

          <View style={styles.progressContainer}>
            <Text style={styles.progressTitle}>
              {t("documentUploadTerms.createAccount", "Criar conta")}
            </Text>
            <Text style={styles.progressStep}>
              {t(
                "documentUploadTerms.stepProgress",
                "6 / 7 Documentação e termos"
              )}
            </Text>
            <View style={styles.progressBar}>
              <View style={[styles.progressFill, {width: "86%"}]} />
            </View>
          </View>

          <View style={styles.headerContainer}>
            <Text style={styles.title}>
              {t(
                "documentUploadTerms.title",
                "Anexe seu documento de identificação e aceite os termos."
              )}
            </Text>
          </View>

          <View style={styles.documentsSection}>
            <Text style={styles.sectionTitle}>
              {t(
                "documentUploadTerms.documentType",
                "Anexar documento (RG, CNH)"
              )}
            </Text>

            <TouchableOpacity
              style={styles.uploadArea}
              onPress={handleDocumentUpload}
            >
              <View style={styles.uploadIconContainer}>
                <PdfIcon width={24} height={24} color="#FFFFFF" />
              </View>
              <Text style={styles.uploadText}>
                {selectedDocuments.length > 0
                  ? t(
                      "documentUploadTerms.addMoreFiles",
                      "Adicionar mais arquivos"
                    )
                  : t("documentUploadTerms.uploadFile", "Anexar arquivo")}
              </Text>
            </TouchableOpacity>

            <Text style={styles.fileSizeText}>
              {t("documentUploadTerms.maxSize", "Tamanho máximo: 200kb")}
            </Text>

            {/* Termos dinâmicos do título */}
            {isLoadingTerms && (
              <View style={{padding: 16}}>
                <Text style={{color: "#FFFFFF"}}>
                  {t(
                    "documentUploadTerms.loadingTerms",
                    "Carregando termos..."
                  )}
                </Text>
              </View>
            )}

            {termsError && (
              <View style={{padding: 16}}>
                <Text style={{color: "#FF6B6B"}}>
                  {t(
                    "documentUploadTerms.error.loadTerms",
                    "Não foi possível carregar os termos. Tente novamente."
                  )}
                </Text>
              </View>
            )}

            {allTerms.length > 0 && (
              <View style={{marginTop: 12}}>
                {allTerms.map((term) => {
                  const latest = getLatestVersionFromApiTerm(term);
                  const checked = !!acceptedTermIds[term.id];
                  const toggle = () =>
                    setAcceptedTermIds((prev) => ({
                      ...prev,
                      [term.id]: !checked
                    }));

                  return (
                    <View
                      key={term.id}
                      style={{
                        backgroundColor: "#2A2A2A",
                        borderRadius: 12,
                        padding: 12,
                        marginBottom: 12
                      }}
                    >
                      <View
                        style={{flexDirection: "row", alignItems: "center"}}
                      >
                        <Text
                          style={{
                            color: "#FFFFFF",
                            fontWeight: "bold",
                            flex: 1
                          }}
                        >
                          {term.name} {latest ? `(v${latest.version})` : ""}
                        </Text>
                        <TouchableOpacity onPress={() => openTermDrawer(term)}>
                          <Text
                            style={[
                              styles.linkText,
                              {textDecorationLine: "underline"}
                            ]}
                          >
                            Ver
                          </Text>
                        </TouchableOpacity>
                      </View>

                      <ScrollView
                        style={{maxHeight: 120, marginTop: 8}}
                        nestedScrollEnabled
                        showsVerticalScrollIndicator
                      >
                        <Text style={{color: "#CCCCCC", lineHeight: 20}}>
                          {latest
                            ? cleanHtmlContent(latest.content)
                            : t(
                                "documentUploadTerms.noContent",
                                "Sem conteúdo"
                              )}
                        </Text>
                      </ScrollView>

                      <TouchableOpacity
                        style={[styles.checkboxContainer, {marginTop: 8}]}
                        onPress={toggle}
                      >
                        <View
                          style={[
                            styles.checkbox,
                            checked && styles.checkboxChecked
                          ]}
                        >
                          {checked && <CheckIcon replaceColor="#E3691B" />}
                        </View>
                        <Text style={styles.checkboxText}>
                          {t(
                            "documentUploadTerms.acceptThisTerm",
                            "Declaro que li e concordo com este termo"
                          )}
                        </Text>
                      </TouchableOpacity>
                    </View>
                  );
                })}
              </View>
            )}

            {/* Lista de documentos selecionados */}

            {selectedDocuments.length > 0 && (
              <View style={styles.selectedDocumentsContainer}>
                <Text style={styles.selectedDocumentsTitle}>
                  {t(
                    "documentUploadTerms.selectedDocuments",
                    "Documentos selecionados"
                  )}{" "}
                  ({selectedDocuments.length})
                </Text>
                <ScrollView
                  style={styles.documentsScrollView}
                  nestedScrollEnabled
                >
                  {selectedDocuments.map((document, index) => (
                    <View key={index} style={styles.documentItem}>
                      <View style={styles.documentInfo}>
                        <PdfIcon width={20} height={20} color="#E3691B" />
                        <View style={styles.documentDetails}>
                          <Text style={styles.documentName} numberOfLines={1}>
                            {document.name}
                          </Text>
                          <Text style={styles.documentSize}>
                            {(document.size / 1024).toFixed(1)} KB
                          </Text>
                        </View>
                      </View>
                      <TouchableOpacity
                        style={styles.removeButton}
                        onPress={() => handleRemoveDocument(index)}
                      >
                        <TrashIcon width={16} height={16} color="#FF3B30" />
                      </TouchableOpacity>
                    </View>
                  ))}
                </ScrollView>
              </View>
            )}
          </View>

          {/* Removido bloco de 3 checkboxes estáticos; usamos apenas os termos dinâmicos acima */}

          <View style={styles.buttonContainer}>
            <View style={styles.backButton}>
              <FullSizeButton
                text={t("documentUploadTerms.back", "Voltar")}
                variant="secondary"
                onPress={handleBack}
              />
            </View>
            <View style={styles.nextButton}>
              <FullSizeButton
                text={t("documentUploadTerms.next", "Avançar")}
                onPress={handleNext}
              />
            </View>
          </View>
        </View>
      </ImageBackground>
    </Screen>
  );
};

export default DocumentUploadTerms;
