/**
 * Barrel export para todos os hooks de API
 * Facilita importação e organização dos hooks React Query
 */

// Hooks de Eventos
export {
  useEvents,
  useInfiniteEvents,
  useEvent,
  useEventCategories,
  useFeaturedEvents,
  useUpcomingEvents,
  useEventsPerDay,
  useUserEvents,
  useUserEventCheckInQrCodes,
  useSimilarEvents,
  useRegisterForEvent,
  useCancelRegistration,
  useCreateEvent,
  useUpdateEvent,
  useDeleteEvent,
  eventsKeys
} from "./use-events";

// Hooks de Tickets
export {
  useUserTickets,
  useInfiniteUserTickets,
  useTicket,
  useTicketsByPeriod,
  useTicketsByStatus,
  useSearchTickets,
  useTicketStats,
  useHasTicketForEvent,
  useInvalidateTickets,
  usePrefetchTickets,
  useUnmarkAttendance,
  ticketsKeys
} from "./use-tickets";

// Hooks de Produtos
export {
  useProducts,
  useInfiniteProducts,
  useProduct,
  useRecommendedProducts,
  useFeaturedProducts,
  useProductCategories,
  useProductBrands,
  useRelatedProducts,
  useProductReviews,
  usePriceHistory,
  useProductAvailability,
  useProductsByCategory,
  useInfiniteProductsByCategory,
  useCatalog,
  // useProductById removido - use useProduct do hooks/use-product.ts ou useProductById do hooks/use-catalog.ts
  productsKeys
} from "./use-products";

// Hooks de Magazines
export {
  useMagazines,
  useInfiniteMagazines,
  useMagazine,
  useFeaturedMagazines,
  useNewMagazines,
  useMagazinesByCategory,
  useMagazinesByAuthor,
  useRelatedMagazines,
  useSearchMagazines,
  useInvalidateMagazines,
  usePrefetchMagazines,
  magazinesKeys
} from "./use-magazines";

// Hooks de Usuários
export {
  useUpdateProfile,
  useUserAddresses,
  useAddAddress,
  useUpdateAddress,
  useDeleteAddress,
  usePaymentMethods,
  useDeletePaymentMethod,
  useUserActivity,
  useUserStats,
  useUploadAvatar,
  useUserById,
  useCheckUserExists,
  useDeleteAccount,
  useUser,
  useUsers,
  usersKeys
} from "./use-users";

// Hooks de Objetivos
export {
  useObjectives,
  useObjective,
  useCreateObjective,
  useUpdateObjective,
  useDeleteObjective,
  useUserObjectivesNew,
  objectivesKeys
} from "./use-objectives";

// Hooks de Especializações
export {
  useSpecializations,
  useCreateSpecialization,
  specializationsKeys
} from "./use-specializations";

// Hooks de Áreas de Interesse
export {
  useAreaInterests,
  useCreateAreaInterest,
  areaInterestsKeys
} from "./use-area-interests";

// Hooks de Oportunidades
export {
  useOpportunities,
  useInfiniteOpportunities,
  useUserOpportunities,
  useUserOpportunitiesById,
  useOpportunity,
  useCreateOpportunity,
  useUpdateOpportunity,
  useUpdateOpportunityStatus,
  useOpportunityPublicationValue,
  useOpportunitySegments,
  useOpportunityCurrentStages,
  opportunitiesKeys
} from "./use-opportunities";

// Hooks de Home
export {
  useHomeFeed,
  useInfiniteHomeFeed,
  useHomeSearch,
  useInfiniteHomeSearch,
  useHomeUtils,
  homeKeys
} from "./use-home";

// Hooks de Planos
export {
  usePlans,
  usePlansPublic,
  usePlan,
  useFranchisesByPlan,
  plansKeys
} from "./use-plans";

// Hooks de Títulos
export {useTitleInstallmentOptions, TITLES_QUERY_KEYS} from "./use-titles";

// Hooks de Segmentos de Oportunidade
export {
  useOpportunitySegments as useOpportunitySegmentsList,
  useOpportunitySegment,
  opportunitySegmentsKeys
} from "./use-opportunity-segments";

// Hooks de Indicações
export {
  useIndications,
  useIndication,
  useCreateIndication,
  useIndicationStats,
  useInvalidateIndications
} from "./use-indications";

// Hooks de Enquetes
export {
  usePolls,
  useInfinitePolls,
  usePoll,
  useVoteOnPoll,
  useInvalidatePolls,
  usePrefetchPolls,
  pollsKeys
} from "./use-polls";

// Hooks de Notificações (Fase 3)
export {
  useNotifications,
  useInfiniteNotifications,
  useNotification,
  useNotificationStats,
  useNotificationPreferences,
  useUnreadNotifications,
  useNotificationAnalytics,
  useMarkNotificationAsRead,
  useMarkAllNotificationsAsRead,
  useArchiveNotification,
  useDeleteNotification,
  useUpdateNotificationPreferences,
  useUnregisterDevice,
  useTestPushNotification,
  useClearOldNotifications,
  useOptimisticNotificationUpdate,
  notificationsKeys
} from "./use-notifications";

// Hooks de Autenticação
export {
  useCurrentUser,
  useTokenValidation,
  useActiveSession,
  useLogin,
  useLogout,
  useRefreshToken,
  useForgotPassword,
  useResetPassword,
  useChangePassword,
  useBiometricAuth,
  useRegisterDevice,
  useAuthStatus,
  useAuthActions,
  useIsAuthenticated,
  authKeys
} from "./use-auth";

// Hooks de Endereços
export {useStates, useCitiesByState, addressesKeys} from "./use-addresses";

// Hook de tratamento de erros
export {useApiErrorHandler} from "./use-api-error-handler";

// Hooks avançados com error handling
export {
  useEnhancedQuery,
  useEnhancedMutation,
  useRetryConfig
} from "./use-enhanced-query";

// Hooks de busca
export {
  useGlobalSearch,
  useInfiniteGlobalSearch,
  useSearchMembers,
  useSearchEvents,
  useSearchProducts,
  useSearchChats,
  useSearchPartners,
  useSearchOpportunities,
  searchKeys
} from "./use-search";

// Hooks de Chat/Mensagens (Fase 3)
export {
  useChats,
  useInfiniteChats,
  useChat,
  useMessages,
  useInfiniteMessages,
  useChatMembers,
  useChatStats,
  useUnreadChats,
  useCreateChat,
  useUpdateChat,
  useDeleteChat,
  useSendMessage,
  useUpdateMessage,
  useDeleteMessage,
  useMarkMessagesAsRead,
  useAddChatMembers,
  useUploadFile,
  useStartCall,
  useLeaveChat,
  chatsKeys
} from "./use-chats";

// Hooks de Referrals/Indications (Fase 4)
export {
  useReferrals,
  useInfiniteReferrals,
  useReferral,
  useReferralStats,
  useContactSearch,
  useCreateReferral,
  useCreateBulkReferrals,
  useResendInvitation,
  useCancelReferral,
  useInvalidateReferrals,
  useUserByDocument,
  referralsKeys
} from "./use-referrals";

// Hooks de Error Handling
export {useErrorHandling} from "../use-error-handling";

// Hooks de Installment Config
export {
  useProductInstallmentConfig,
  useEventInstallmentConfig,
  useInstallmentConfig // Deprecated but kept for backward compatibility
} from "./use-system-config";

// Hooks de Formulários
export {
  useForms,
  useForm,
  useFormAnswers,
  useAvailableForms,
  useSubmitFormAnswers,
  useInvalidateForms,
  usePrefetchForms,
  formsKeys
} from "./use-forms";

// Hooks de Storage
export {
  useFileInfo,
  useDownloadFile,
  useFileUrl,
  useFileAsBase64,
  useGetFileAsBase64,
  useInvalidateStorage,
  usePrefetchStorage,
  storageKeys
} from "./use-storage";

// Chaves consolidadas para invalidação de cache
export const apiKeys = {
  auth: ["auth"],
  events: ["events"],
  products: ["products"],
  magazines: ["magazines"],
  users: ["users"],
  indications: ["indications"],
  notifications: ["notifications"],
  chats: ["chats"],
  referrals: ["referrals"],
  forms: ["forms"]
} as const;

/**
 * Utilitários para gerenciamento de cache
 */
export const cacheUtils = {
  /**
   * Invalida todo o cache de eventos
   */
  invalidateEvents: (queryClient: any) => {
    queryClient.invalidateQueries({queryKey: ["events"]});
  },

  /**
   * Invalida todo o cache de produtos
   */
  invalidateProducts: (queryClient: any) => {
    queryClient.invalidateQueries({queryKey: ["products"]});
  },

  /**
   * Invalida todo o cache de magazines
   */
  invalidateMagazines: (queryClient: any) => {
    queryClient.invalidateQueries({queryKey: ["magazines"]});
  },

  /**
   * Invalida todo o cache de usuários
   */
  invalidateUsers: (queryClient: any) => {
    queryClient.invalidateQueries({queryKey: ["users"]});
  },

  /**
   * Invalida todo o cache de indicações
   */
  invalidateIndications: (queryClient: any) => {
    queryClient.invalidateQueries({queryKey: ["indications"]});
  },

  /**
   * Invalida todo o cache de notificações
   */
  invalidateNotifications: (queryClient: any) => {
    queryClient.invalidateQueries({queryKey: ["notifications"]});
  },

  /**
   * Invalida todo o cache de chats
   */
  invalidateChats: (queryClient: any) => {
    queryClient.invalidateQueries({queryKey: ["chats"]});
  },

  /**
   * Invalida todo o cache da aplicação
   */
  invalidateAll: (queryClient: any) => {
    queryClient.invalidateQueries();
  },

  /**
   * Remove todo o cache da aplicação
   */
  clearAll: (queryClient: any) => {
    queryClient.clear();
  }
};

/**
 * Configurações padrão para React Query
 */
export const defaultQueryOptions = {
  staleTime: 5 * 60 * 1000, // 5 minutos
  gcTime: 10 * 60 * 1000, // 10 minutos
  retry: (failureCount: number, error: any) => {
    // Não fazer retry para erros de autenticação
    if (error?.status === 401 || error?.status === 403) {
      return false;
    }
    // Máximo 3 tentativas para outros erros
    return failureCount < 3;
  },
  retryDelay: (attemptIndex: number) =>
    Math.min(1000 * 2 ** attemptIndex, 30000)
};

/**
 * Configurações para mutações
 */
export const defaultMutationOptions = {
  retry: false // Não fazer retry automático em mutações
};

/**
 * Hook personalizado para configuração global do React Query
 */
export const useApiConfig = () => {
  return {
    defaultOptions: {
      queries: defaultQueryOptions,
      mutations: defaultMutationOptions
    }
  };
};
