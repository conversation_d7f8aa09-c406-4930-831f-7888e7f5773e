import React, {useState} from "react";
import {
  Text,
  View,
  ScrollView,
  TouchableOpacity,
  TextInput
} from "react-native";
import ScreenWithHeader from "../../components/screen-with-header";
import {useTranslation} from "react-i18next";
import {useRouter} from "expo-router";
import FullSizeButton from "../../components/full-size-button";
import styles from "../../styles/settings/rate-app.style";
import {
  showErrorToast,
  showSuccessToast,
  showWarningToast
} from "@/utils/error-handler";

const RateApp: React.FC = () => {
  const {t} = useTranslation();
  const router = useRouter();
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [feedback, setFeedback] = useState("");

  const feedbackTags = [
    {
      id: "navigation",
      label: t("rateApp.navigationIntuitive", "Navegação intuitiva")
    },
    {id: "support", label: t("rateApp.fastSupport", "Suporte rápido")},
    {id: "usability", label: t("rateApp.easeOfUse", "Facilidade de uso")},
    {
      id: "accessibility",
      label: t("rateApp.goodAccessibility", "Boa acessibilidade")
    },
    {id: "resources", label: t("rateApp.usefulResources", "Recursos úteis")},
    {
      id: "evolution",
      label: t("rateApp.constantEvolution", "Evolução constante")
    }
  ];

  const handleTagToggle = (tagId: string) => {
    setSelectedTags((prev) =>
      prev.includes(tagId)
        ? prev.filter((id) => id !== tagId)
        : [...prev, tagId]
    );
  };

  const handleSubmitFeedback = () => {
    if (selectedTags.length === 0 && !feedback.trim()) {
      showWarningToast(
        t("rateApp.error", "Erro"),
        t(
          "rateApp.selectTagsOrFeedback",
          "Por favor, selecione pelo menos uma característica ou escreva um feedback"
        )
      );
      return;
    }

    showSuccessToast(
      t("rateApp.thankYou", "Obrigado!"),
      t(
        "rateApp.feedbackSent",
        "Seu feedback foi enviado com sucesso. Agradecemos sua avaliação!"
      )
    );
    router.back();
  };

  const handleBackToSettings = () => {
    router.back();
  };

  return (
    <ScreenWithHeader
      screenTitle={t("rateApp.title", "Avaliar o aplicativo")}
      backButton
    >
      <View style={styles.container}>
        <ScrollView style={styles.scrollContainer}>
          <View style={styles.contentContainer}>
            {/* Header Text */}
            <Text style={styles.headerText}>
              {t(
                "rateApp.headerText",
                "Alguma característica se destacou para você durante a sua experiência usando o aplicativo?"
              )}
            </Text>

            {/* Feedback Tags */}
            <View style={styles.tagsContainer}>
              {feedbackTags.map((tag) => (
                <TouchableOpacity
                  key={tag.id}
                  style={[
                    styles.tagButton,
                    selectedTags.includes(tag.id) && styles.tagButtonSelected
                  ]}
                  onPress={() => handleTagToggle(tag.id)}
                >
                  <Text
                    style={[
                      styles.tagText,
                      selectedTags.includes(tag.id) && styles.tagTextSelected
                    ]}
                  >
                    {tag.label}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>

            {/* Description Section */}
            <View style={styles.descriptionContainer}>
              <Text style={styles.descriptionLabel}>
                {t("rateApp.description", "Descrição")}
              </Text>
              <View style={styles.textAreaContainer}>
                <TextInput
                  style={styles.textArea}
                  value={feedback}
                  onChangeText={setFeedback}
                  placeholder={t(
                    "rateApp.descriptionPlaceholder",
                    "Descreva melhor sua experiência aqui..."
                  )}
                  placeholderTextColor="#8B8B8B"
                  multiline
                  numberOfLines={6}
                  textAlignVertical="top"
                  maxLength={500}
                />
              </View>
            </View>
          </View>
        </ScrollView>

        {/* Bottom Buttons - Fixed at bottom */}
        <View style={styles.buttonsContainer}>
          {/* Submit Button */}
          <View style={styles.submitButton}>
            <FullSizeButton
              text={t("rateApp.submitFeedback", "Enviar feedback")}
              onPress={handleSubmitFeedback}
            />
          </View>

          {/* Back to Settings Button */}
          <TouchableOpacity
            style={styles.backButton}
            onPress={handleBackToSettings}
          >
            <Text style={styles.backButtonText}>
              {t("rateApp.backToSettings", "Voltar aos ajustes")}
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </ScreenWithHeader>
  );
};

export default RateApp;
