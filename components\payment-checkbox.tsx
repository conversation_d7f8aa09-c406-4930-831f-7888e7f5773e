import React from "react";
import {Text, TouchableOpacity, View} from "react-native";
import CheckIcon from "@/components/icons/check-icon";

export interface PaymentCheckboxProps {
  checked: boolean;
  onPress: () => void;
  text: string;
}

const PaymentCheckbox: React.FC<PaymentCheckboxProps> = ({
  checked,
  onPress,
  text
}) => {
  const checkboxStyle = {
    width: 20,
    height: 20,
    backgroundColor: checked ? "#00D4AA" : "#FFFFFF",
    borderWidth: 1,
    borderColor: checked ? "#00D4AA" : "#D0D5DD",
    borderRadius: 4,
    marginTop: 2,
    overflow: "hidden" as const
  };

  const textStyle = {
    color: "#FCFCFD",
    fontFamily: "Open Sans",
    fontSize: 14,
    fontWeight: "400" as const,
    lineHeight: 20,
    width: 303,
    textShadowColor: "rgba(0, 0, 0, 0.25)",
    textShadowOffset: {width: 0, height: 4},
    textShadowRadius: 4
  };

  const containerStyle = {
    flexDirection: "row" as const,
    alignItems: "flex-start" as const,
    gap: 8
  };

  return (
    <TouchableOpacity onPress={onPress} activeOpacity={0.7}>
      <View style={containerStyle}>
        <View style={checkboxStyle}>
          {checked ? (
            <View
              style={{
                flex: 1,
                alignItems: "center",
                justifyContent: "center"
              }}
            >
              <CheckIcon width={14} height={14} replaceColor="#FFFFFF" />
            </View>
          ) : null}
        </View>
        <Text style={textStyle}>{text}</Text>
      </View>
    </TouchableOpacity>
  );
};

export default PaymentCheckbox;
