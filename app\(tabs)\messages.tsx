import React, {use<PERSON><PERSON>back, use<PERSON>emo, useState, useEffect} from "react";
import {
  FlatList,
  ListRenderItemInfo,
  Text,
  TouchableOpacity,
  View,
  ActivityIndicator
} from "react-native";
import {useTranslation} from "react-i18next";
import ScreenWithHeader from "../../components/screen-with-header";
import MessagePlusIcon from "../../components/icons/message-plus-icon";
import styles from "../../styles/tabs/messages.style";
import stylesConstants from "../../styles/styles-constants";
import Search from "../../components/search";
import {useRouter} from "expo-router";
import {useChats} from "@/hooks/api/use-chats";
import {Chat} from "@/models/api/chats.models";
import {ChatListItem} from "@/components/chat";
import MessageFilterDrawer, {
  MessageFilters
} from "../../components/messages-screen/message-filter-drawer";
import {useSignalRChatContextOptional} from "@/contexts/signalr-chat-context";
import {useGuestUser} from "@/contexts/guest-user-context";
import {useUpsellDrawer} from "@/hooks/use-upsell-drawer";
import UpsellDrawer from "@/components/modals/upsell-drawer";
import UserSelectionModal from "../../components/modals/user-selection-modal";
import MessagesEmptyState from "../../components/messages-screen/messages-empty-state";
import GuestMessagesEmptyState from "../../components/messages-screen/guest-messages-empty-state";

interface MessageData {
  id: string;
  name: string;
  message: string;
  time: string;
  avatar: string;
  isOnline: boolean;
  isPinned?: boolean;
  unreadCount?: number;
  hasButton?: boolean;
  buttonText?: string;
  isOlderMessage?: boolean;
  lastActivity?: string;
}

const Messages: React.FC = () => {
  const {t} = useTranslation();
  const [searchValue, setSearchValue] = useState("");
  const router = useRouter();
  const [isFilterDrawerVisible, setIsFilterDrawerVisible] = useState(false);
  const {isGuest} = useGuestUser();
  const {
    isVisible: isUpsellDrawerVisible,
    config: upsellConfig,
    showUpsellDrawer,
    hideUpsellDrawer
  } = useUpsellDrawer();
  const [filters, setFilters] = useState<MessageFilters>({
    unread: false,
    unanswered: false,
    older: false,
    recent: false
  });
  const [isUserSelectionModalVisible, setIsUserSelectionModalVisible] =
    useState(false);

  // Debug: Log modal state changes
  useEffect(() => {
    console.log("🔥 Modal state changed:", isUserSelectionModalVisible);
  }, [isUserSelectionModalVisible]);

  // SignalR real-time chat functionality
  const signalRChat = useSignalRChatContextOptional();

  // Parâmetros para buscar chats com filtros
  const chatsParams = useMemo(() => {
    const params = {
      search: searchValue || undefined,
      pageSize: 20,
      hasUnread: filters.unread || undefined
    };

    // Log para debug dos filtros
    console.log("🔍 Filtros de mensagens aplicados:", {
      search: params.search,
      hasUnread: params.hasUnread,
      unanswered: filters.unanswered,
      older: filters.older,
      recent: filters.recent,
      hasSearch: !!params.search,
      hasUnreadFilter: !!params.hasUnread,
      activeFiltersCount: Object.values(filters).filter(Boolean).length
    });

    return params;
  }, [searchValue, filters]);

  // Use real API data with SignalR real-time updates - disabled for guest users
  const {
    data: chatsData,
    isLoading,
    isError
  } = useChats(chatsParams, {
    enabled: !isGuest // Disable for guest users
  } as any);

  // Helper function to format message time
  const formatMessageTime = (dateString: string): string => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 1) {
      return "Agora";
    } else if (diffInHours < 24) {
      return date.toLocaleTimeString("pt-BR", {
        hour: "2-digit",
        minute: "2-digit"
      });
    } else if (diffInHours < 48) {
      return "Há 1 dia";
    } else if (diffInHours < 168) {
      // 7 days
      const days = Math.floor(diffInHours / 24);
      return `Há ${days} dias`;
    } else if (diffInHours < 720) {
      // 30 days
      const weeks = Math.floor(diffInHours / 168);
      return weeks === 1 ? "Há 1 semana" : `Há ${weeks} semanas`;
    } else if (diffInHours < 8760) {
      // 365 days
      const months = Math.floor(diffInHours / 720);
      return months === 1 ? "Há 1 mês" : `Há ${months} meses`;
    } else {
      const years = Math.floor(diffInHours / 8760);
      return years === 1 ? "Há 1 ano" : `Há ${years} anos`;
    }
  };

  // Convert API data to MessageData format for UI compatibility with local filtering
  const messages: MessageData[] = useMemo(() => {
    if (!chatsData?.data || chatsData.data.length === 0) {
      // Return empty array when no data is available
      return [];
    }

    let processedMessages = chatsData.data.map((chat: Chat) => {
      // Get last message content
      const lastMessageContent =
        chat.lastMessage?.content || "Última mensagem não disponível";

      // Format the time using lastActivity or lastMessage createdAt
      const timeToFormat = chat.lastMessage?.createdAt || chat.lastActivity;
      const formattedTime = timeToFormat
        ? formatMessageTime(timeToFormat)
        : "Há algum tempo";

      // Calculate if message is older (more than 7 days)
      const messageDate = new Date(timeToFormat || Date.now());
      const now = new Date();
      const diffInDays =
        (now.getTime() - messageDate.getTime()) / (1000 * 60 * 60 * 24);
      const isOlderMessage = diffInDays > 7;

      return {
        id: chat.id,
        name: chat.name || "Chat sem nome",
        message: lastMessageContent,
        time: formattedTime,
        avatar:
          "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face", // Default avatar
        isOnline: false, // This would come from participant status
        isPinned: false,
        unreadCount: chat.unreadCount || 0,
        hasButton: false,
        isOlderMessage,
        lastActivity: timeToFormat || chat.lastActivity
      };
    });

    // Apply local filters that are not handled by the API
    if (filters.unanswered) {
      // Filter for chats where the last message was not sent by the current user
      // This is a simplified implementation - in a real app, you'd need user ID comparison
      processedMessages = processedMessages.filter(
        (msg) => msg.unreadCount > 0 // Simplified: assume unread messages need answers
      );
    }

    if (filters.older) {
      // Filter for messages older than 7 days
      processedMessages = processedMessages.filter((msg) => msg.isOlderMessage);
    }

    if (filters.recent) {
      // Filter for messages from the last 24 hours
      processedMessages = processedMessages.filter((msg) => {
        if (!msg.lastActivity) return false;
        const messageDate = new Date(msg.lastActivity);
        const now = new Date();
        const diffInHours =
          (now.getTime() - messageDate.getTime()) / (1000 * 60 * 60);
        return diffInHours <= 24;
      });
    }

    // Sort messages by last activity (most recent first) unless "older" filter is active
    if (filters.older) {
      // Sort oldest first when "older" filter is active
      processedMessages.sort((a, b) => {
        const dateA = new Date(a.lastActivity || 0).getTime();
        const dateB = new Date(b.lastActivity || 0).getTime();
        return dateA - dateB;
      });
    } else {
      // Default sort: most recent first
      processedMessages.sort((a, b) => {
        const dateA = new Date(a.lastActivity || 0).getTime();
        const dateB = new Date(b.lastActivity || 0).getTime();
        return dateB - dateA;
      });
    }

    // Log final results for debugging
    console.log("📊 Resultado da filtragem:", {
      totalChats: chatsData?.data?.length || 0,
      filteredMessages: processedMessages.length,
      activeFilters: Object.entries(filters)
        .filter(([_, value]) => value)
        .map(([key]) => key),
      hasFiltersActive: Object.values(filters).some(Boolean)
    });

    return processedMessages;
  }, [chatsData, filters]);

  const handleSearchChange = useCallback((value: string) => {
    setSearchValue(value);
  }, []);

  const handleMessagePress = useCallback(
    (messageData: MessageData) => {
      router.push(
        `/(logged-stack)/chat?chatId=${
          messageData.id
        }&name=${encodeURIComponent(messageData.name)}`
      );
    },
    [router]
  );

  // Handle create new chat
  const handleCreateChat = useCallback(() => {
    console.log("🔥 handleCreateChat called - checking upsell");

    // Interceptar ação para usuários guest (upsell)
    const wasIntercepted = showUpsellDrawer({
      title: "Quase lá!",
      description:
        "Faça seu cadastro e torne-se membro para enviar mensagens e outros diversos benefícios no aplicativo."
    });

    if (!wasIntercepted) {
      console.log("🔥 Opening modal for authenticated user");
      console.log("🔥 Current modal state:", isUserSelectionModalVisible);

      // Force immediate state update
      setIsUserSelectionModalVisible((prev) => {
        console.log("🔥 Previous state:", prev, "-> Setting to: true");
        return true;
      });

      // Backup timeout to ensure modal opens
      setTimeout(() => {
        setIsUserSelectionModalVisible(true);
        console.log("🔥 Backup modal open triggered");
      }, 50);

      console.log("🔥 Modal state set to true");
    }
  }, [showUpsellDrawer, isUserSelectionModalVisible]);

  // Handle chat created
  const handleChatCreated = useCallback(
    (chatId: string) => {
      // Navigate to the created chat
      router.push(`/(logged-stack)/chat?chatId=${chatId}`);
    },
    [router]
  );

  // Handle close user selection modal
  const handleCloseUserSelectionModal = useCallback(() => {
    setIsUserSelectionModalVisible(false);
  }, []);

  const handleFilterPress = useCallback(() => {
    setIsFilterDrawerVisible(true);
  }, []);

  const handleFilterDrawerClose = useCallback(() => {
    setIsFilterDrawerVisible(false);
  }, []);

  const handleFiltersChange = useCallback((newFilters: MessageFilters) => {
    setFilters(newFilters);
  }, []);

  const handleClearFilters = useCallback(() => {
    setFilters({
      unread: false,
      unanswered: false,
      older: false,
      recent: false
    });
  }, []);

  const renderMessage = useCallback(
    ({item}: ListRenderItemInfo<MessageData>) => (
      <ChatListItem
        id={item.id}
        name={item.name}
        message={item.message}
        time={item.time}
        avatar={item.avatar}
        isOnline={item.isOnline}
        isPinned={item.isPinned}
        unreadCount={item.unreadCount}
        hasButton={item.hasButton}
        buttonText={item.buttonText}
        isOlderMessage={item.isOlderMessage}
        onPress={() => handleMessagePress(item)}
        onButtonPress={() => {
          // Handle button press for events, etc.
          console.log("Button pressed for:", item.id);
        }}
      />
    ),
    [handleMessagePress]
  );

  const rightHeaderChild = useMemo(
    () => (
      <View style={{position: "relative"}}>
        <TouchableOpacity
          onPress={handleCreateChat}
          onPressIn={() => console.log("🔥 PRESS IN detected")}
          onPressOut={() => console.log("🔥 PRESS OUT detected")}
          style={{
            width: 80,
            height: 80,
            justifyContent: "center",
            alignItems: "center",
            backgroundColor: "transparent",
            position: "absolute",
            top: 0,
            left: 0,
            zIndex: 999
          }}
          activeOpacity={0.5}
          hitSlop={{top: 20, bottom: 20, left: 20, right: 20}}
        />
        <View
          style={{
            width: 80,
            height: 80,
            justifyContent: "center",
            alignItems: "center",
            backgroundColor: "transparent"
          }}
          pointerEvents="none"
        >
          <MessagePlusIcon width={40} height={40} />
        </View>
      </View>
    ),
    [handleCreateChat]
  );

  // Log SignalR connection status changes in development
  useEffect(() => {
    if (__DEV__ && signalRChat) {
      console.log("🔗 SignalR Status:", {
        isConnected: signalRChat.isConnected,
        isConnecting: signalRChat.isConnecting,
        isReconnecting: signalRChat.isReconnecting,
        connectionState: signalRChat.connectionState,
        typingUsers: signalRChat.typingUsers.length,
        onlineUsers: signalRChat.onlineUsers.length
      });
    }
  }, [
    signalRChat?.isConnected,
    signalRChat?.isConnecting,
    signalRChat?.isReconnecting
  ]);

  // Show loading state
  if (isLoading) {
    return (
      <ScreenWithHeader screenTitle={t("messages.title")} disableScrollView>
        <View style={styles.pageContainer}>
          <Search
            searchBarValue={searchValue}
            onSearchBarChange={handleSearchChange}
            onFilterPress={handleFilterPress}
            searchBarPlaceholder={t("messages.searchPlaceholder")}
          />
          <View
            style={{flex: 1, justifyContent: "center", alignItems: "center"}}
          >
            <ActivityIndicator
              size="large"
              color={stylesConstants.colors.primary}
            />
            <Text
              style={{marginTop: 16, color: stylesConstants.colors.textPrimary}}
            >
              Carregando conversas...
            </Text>
          </View>
        </View>
      </ScreenWithHeader>
    );
  }

  // Show error state
  if (isError) {
    return (
      <ScreenWithHeader screenTitle={t("messages.title")} disableScrollView>
        <View style={styles.pageContainer}>
          <Search
            searchBarValue={searchValue}
            onSearchBarChange={handleSearchChange}
            onFilterPress={handleFilterPress}
            searchBarPlaceholder={t("messages.searchPlaceholder")}
          />
          <View
            style={{flex: 1, justifyContent: "center", alignItems: "center"}}
          >
            <Text
              style={{
                color: stylesConstants.colors.textPrimary,
                marginBottom: 16
              }}
            >
              Erro ao carregar conversas
            </Text>
            <TouchableOpacity
              style={{
                backgroundColor: stylesConstants.colors.primary,
                paddingHorizontal: 20,
                paddingVertical: 10,
                borderRadius: 8
              }}
              onPress={() => window.location.reload()}
            >
              <Text style={{color: "white"}}>Tentar novamente</Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScreenWithHeader>
    );
  }

  return (
    <ScreenWithHeader
      screenTitle={t("messages.title")}
      rightHeaderChild={rightHeaderChild}
      disableScrollView
    >
      <View style={styles.pageContainer}>
        <Search
          searchBarValue={searchValue}
          onSearchBarChange={handleSearchChange}
          onFilterPress={handleFilterPress}
          searchBarPlaceholder={t("messages.searchPlaceholder")}
        />

        {/* Show empty state when no messages */}
        {messages.length === 0 ? (
          isGuest ? (
            <GuestMessagesEmptyState />
          ) : (
            <MessagesEmptyState onStartConversation={handleCreateChat} />
          )
        ) : (
          <FlatList
            data={messages}
            renderItem={renderMessage}
            keyExtractor={(item) => item.id}
            showsVerticalScrollIndicator={false}
          />
        )}
      </View>

      {/* Message Filter Drawer */}
      <MessageFilterDrawer
        visible={isFilterDrawerVisible}
        filters={filters}
        onFiltersChange={handleFiltersChange}
        onClose={handleFilterDrawerClose}
        onClear={handleClearFilters}
      />

      {/* User Selection Modal */}
      <UserSelectionModal
        visible={isUserSelectionModalVisible}
        onClose={handleCloseUserSelectionModal}
        onChatCreated={handleChatCreated}
      />

      {/* Upsell Drawer for Guest Users */}
      <UpsellDrawer
        visible={isUpsellDrawerVisible}
        onClose={hideUpsellDrawer}
        title={upsellConfig.title}
        description={upsellConfig.description}
      />
    </ScreenWithHeader>
  );
};

export default Messages;
