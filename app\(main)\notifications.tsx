import React, {useState, useMemo, useCallback} from "react";
import {
  Text,
  View,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator
} from "react-native";
import ScreenWithHeader from "../../components/screen-with-header";
import {useTranslation} from "react-i18next";
import {useRouter} from "expo-router";

import styles from "@/styles/notifications/notifications.style";
import NotificationItem from "../../components/notifications/notification-item";

import {
  useNotifications,
  useMarkNotificationAsRead,
  useOptimisticNotificationUpdate
} from "../../hooks/api/use-notifications";
import {
  Notification as ApiNotification,
  NotificationStatus,
  NotificationType,
  NotificationPriority,
  NotificationChannel
} from "../../models/api/notifications.models";
import stylesConstants from "../../styles/styles-constants";
import {useGuestUser} from "@/contexts/guest-user-context";

type NotificationFilter = "all" | "read" | "unread";

const Notifications: React.FC = () => {
  const {t} = useTranslation();
  const {isGuest} = useGuestUser();
  const router = useRouter();
  const [activeFilter, setActiveFilter] = useState<NotificationFilter>("all");

  // Hooks para buscar notificações - disabled for guest users
  // Preparar parâmetros baseados no filtro ativo
  const getNotificationParams = useCallback(() => {
    const baseParams = {page: 1, pageSize: 50};

    switch (activeFilter) {
      case "read": {
        const readParams = {...baseParams, isRead: true};
        console.log(
          "🔍 [NOTIFICATIONS] Buscando notificações LIDAS com params:",
          readParams
        );
        return readParams;
      }
      case "unread": {
        const unreadParams = {...baseParams, isRead: false};
        console.log(
          "🔍 [NOTIFICATIONS] Buscando notificações NÃO LIDAS com params:",
          unreadParams
        );
        return unreadParams;
      }
      default:
        console.log(
          "🔍 [NOTIFICATIONS] Buscando TODAS as notificações com params:",
          baseParams
        );
        return baseParams; // Para "all", não passa o filtro isRead
    }
  }, [activeFilter]);

  const {
    data: notificationsResponse,
    isLoading,
    error,
    refetch
  } = useNotifications(getNotificationParams(), {
    enabled: !isGuest // Disable for guest users
  } as any);

  // Hook para marcar notificação como lida
  const markAsReadMutation = useMarkNotificationAsRead();

  // Hook para atualização otimista
  const {markAsReadOptimistic} = useOptimisticNotificationUpdate();

  // Função para mapear tipos numéricos para enums
  const mapNotificationType = (typeNumber: number): NotificationType => {
    switch (typeNumber) {
      case 1:
        return NotificationType.EVENT;
      case 2:
        return NotificationType.ANNOUNCEMENT;
      case 3:
        return NotificationType.SYSTEM;
      case 4:
        return NotificationType.PROMOTION;
      case 5:
        return NotificationType.REMINDER;
      case 6:
        return NotificationType.CHAT;
      case 7:
        return NotificationType.MESSAGE;
      case 8:
        return NotificationType.PRODUCT;
      default:
        return NotificationType.GENERAL;
    }
  };

  // Mapear o status do nível superior da resposta da API
  const mapNotificationStatus = (statusNumber: number): NotificationStatus => {
    switch (statusNumber) {
      case 2:
        return NotificationStatus.READ; // 2 = lida
      case 3:
        return NotificationStatus.UNREAD; // 3 = não lida
      default:
        return NotificationStatus.UNREAD;
    }
  };

  // Mapear os dados da API para extrair os objetos notification
  const notifications = useMemo(() => {
    const rawData = notificationsResponse?.data || [];
    console.log(
      "🔍 [NOTIFICATIONS] Raw data from API:",
      JSON.stringify(rawData, null, 2)
    );

    // A API retorna um wrapper com { id, notification, notificationId, sentAt, userId }
    // Usamos o 'id' do wrapper (relação usuário-notificação) para operações de leitura
    const mappedNotifications = rawData.map((item: any, index: number) => {
      console.log(
        `📦 [NOTIFICATIONS] Item ${index}:`,
        JSON.stringify(item, null, 2)
      );

      if (item.notification) {
        console.log(
          `📋 [NOTIFICATIONS] Notification object inside item ${index}:`,
          JSON.stringify(item.notification, null, 2)
        );

        const notification = item.notification;
        const mappedNotification = {
          id:
            item.id?.toString() ||
            item.notificationId?.toString() ||
            notification.id?.toString(),
          userId:
            item.userId?.toString() || notification.userId?.toString() || "",
          type: mapNotificationType(notification.type),
          priority: NotificationPriority.NORMAL, // Default priority
          status: mapNotificationStatus(item.status), // Usar o status do nível superior (item.status)
          title: notification.title || "",
          message: notification.body || notification.message || "", // API usa 'body' em vez de 'message'
          data: notification.data || {},
          imageUrl: notification.imageUrl,
          actionUrl: notification.actionUrl,
          actionText: notification.actionText,
          channels: [NotificationChannel.IN_APP], // Default channel
          scheduledAt: notification.scheduledAt,
          sentAt: item.sentAt || notification.sentAt,
          readAt: item.readAt || notification.readAt, // Usar o readAt do nível superior (item.readAt)
          expiresAt: notification.expiresAt,
          createdAt: notification.createdAt || item.sentAt,
          updatedAt:
            notification.updatedAt || notification.createdAt || item.sentAt
        };

        console.log(
          `✅ [NOTIFICATIONS] Mapped notification ${index}:`,
          JSON.stringify(mappedNotification, null, 2)
        );
        return mappedNotification;
      }

      // Fallback caso não tenha a estrutura esperada
      console.log(
        `⚠️ [NOTIFICATIONS] Item ${index} doesn't have notification property, using as is`
      );
      return item;
    });

    console.log(
      "🎯 [NOTIFICATIONS] Final mapped notifications:",
      JSON.stringify(mappedNotifications, null, 2)
    );
    return mappedNotifications;
  }, [notificationsResponse?.data]);

  // Função para marcar notificação como lida (com atualização otimista)
  const markNotificationAsRead = useCallback(
    (notification: ApiNotification) => {
      if (notification.status === NotificationStatus.UNREAD) {
        // Atualização otimista para feedback imediato
        markAsReadOptimistic(notification.id);

        // Chamada para API apenas para notificações reais
        markAsReadMutation.mutate(notification.id, {
          onError: () => {
            // Em caso de erro, reverter a atualização otimista
            // O React Query já faz isso automaticamente ao invalidar as queries
            console.error("Erro ao marcar notificação como lida");
          }
        });
      }
    },
    [markAsReadOptimistic, markAsReadMutation]
  );

  // Função para navegar baseada na URL da notificação
  const navigateToNotificationUrl = useCallback(
    (actionUrl: string) => {
      try {
        // Remover barra inicial se existir para normalizar
        const cleanUrl = actionUrl.startsWith("/")
          ? actionUrl.slice(1)
          : actionUrl;

        // Mapear URLs para rotas do app
        if (cleanUrl.startsWith("events/")) {
          // URLs como "/events/4" -> navegar para event-sale
          const eventId = cleanUrl.split("/")[1];
          router.push({
            pathname: "/(events)/event-sale",
            params: {id: eventId}
          });
        } else if (cleanUrl === "opportunities") {
          // URL "/opportunities" -> navegar para business center
          router.push("/(business)/business-center");
        } else if (cleanUrl.startsWith("opportunities/")) {
          // URLs como "/opportunities/123" -> navegar para oportunidade específica
          const opportunityId = cleanUrl.split("/")[1];
          router.push(`/(business)/opportunities/${opportunityId}`);
        } else if (cleanUrl.startsWith("products/")) {
          // URLs como "/products/123" -> navegar para produto específico
          const productId = cleanUrl.split("/")[1];
          router.push({
            pathname: "/(logged-stack)/product-page",
            params: {id: productId}
          });
        } else if (
          cleanUrl.startsWith("user-profile/") ||
          cleanUrl.startsWith("users/")
        ) {
          // URLs como "/user-profile/123" ou "/users/123" -> navegar para perfil
          const userId = cleanUrl.split("/")[1];
          router.push(`/(main)/user-profile/${userId}`);
        } else if (
          cleanUrl.startsWith("chat/") ||
          cleanUrl.startsWith("messages/")
        ) {
          // URLs como "/chat/123" ou "/messages/123" -> navegar para chat
          const chatId = cleanUrl.split("/")[1];
          router.push(`/(logged-stack)/chat?chatId=${chatId}`);
        } else if (cleanUrl === "wallet" || cleanUrl === "carteira-digital") {
          // URL "/wallet" -> navegar para carteira
          router.push("/(wallet)/wallet");
        } else if (
          cleanUrl === "magazine" ||
          cleanUrl.startsWith("magazine/")
        ) {
          // URLs de revista
          if (cleanUrl === "magazine") {
            router.push("/(magazine)/magazine-list");
          } else {
            const magazineId = cleanUrl.split("/")[1];
            router.push(`/(magazine)/magazine-details/${magazineId}`);
          }
        } else if (cleanUrl === "referral" || cleanUrl === "indicacao-amigos") {
          // URL de indicação de amigos
          router.push("/(main)/referral");
        } else if (cleanUrl === "schedule" || cleanUrl === "agenda") {
          // URL da agenda
          router.push("/(tabs)/schedule");
        } else if (cleanUrl === "home") {
          // URL da home
          router.push("/(tabs)/home");
        } else {
          // Para URLs não mapeadas, tentar navegar diretamente
          console.warn(`URL de notificação não mapeada: ${actionUrl}`);
          // Tentar navegar para a URL como está (pode funcionar para algumas rotas)
          router.push(actionUrl as any);
        }
      } catch (error) {
        console.error("Erro ao navegar para URL da notificação:", error);
      }
    },
    [router]
  );

  // Função para lidar com ação da notificação
  const handleNotificationAction = useCallback(
    (notification: ApiNotification) => {
      // Marcar como lida se não estiver
      markNotificationAsRead(notification);

      // Navegar para URL da ação se existir
      if (notification.actionUrl) {
        navigateToNotificationUrl(notification.actionUrl);
      }
    },
    [markNotificationAsRead, navigateToNotificationUrl]
  );

  // Função para marcar notificação como lida ao tocar
  const handleNotificationPress = useCallback(
    (notification: ApiNotification) => {
      markNotificationAsRead(notification);

      // Se a notificação tem actionUrl, navegar para ela mesmo sem botão de ação
      if (notification.actionUrl) {
        navigateToNotificationUrl(notification.actionUrl);
      }
    },
    [markNotificationAsRead, navigateToNotificationUrl]
  );

  const filteredNotifications = useMemo(() => notifications, [notifications]);

  const renderFilterTabs = () => {
    return (
      <View style={styles.tabContainer}>
        <View style={styles.tabList}>
          <TouchableOpacity
            style={[
              styles.tabButton,
              activeFilter === "all" && styles.tabButtonActive
            ]}
            onPress={() => setActiveFilter("all")}
          >
            <Text
              style={[
                styles.tabButtonText,
                activeFilter === "all" && styles.tabButtonTextActive
              ]}
            >
              Todas
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[
              styles.tabButton,
              activeFilter === "read" && styles.tabButtonActive
            ]}
            onPress={() => setActiveFilter("read")}
          >
            <Text
              style={[
                styles.tabButtonText,
                activeFilter === "read" && styles.tabButtonTextActive
              ]}
            >
              Lidas
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[
              styles.tabButton,
              activeFilter === "unread" && styles.tabButtonActive
            ]}
            onPress={() => setActiveFilter("unread")}
          >
            <Text
              style={[
                styles.tabButtonText,
                activeFilter === "unread" && styles.tabButtonTextActive
              ]}
            >
              Não lidas
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  return (
    <ScreenWithHeader
      screenTitle={t("notifications.title", "Notificações")}
      backButton
    >
      <View style={styles.container}>
        {renderFilterTabs()}

        {/* Loading state */}
        {isLoading && (
          <View style={{padding: 20, alignItems: "center"}}>
            <ActivityIndicator
              size="large"
              color={stylesConstants.colors.primary500}
            />
            <Text
              style={{
                marginTop: 10,
                color: stylesConstants.colors.textSecondary
              }}
            >
              {t("notifications.loading", "Carregando notificações...")}
            </Text>
          </View>
        )}

        {/* Error state */}
        {error && !isLoading && (
          <View style={{padding: 20, alignItems: "center"}}>
            <Text
              style={{
                color: stylesConstants.colors.error500,
                textAlign: "center"
              }}
            >
              {t(
                "notifications.error",
                "Erro ao carregar notificações. Toque para tentar novamente."
              )}
            </Text>
            <TouchableOpacity
              onPress={() => refetch()}
              style={{
                marginTop: 10,
                padding: 10,
                backgroundColor: stylesConstants.colors.primary500,
                borderRadius: 8
              }}
            >
              <Text style={{color: "white"}}>
                {t("notifications.retry", "Tentar novamente")}
              </Text>
            </TouchableOpacity>
          </View>
        )}

        {/* Notifications list */}
        {!isLoading && !error && (
          <ScrollView style={styles.notificationsList}>
            {filteredNotifications.map((notification, index) => {
              // Render normal notification item
              return (
                <NotificationItem
                  key={notification.id}
                  notification={notification}
                  onPress={handleNotificationPress}
                  onActionPress={handleNotificationAction}
                  showSeparator={index < filteredNotifications.length - 1}
                />
              );
            })}

            {filteredNotifications.length === 0 && (
              <View style={styles.emptyStateContainer}>
                <Text style={styles.emptyStateText}>
                  {(() => {
                    if (activeFilter === "all") {
                      return t("notifications.empty", "Nenhuma notificação");
                    } else if (activeFilter === "read") {
                      return t(
                        "notifications.emptyRead",
                        "Nenhuma notificação lida"
                      );
                    } else {
                      return t(
                        "notifications.emptyUnread",
                        "Nenhuma notificação não lida"
                      );
                    }
                  })()}
                </Text>
              </View>
            )}
          </ScrollView>
        )}
      </View>
    </ScreenWithHeader>
  );
};

export default Notifications;
