import React, {use<PERSON><PERSON>back, useMemo, useState, useEffect} from "react";
import BackgroundLogoTexture from "@/components/logos/background-logo-texture";
import Screen from "@/components/screen";
import {ColorValue, Text, View, TouchableOpacity} from "react-native";
import styles from "@/styles/auth/login.style";
import <PERSON><PERSON><PERSON> from "@/components/logos/small-logo";
import {useTranslation} from "react-i18next";
import InputField from "@/components/input-field";
import {Login as LoginModel} from "@/models/login";
import PasswordIcon from "@/components/icons/password-icon";
import FullSizeButton from "@/components/full-size-button";
import useAuthLogin from "@/hooks/use-auth-login";
import {formatCpf, isValidCpf, cleanCpf} from "@/utils/cpf";
import LicenceThirdPartyIcon from "@/components/icons/licence-third-party-icon";
import {useRouter, useLocalSearchParams} from "expo-router";

import useForgetPassword from "@/hooks/use-forget-password";
import UserIcon from "@/components/icons/user-icon";
import PhoneIcon from "@/components/icons/phone-icon";
import useUpsell from "@/hooks/use-upsell";
import {useLoading} from "@/contexts/loading-context";
import {useCheckUserExists} from "@/hooks/api/use-users";
import BiometricLoginButton from "@/components/biometric-login-button";

enum LoginStages {
  Document = 0,
  Password = 1,
  Upsell = 2
}

const Login: React.FC = () => {
  const {t} = useTranslation();
  const router = useRouter();
  const params = useLocalSearchParams<{
    document?: string;
    fromBiometric?: string;
  }>();
  const loginAction = useAuthLogin();
  const upsellAction = useUpsell();
  const forgetPasswordAction = useForgetPassword();
  const {currentLoading} = useLoading();
  // Log dos parâmetros recebidos para debug
  console.log("🔍 Login Screen Params:", {
    document: params.document,
    fromBiometric: params.fromBiometric,
    willGoToPasswordStage: params.fromBiometric === "true" && params.document
  });

  const [loginForm, setLoginForm] = useState<LoginModel>({
    document: params.document ? formatCpf(params.document) : "",
    password: ""
  });
  const [upsellForm, setUpsellForm] = useState({
    name: "",
    phone: ""
  });
  const [stage, setStage] = useState<LoginStages>(
    params.fromBiometric === "true" && params.document
      ? LoginStages.Password
      : LoginStages.Document
  );
  const [validationErrors, setValidationErrors] = useState<
    Record<string, string>
  >({});
  const [shouldCheckUser, setShouldCheckUser] = useState(false);

  // Only call useCheckUserExists when we have a valid document and need to check
  const cleanDocument = loginForm.document ? cleanCpf(loginForm.document) : "";
  const checkUserExistsQuery = useCheckUserExists(
    shouldCheckUser && cleanDocument.length === 11 ? cleanDocument : ""
  );

  // Handle user existence check result
  useEffect(() => {
    if (checkUserExistsQuery.data !== undefined && shouldCheckUser) {
      setShouldCheckUser(false); // Reset the check flag

      if (checkUserExistsQuery.data.exists) {
        // User exists, proceed to password stage
        setStage(LoginStages.Password);
        // Persistir CPF e sinalizar que já estamos na etapa de senha para evitar voltar ao início em caso de erro
        // Atualizamos a rota atual com os parâmetros necessários
        router.replace({
          pathname: "/(auth)/login",
          params: {
            document: cleanDocument,
            fromBiometric: "true"
          }
        });
      } else {
        // User doesn't exist, redirect to no-login flow
        router.push({
          pathname: "/(auth)/no-login-flow",
          params: {
            document: cleanDocument
          }
        });
      }
    }
  }, [checkUserExistsQuery.data, shouldCheckUser, router, loginForm.document]);

  const errors = useMemo(
    () => ({
      ...loginAction.errors,
      ...upsellAction.errors,
      ...validationErrors
    }),
    [loginAction.errors, upsellAction.errors, validationErrors]
  );

  const validDocument = useMemo(() => {
    return (
      loginForm.document &&
      loginForm.document.length >= 14 &&
      isValidCpf(loginForm.document)
    );
  }, [loginForm.document]);

  const validPhone = useMemo(() => {
    return (
      upsellForm.phone &&
      upsellForm.phone.length >= 10 &&
      upsellForm.phone.length <= 11
    );
  }, [upsellForm.phone]);

  const onFormChange = useCallback(
    (field: "document" | "password") => (value: string) => {
      setLoginForm((prev) => ({
        ...prev,
        [field]: field === "document" ? formatCpf(value) : value
      }));
    },
    []
  );

  const onUpsellFormChange = useCallback(
    (field: "name" | "phone") => (value: string) => {
      setUpsellForm((prev) => ({
        ...prev,
        [field]: value
      }));
    },
    []
  );

  const onNextStage = useCallback(async () => {
    switch (stage) {
      case LoginStages.Document:
        if (!validDocument) {
          setValidationErrors({document: t("login.invalidDocument")});
          return;
        }

        // Trigger user existence check
        setShouldCheckUser(true);
        break;
      case LoginStages.Password:
        if (loginForm.document) {
          loginAction.login({
            document: loginForm.document,
            password: loginForm.password
          });
        }
        break;
      case LoginStages.Upsell:
        if (!upsellForm.name) {
          setValidationErrors({
            name: t("login.invalidName")
          });
          return;
        }

        if (!validPhone) {
          setValidationErrors({
            phone: t("login.invalidPhone")
          });
          return;
        }

        upsellAction.upsell({
          name: upsellForm.name,
          phoneNumber: upsellForm.phone,
          document: loginForm.document ?? ""
        });
        break;
    }
  }, [stage, loginForm, upsellForm, validDocument, validPhone, t, router]);

  const documentIcon = useCallback(
    (errorColor?: ColorValue) => (
      <LicenceThirdPartyIcon replaceColor={errorColor} />
    ),
    []
  );

  const passwordIcon = useCallback(
    (errorColor?: ColorValue) => <PasswordIcon replaceColor={errorColor} />,
    []
  );

  const userIcon = useCallback(
    (errorColor?: ColorValue) => <UserIcon replaceColor={errorColor} />,
    []
  );

  const phoneIcon = useCallback(
    (errorColor?: ColorValue) => <PhoneIcon replaceColor={errorColor} />,
    []
  );

  const onRecoveryPasswordButtonPress = useCallback(() => {
    forgetPasswordAction.sendRecoveryEmail({
      document: loginForm.document ?? ""
    });
  }, [forgetPasswordAction.sendRecoveryEmail, loginForm.document]);

  const onRegisterButtonPress = useCallback(() => {
    router.push("/(registration)/subscription-registration");
  }, [router]);

  const onActivateAccountButtonPress = useCallback(() => {
    router.push({
      pathname: "/(auth)/account-activation/activation-code",
      params: {
        document: loginForm.document || ""
      }
    });
  }, [router, loginForm.document]);

  const form = useMemo(() => {
    switch (stage) {
      case LoginStages.Document:
        return (
          <InputField
            key="cpf-input"
            label={t("login.fields.document")}
            icon={documentIcon}
            value={loginForm.document ?? ""}
            maxLength={14}
            onChangeText={onFormChange("document")}
            placeholder={t("login.fields.documentPlaceholder")}
            inputMode="numeric"
            error={errors.document}
          />
        );
      case LoginStages.Password:
        return (
          <InputField
            key="password-input"
            label={t("login.fields.password")}
            icon={passwordIcon}
            value={loginForm.password}
            onChangeText={onFormChange("password")}
            placeholder={t("login.fields.passwordPlaceholder")}
            isPassword
            error={errors.password}
          />
        );
      case LoginStages.Upsell:
        return (
          <View style={styles.gap}>
            <InputField
              key="name-input"
              label={t("login.fields.fullName")}
              icon={userIcon}
              value={upsellForm.name}
              onChangeText={onUpsellFormChange("name")}
              placeholder={t("login.fields.fullNamePlaceholder")}
              error={errors.name}
            />
            <InputField
              key="phone-input"
              label={t("login.fields.phone")}
              icon={phoneIcon}
              value={upsellForm.phone}
              onChangeText={onUpsellFormChange("phone")}
              placeholder={t("login.fields.phonePlaceholder")}
              inputMode="tel"
              maxLength={11}
              error={errors.phone}
            />
          </View>
        );
    }
  }, [stage, loginForm, errors, upsellForm, t]);

  const goToInitialStage = useCallback(() => {
    setStage(LoginStages.Document);
    setLoginForm({document: "", password: ""});
    setValidationErrors({});
  }, []);

  const stageTexts = useMemo(() => {
    switch (stage) {
      case LoginStages.Document:
        return {
          footer: (
            <Text style={[styles.text, styles.createAccountContainer]}>
              {t("login.register.title") + " "}
              <Text
                style={styles.clickableText}
                onPress={onRegisterButtonPress}
              >
                {t("login.register.button")}
              </Text>
            </Text>
          ),
          btnText: checkUserExistsQuery.isLoading
            ? t("login.verifying", "Verificando...")
            : t("login.nextButton"),
          description: t("login.description")
        };
      case LoginStages.Password:
        return {
          footer: (
            <View>
              <Text style={[styles.text, styles.createAccountContainer]}>
                {t("login.register.title") + " "}
                <Text
                  style={styles.clickableText}
                  onPress={onRegisterButtonPress}
                >
                  {t("login.register.button")}
                </Text>
              </Text>
              <Text
                style={[
                  styles.text,
                  styles.forgotPassword,
                  styles.clickableText
                ]}
                onPress={onRecoveryPasswordButtonPress}
              >
                {t("login.forgotPassword")}
              </Text>
            </View>
          ),
          btnText: t("Prosseguir"),
          description: t("login.passwordDescription")
        };
      case LoginStages.Upsell:
        return {
          footer: (
            <Text
              style={[styles.text, styles.createAccountContainer]}
              onPress={goToInitialStage}
            >
              {t("login.goToStart")}
            </Text>
          ),
          btnText: t("login.access"),
          description: t("login.upsellDescription")
        };
      default:
        return {
          footer: null,
          btnText: t("login.accessButton"),
          description: t("login.description")
        };
    }
  }, [stage, t, checkUserExistsQuery.isLoading]);

  return (
    <>
      <BackgroundLogoTexture gray={true} />
      <Screen>
        <View style={styles.container}>
          <View>
            <View style={styles.headerContainer}>
              {stage === LoginStages.Password && (
                <TouchableOpacity
                  style={styles.activateAccountButton}
                  onPress={onActivateAccountButtonPress}
                >
                  <Text style={styles.activateAccountText}>
                    {t("login.activateAccount")}
                  </Text>
                </TouchableOpacity>
              )}
            </View>
            <View style={styles.logoContainer}>
              <SmallLogo width={63.3} height={86.4} />
            </View>
            <View style={styles.textContainer}>
              <Text style={[styles.text, styles.title]}>
                {t("login.title")}
              </Text>
              <Text style={styles.text}>{stageTexts?.description || ""}</Text>
            </View>
            <View style={styles.fieldsMargin}>{form}</View>
            <FullSizeButton
              text={stageTexts?.btnText || t("login.accessButton")}
              onPress={onNextStage}
              loading={
                loginAction.isLoading ||
                currentLoading ||
                checkUserExistsQuery.isLoading
              }
              disabled={
                loginAction.isLoading ||
                currentLoading ||
                checkUserExistsQuery.isLoading
              }
            />
            {stage === LoginStages.Password && (
              <BiometricLoginButton
                disabled={
                  loginAction.isLoading ||
                  currentLoading ||
                  checkUserExistsQuery.isLoading
                }
              />
            )}
          </View>
          <View>{stageTexts?.footer}</View>
        </View>
      </Screen>
    </>
  );
};

export default Login;
