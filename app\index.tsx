import React, {useCallback, useEffect, useMemo, useRef, useState} from "react";
import {NativeSyntheticEvent, View} from "react-native";
import PagerView from "react-native-pager-view";
import styles from "../styles/auth/index.style";
import Slide from "../components/intro/slide";
import {ImageSource} from "expo-image/src/Image.types";
import Overlay, {SlideTextProps} from "../components/intro/overlay";
import OverlayTop from "../components/intro/overlay-top";
import {Double} from "react-native/Libraries/Types/CodegenTypes";
import useLogin from "../hooks/use-login";
import {useAuth} from "@/contexts/AuthContext";
import {useRegistrationAnalysis} from "@/contexts/registration-analysis-context";
import StartupBiometricPrompt from "@/components/startup-biometric-prompt";
import BackgroundLogoTexture from "@/components/logos/background-logo-texture";
import {useRouter} from "expo-router";

const IndexPage: React.FC = () => {
  const [slideState, setSlideState] = useState({
    offset: 0,
    index: 0
  });
  const [isRedirecting, setIsRedirecting] = useState(false);
  const pageViewRef = useRef<PagerView>(null);
  const router = useRouter();

  const loginAction = useLogin();
  const {
    isAuthenticated,
    isLoading,
    requiresBiometricAuth,
    authenticateWithBiometrics,
    skipBiometricAuth
  } = useAuth();

  // Redirecionamento na abertura do app
  useEffect(() => {
    if (isLoading || isRedirecting) return;

    // Se usuário está em análise (persistido), sempre ir para a tela de status primeiro
    // Isso intercepta a navegação padrão e evita a biometria
    (async () => {
      try {
        const AsyncStorage = await import(
          "@react-native-async-storage/async-storage"
        );
        const underAnalysis = await AsyncStorage.default.getItem(
          "@club_m_registration_under_analysis"
        );
        if (underAnalysis === "1") {
          setIsRedirecting(true);
          router.replace("/(registration)/registration-status");
          return;
        }
      } catch {}

      // Caso contrário, manter regra padrão para usuários autenticados
      if (isAuthenticated && !requiresBiometricAuth) {
        setIsRedirecting(true);
        router.replace("/(tabs)/home");
      }
    })();
  }, [
    isLoading,
    isAuthenticated,
    requiresBiometricAuth,
    isRedirecting,
    router
  ]);

  // Monitorar especificamente quando a biometria for concluída
  useEffect(() => {
    // Se o usuário estava em biometria e agora não está mais, e está autenticado
    if (
      isAuthenticated &&
      !requiresBiometricAuth &&
      !isLoading &&
      !isRedirecting
    ) {
      console.log(
        "🔐 app/index.tsx: Biometria concluída, redirecionando para home"
      );
      setIsRedirecting(true);
      router.replace("/(tabs)/home");
    }
  }, [
    requiresBiometricAuth,
    isAuthenticated,
    isLoading,
    isRedirecting,
    router
  ]);

  const slidesText: SlideTextProps[] = useMemo(
    () => [
      {
        title: "intro.slide1.title",
        description: "intro.slide1.description"
      },
      {
        title: "intro.slide2.title",
        description: "intro.slide1.description"
      },
      {
        title: "intro.slide3.title",
        description: "intro.slide1.description"
      }
    ],
    []
  );

  const imagesPosition = useMemo(() => [65, 50, 49.5], []);

  const images: ImageSource[] = useMemo(
    () => [
      require("../assets/intro-images/intro-slide-1.jpg"),
      require("../assets/intro-images/intro-slide-2.png"),
      require("../assets/intro-images/intro-slide-3.png")
    ],
    []
  );

  useEffect(() => {
    // O AuthContext agora gerencia automaticamente toda a lógica de autenticação
    // incluindo interceptação de login automático e biometria obrigatória.
    // Não fazemos mais nenhuma restauração de sessão aqui para evitar bypass da biometria.
    console.log(
      "app/index.tsx: AuthContext gerencia toda autenticação - não fazendo restore manual"
    );
  }, [loginAction, isLoading, isAuthenticated, requiresBiometricAuth]);

  const onPageScroll = useCallback(
    (
      e: NativeSyntheticEvent<
        Readonly<{
          position: Double;
          offset: Double;
        }>
      >
    ) => {
      setSlideState({
        index: e.nativeEvent.position,
        offset: e.nativeEvent.offset
      });
    },
    []
  );

  const onPageChange = useCallback((slide: number) => {
    pageViewRef.current?.setPage(slide);
  }, []);

  // Mostrar prompt de biometria se necessário (gerenciado pelo AuthContext)
  if (requiresBiometricAuth) {
    console.log("🔐 app/index.tsx: Mostrando tela de biometria obrigatória");
    return (
      <>
        <BackgroundLogoTexture gray={true} />
        <StartupBiometricPrompt
          isAuthenticating={false} // O AuthContext gerencia o estado de loading
          onBiometricPress={authenticateWithBiometrics}
          onSkipPress={skipBiometricAuth}
        />
      </>
    );
  }

  // Log do estado atual para debug
  console.log("📊 app/index.tsx Estado:", {
    isLoading,
    isAuthenticated,
    requiresBiometricAuth,
    isRedirecting
  });

  // Se estiver redirecionando, não renderizar nada
  if (isRedirecting) {
    console.log("🔄 app/index.tsx: Redirecionando, não renderizando tela");
    return null;
  }

  // Mostrar tela de introdução normal
  return (
    <View style={styles.screenView}>
      <PagerView
        ref={pageViewRef}
        initialPage={0}
        style={styles.screenView}
        onPageScroll={onPageScroll}
      >
        {images.map((image, index) => (
          <Slide
            key={`slide-${index}-${imagesPosition[index]}`}
            id={index}
            imageLeftPosition={imagesPosition[index]}
            image={image}
          />
        ))}
      </PagerView>
      <OverlayTop />
      <Overlay
        currentSlide={slideState.index}
        texts={slidesText}
        slideState={slideState.offset}
        onChangeSlide={onPageChange}
      />
    </View>
  );
};

export default IndexPage;
