import React, {useMemo, useState} from "react";
import {Text, View, TouchableOpacity, Alert} from "react-native";
import Screen<PERSON>ithHeader from "../../components/screen-with-header";
import styles from "@/styles/settings/privacy.style";
import {useCurrentUser} from "@/hooks/api/use-users";

interface PrivacySettings {
  showLocation: boolean;
  showPhone: boolean;
  showEmail: boolean;
  showCurrentNeeds: boolean;
  showObjectives: boolean;
}

const Privacy: React.FC = () => {
  const [settings, setSettings] = useState<PrivacySettings>({
    showLocation: true,
    showPhone: true,
    showEmail: true,
    showCurrentNeeds: true,
    showObjectives: false
  });

  const updateSetting = (key: keyof PrivacySettings, value: boolean) => {
    setSettings((prev) => ({
      ...prev,
      [key]: value
    }));
  };

  const handleRestoreDefaults = () => {
    Alert.alert(
      "Restaurar configurações padrão",
      "Tem certeza que deseja restaurar todas as configurações de privacidade para os valores padrão?",
      [
        {
          text: "Cancelar",
          style: "cancel"
        },
        {
          text: "Restaurar",
          onPress: () => {
            setSettings({
              showLocation: true,
              showPhone: true,
              showEmail: true,
              showCurrentNeeds: true,
              showObjectives: false
            });
          }
        }
      ]
    );
  };

  const {data: user} = useCurrentUser();

  const getLocationDisplay = (u: any) => {
    if (u?.address?.city && u?.address?.state) {
      return `${u.address.city} - ${u.address.state}`;
    }
    if (u?.address?.city) return u.address.city;
    return "Não informado";
  };

  const formatObjectivesSubtitle = (count: number) => {
    if (!count) return "Nenhum objetivo";
    return `${count} objetivo${count > 1 ? "s" : ""}`;
  };

  const privacyItems: Array<{
    key: keyof PrivacySettings;
    title: string;
    subtitle: string;
  }> = useMemo(
    () => [
      {
        key: "showLocation",
        title: "Localização",
        subtitle: getLocationDisplay(user)
      },
      {
        key: "showPhone",
        title: "Telefone",
        subtitle: user?.phoneNumber || user?.phone || "Não informado"
      },
      {
        key: "showEmail",
        title: "E-mail",
        subtitle: user?.email || "Não informado"
      },
      {
        key: "showCurrentNeeds",
        title: "Necessidade atual",
        subtitle: "Não informado"
      },
      {
        key: "showObjectives",
        title: "Objetivos",
        subtitle: formatObjectivesSubtitle(user?.objectives?.length || 0)
      }
    ],
    [user]
  );

  return (
    <ScreenWithHeader
      screenTitle="Opções de privacidade"
      backButton
      disablePadding
    >
      <ScreenWithHeader.InternalPadding style={styles.contentContainer}>
        <Text style={styles.sectionTitle}>Alterar visibilidade de:</Text>

        {privacyItems.map((item) => (
          <View key={item.key} style={styles.privacyItem}>
            <View>
              <Text style={styles.privacyTitle}>{item.title}</Text>
              <Text style={styles.privacySubtitle}>{item.subtitle}</Text>
            </View>
            <View style={styles.privacyControl}>
              <Text style={styles.privacyStatus}>
                {settings[item.key]
                  ? "(Visível para todos)"
                  : "(Não visível para todos)"}
              </Text>
              <TouchableOpacity
                style={[
                  styles.toggleBase,
                  settings[item.key]
                    ? styles.toggleEnabled
                    : styles.toggleDisabled
                ]}
                onPress={() => updateSetting(item.key, !settings[item.key])}
                activeOpacity={0.8}
              >
                <View
                  style={[
                    styles.toggleButton,
                    settings[item.key]
                      ? styles.toggleButtonEnabled
                      : styles.toggleButtonDisabled
                  ]}
                />
              </TouchableOpacity>
            </View>
          </View>
        ))}

        <TouchableOpacity
          style={styles.restoreButton}
          onPress={handleRestoreDefaults}
        >
          <Text style={styles.restoreButtonText}>
            Restaurar configurações padrão
          </Text>
        </TouchableOpacity>
      </ScreenWithHeader.InternalPadding>
    </ScreenWithHeader>
  );
};

export default Privacy;
