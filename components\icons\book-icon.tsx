import React, {useMemo} from "react";
import Svg, {SvgProps, Path} from "react-native-svg";

// Book icon based on the SVG provided by the user (glyph only, no background)
// Color can be customized via `stroke` (preferred), or `color`; defaults to white.
const BookIcon: React.FC<SvgProps> = (props) => {
  const color = useMemo(
    () => (props.stroke as string) || (props.color as string) || "#FFFFFF",
    [props.stroke, props.color]
  );
  return (
    <Svg width={24} height={24} viewBox="0 0 24 24" fill="none" {...props}>
      <Path
        // Center the original path (which had min -0.85..16.85 in Y and -0.85..20.85 in X) inside 24x24
        // X: (24-21.7)/2 + 0.85 = 2.0; Y: (24-17.7)/2 + 0.85 = 4.0
        transform="translate(2, 4)"
        d="M11.7981 -0.321381Q10.6812 0.247752 10 1.25271Q9.31884 0.247752 8.20185 -0.321382Q7.46412 -0.697274 6.29641 -0.79268Q5.59485 -0.85 3.6 -0.85L3.2 -0.85Q2.18524 -0.85 1.81719 -0.819929Q1.14973 -0.765395 0.706127 -0.539369Q-0.118964 -0.118964 -0.539369 0.706127Q-0.765395 1.14973 -0.819929 1.81719Q-0.85 2.18524 -0.85 3.2L-0.85 12.8Q-0.85 13.8148 -0.819929 14.1828Q-0.765395 14.8503 -0.539368 15.2939Q-0.118963 16.119 0.706127 16.5394Q1.14973 16.7654 1.81719 16.8199Q2.18524 16.85 3.2 16.85L16.8 16.85Q17.8148 16.85 18.1828 16.8199Q18.8503 16.7654 19.2939 16.5394Q20.119 16.119 20.5394 15.2939Q20.7654 14.8503 20.8199 14.1828Q20.85 13.8148 20.85 12.8L20.85 3.2Q20.85 2.18524 20.8199 1.81719Q20.7654 1.14973 20.5394 0.706126Q20.119 -0.118965 19.2939 -0.539369Q18.8503 -0.765396 18.1828 -0.819929Q17.8148 -0.85 16.8 -0.85L16.4 -0.85Q14.4052 -0.85 13.7036 -0.79268Q12.5359 -0.697274 11.7981 -0.321381ZM9.09833 3.84202Q9.14677 4.43495 9.1498 6.16436Q9.15 6.27984 9.15 6.4L9.15 15.15L3.2 15.15Q2.25458 15.15 1.95562 15.1256Q1.62257 15.0984 1.47791 15.0247Q1.14498 14.855 0.975343 14.5221Q0.901637 14.3774 0.874425 14.0444Q0.85 13.7454 0.85 12.8L0.85 3.2Q0.85 2.25457 0.874425 1.95562Q0.901637 1.62257 0.975343 1.47791Q1.14498 1.14498 1.47791 0.975343Q1.62257 0.901637 1.95562 0.874425Q2.25457 0.85 3.2 0.85L3.6 0.85Q5.52552 0.85 6.15797 0.901674Q6.99129 0.969758 7.43007 1.19333Q8.34201 1.65799 8.80667 2.56993Q9.03024 3.00871 9.09833 3.84202ZM10.85 15.15L10.85 6.4Q10.85 4.47449 10.9017 3.84203Q10.9698 3.00871 11.1933 2.56993Q11.658 1.65799 12.5699 1.19333Q13.0087 0.969758 13.842 0.901674Q14.4745 0.85 16.4 0.85L16.8 0.85Q17.7454 0.85 18.0444 0.874425Q18.3774 0.901637 18.5221 0.975343Q18.855 1.14498 19.0247 1.47791Q19.0984 1.62256 19.1256 1.95562Q19.15 2.25457 19.15 3.2L19.15 12.8Q19.15 13.7454 19.1256 14.0444Q19.0984 14.3774 19.0247 14.5221Q18.855 14.855 18.5221 15.0247Q18.3774 15.0984 18.0444 15.1256Q17.7454 15.15 16.8 15.15L10.85 15.15Z"
        fill={color}
        fillRule="evenodd"
      />
    </Svg>
  );
};

export default BookIcon;
