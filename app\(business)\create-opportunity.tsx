import React, {useState, useCallback, useEffect} from "react";
import {
  Text,
  View,
  ScrollView,
  TouchableOpacity,
  Alert,
  Image
} from "react-native";
import * as ImagePicker from "expo-image-picker";
import * as ImageManipulator from "expo-image-manipulator";
import <PERSON>WithHeader from "../../components/screen-with-header";
import InputField from "../../components/input-field";
import FullSizeButton from "../../components/full-size-button";
import InvisibleFullSizeButton from "../../components/invisible-full-size-button";
import BottomDrawer from "../../components/bottom-drawer";
import LocationSelector from "../../components/location-selector";
import styles from "../../styles/business/create-opportunity.style";
import ImagePlusIcon from "../../components/icons/image-plus-icon";
import MarkerPinIcon from "../../components/icons/marker-pin-icon";
import ChevronDownIcon from "../../components/icons/chevron-down-icon";
import DollarIcon from "../../components/icons/dollar-icon";
import Type2Icon from "../../components/icons/type2-icon";
import ClockIcon from "../../components/icons/clock-icon";
import BarChartIcon from "../../components/icons/bar-chart-icon";
import TargetIcon from "../../components/icons/target-icon";
import {router, useLocalSearchParams} from "expo-router";
import {
  useCreateOpportunity,
  useUpdateOpportunity,
  useOpportunity,
  useOpportunityPublicationValue,
  useOpportunitySegments,
  useOpportunityCurrentStages
} from "../../hooks/api/use-opportunities";

import {applyCurrencyMask, parseCurrency} from "../../utils/currency-mask";
import {useOpportunityErrorHandler} from "../../hooks/use-opportunity-error-handler";
import {useAuth} from "../../contexts/AuthContext";

import {
  showSuccessToast,
  showErrorToast,
  showWarningToast
} from "@/utils/error-handler";
const segments = [
  {id: "tech", label: "Tecnologia"},
  {id: "marketing", label: "Marketing"},
  {id: "sales", label: "Vendas"},
  {id: "finance", label: "Finanças"},
  {id: "design", label: "Design"},
  {id: "other", label: "Outros"}
];

const stages = [
  {id: "idea", label: "Ideia"},
  {id: "planning", label: "Planejamento"},
  {id: "development", label: "Desenvolvimento"},
  {id: "testing", label: "Testes"},
  {id: "launch", label: "Lançamento"},
  {id: "growth", label: "Crescimento"}
];

const convertImageToWebP = async (imageUri: string): Promise<string> => {
  try {
    console.log(
      "🖼️ [WEBP-CONVERTER] ========== INICIANDO CONVERSÃO =========="
    );
    console.log("🖼️ [WEBP-CONVERTER] URI original:", imageUri);
    console.log("🖼️ [WEBP-CONVERTER] Configurações de conversão:", {
      targetWidth: 800,
      quality: 0.8,
      format: "WebP",
      outputBase64: false
    });

    const manipulatedImage = await ImageManipulator.manipulateAsync(
      imageUri,
      [{resize: {width: 800}}],
      {
        compress: 0.8,
        format: ImageManipulator.SaveFormat.WEBP,
        base64: false
      }
    );

    console.log("🖼️ [WEBP-CONVERTER] Manipulação concluída:", {
      originalUri: imageUri,
      newUri: manipulatedImage.uri,
      newWidth: manipulatedImage.width,
      newHeight: manipulatedImage.height,
      format: "WebP (expo-image-manipulator)",
      aspectRatio: (manipulatedImage.width / manipulatedImage.height).toFixed(2)
    });

    if (!manipulatedImage.uri) {
      console.error("❌ [WEBP-CONVERTER] URI não foi gerado pela manipulação");
      throw new Error("Falha ao obter URI da imagem WebP");
    }

    console.log(
      "✅ [WEBP-CONVERTER] ========== CONVERSÃO CONCLUÍDA =========="
    );
    console.log("✅ [WEBP-CONVERTER] Estatísticas finais:", {
      originalUri: imageUri,
      processedUri: manipulatedImage.uri,
      width: manipulatedImage.width,
      height: manipulatedImage.height,
      compressionApplied: "80% quality WebP + redimensionamento para 800px",
      readyForFormData: true
    });

    return manipulatedImage.uri;
  } catch (error) {
    console.error(
      "❌ [WEBP-CONVERTER] ========== ERRO NA CONVERSÃO =========="
    );
    console.error("❌ [WEBP-CONVERTER] Detalhes do erro:", error);
    console.error("❌ [WEBP-CONVERTER] URI que causou erro:", imageUri);
    throw new Error("Falha ao converter imagem para WebP");
  }
};

const CreateOpportunity: React.FC = () => {
  const params = useLocalSearchParams();
  const opportunityId = params.opportunityId
    ? parseInt(params.opportunityId as string)
    : null;
  const isEditMode = !!opportunityId;

  const [formData, setFormData] = useState({
    title: "",
    description: "",
    value: "",
    segment: "",
    currentStage: "",
    targetMarket: "",
    addressState: "SC",
    addressCity: "Itajaí",
    addressNeighborhood: "",
    addressStreet: "",
    addressNumber: "",
    addressZip: "",
    addressComplement: "",
    location: "Itajaí - SC",
    featuredImage: null as string | null,
    processedImageUri: null as string | null
  });

  const [isSegmentModalVisible, setIsSegmentModalVisible] = useState(false);
  const [isStageModalVisible, setIsStageModalVisible] = useState(false);
  const [isLocationSelectorVisible, setIsLocationSelectorVisible] =
    useState(false);

  const {data: segmentsData} = useOpportunitySegments();
  const {data: stagesData} = useOpportunityCurrentStages();
  const createOpportunityMutation = useCreateOpportunity();
  const updateOpportunityMutation = useUpdateOpportunity();
  const {data: opportunityData, isLoading: isLoadingOpportunity} =
    useOpportunity(opportunityId || 0, {enabled: isEditMode});

  const [createdOpportunityId, setCreatedOpportunityId] = useState<
    number | null
  >(null);
  const {data: publicationValue, isLoading: isLoadingValue} =
    useOpportunityPublicationValue(createdOpportunityId || 0);

  // Authentication context
  const {user} = useAuth();

  const {validateAndShowError, handleOpportunityApiError} =
    useOpportunityErrorHandler();

  // Check if user has permission to edit this opportunity
  useEffect(() => {
    if (isEditMode && opportunityData && user) {
      // Check if the current user is the owner of the opportunity
      if (opportunityData.user.id !== parseInt(user.id)) {
        console.warn(
          "🚫 [PERMISSION-CHECK] User does not own this opportunity",
          {
            currentUserId: user.id,
            opportunityOwnerId: opportunityData.user.id,
            opportunityId: opportunityData.id
          }
        );

        // Show error and navigate back
        handleOpportunityApiError({
          status: 403,
          message: "Você não tem permissão para editar esta oportunidade."
        });
        router.back();
        return;
      }
    }
  }, [isEditMode, opportunityData, user, handleOpportunityApiError, router]);

  // Pre-fill form data when in edit mode and opportunity data is loaded
  useEffect(() => {
    if (isEditMode && opportunityData) {
      setFormData({
        title: opportunityData.title || "",
        description: opportunityData.description || "",
        value: applyCurrencyMask(opportunityData.value?.toString() || "0", ""),
        segment: opportunityData.segment?.id?.toString() || "",
        currentStage: opportunityData.currentStage?.id?.toString() || "",
        targetMarket: (opportunityData as any).targetMarket || "Mercado geral", // Default value if not in response
        addressState: "SC", // These address fields might not be in the response
        addressCity: "Itajaí",
        addressNeighborhood: "",
        addressStreet: "",
        addressNumber: "",
        addressZip: "",
        addressComplement: "",
        location: "Itajaí - SC",
        featuredImage: opportunityData.imageUrl || null,
        processedImageUri: null
      });
    }
  }, [isEditMode, opportunityData]);

  const handleInputChange =
    (field: keyof typeof formData) => (value: string) => {
      if (field === "value") {
        const maskedValue = applyCurrencyMask(value, formData.value);
        setFormData((prev) => ({...prev, [field]: maskedValue}));
      } else {
        setFormData((prev) => ({...prev, [field]: value}));
      }
    };

  const handleImageUpload = useCallback(async () => {
    console.log(
      "📸 [IMAGE-SELECTION] ========== INICIANDO SELEÇÃO DE IMAGEM =========="
    );
    console.log("📸 [IMAGE-SELECTION] Verificando permissões da galeria...");

    const {status} = await ImagePicker.requestMediaLibraryPermissionsAsync();
    if (status !== "granted") {
      console.log(
        "❌ [IMAGE-SELECTION] Permissão da galeria negada pelo usuário"
      );
      showWarningToast(
        "Permissão necessária",
        "Precisamos de permissão para acessar suas fotos."
      );
      return;
    }

    console.log("✅ [IMAGE-SELECTION] Permissão da galeria concedida");
    console.log(
      "📸 [IMAGE-SELECTION] Exibindo opções de seleção para o usuário"
    );

    Alert.alert("Selecionar foto destaque", "Escolha uma opção", [
      {
        text: "Câmera",
        onPress: async () => {
          console.log(
            "📸 [IMAGE-SELECTION] ========== USUÁRIO ESCOLHEU CÂMERA =========="
          );
          console.log(
            "📸 [IMAGE-SELECTION] Verificando permissões da câmera..."
          );

          try {
            const cameraPermission =
              await ImagePicker.requestCameraPermissionsAsync();
            if (cameraPermission.status !== "granted") {
              console.log(
                "❌ [IMAGE-SELECTION] Permissão da câmera negada pelo usuário"
              );
              showWarningToast(
                "Permissão necessária",
                "Precisamos de permissão para usar a câmera."
              );
              return;
            }

            console.log("✅ [IMAGE-SELECTION] Permissão da câmera concedida");
            console.log("📸 [IMAGE-SELECTION] Abrindo interface da câmera...");

            const result = await ImagePicker.launchCameraAsync({
              mediaTypes: ["images"],
              allowsEditing: true,
              aspect: [16, 9],
              quality: 0.8
            });

            console.log("📸 [IMAGE-SELECTION] Resultado da câmera:", {
              canceled: result.canceled,
              hasAssets: (result.assets?.length || 0) > 0,
              firstAssetUri: result.assets?.[0]?.uri || "N/A"
            });

            if (!result.canceled && result.assets[0]) {
              console.log(
                "✅ [IMAGE-SELECTION] Imagem capturada com sucesso da câmera"
              );
              console.log(
                "📸 [IMAGE-SELECTION] URI da imagem:",
                result.assets[0].uri
              );
              await processSelectedImage(result.assets[0].uri);
            } else {
              console.log(
                "ℹ️ [IMAGE-SELECTION] Usuário cancelou a captura da câmera"
              );
            }
          } catch (error) {
            console.error("❌ [IMAGE-SELECTION] Erro ao abrir câmera:", error);
            showErrorToast(error, "Falha ao abrir a câmera. Tente novamente.");
          }
        }
      },
      {
        text: "Galeria",
        onPress: async () => {
          console.log(
            "📸 [IMAGE-SELECTION] ========== USUÁRIO ESCOLHEU GALERIA =========="
          );
          console.log("📸 [IMAGE-SELECTION] Abrindo interface da galeria...");

          try {
            const result = await ImagePicker.launchImageLibraryAsync({
              mediaTypes: ["images"],
              allowsEditing: true,
              aspect: [16, 9],
              quality: 0.8
            });

            console.log("📸 [IMAGE-SELECTION] Resultado da galeria:", {
              canceled: result.canceled,
              hasAssets: (result.assets?.length || 0) > 0,
              firstAssetUri: result.assets?.[0]?.uri || "N/A"
            });

            if (!result.canceled && result.assets[0]) {
              console.log(
                "✅ [IMAGE-SELECTION] Imagem selecionada com sucesso da galeria"
              );
              console.log(
                "📸 [IMAGE-SELECTION] URI da imagem:",
                result.assets[0].uri
              );
              await processSelectedImage(result.assets[0].uri);
            } else {
              console.log(
                "ℹ️ [IMAGE-SELECTION] Usuário cancelou a seleção da galeria"
              );
            }
          } catch (error) {
            console.error("❌ [IMAGE-SELECTION] Erro ao abrir galeria:", error);
            showErrorToast(error, "Falha ao abrir a galeria. Tente novamente.");
          }
        }
      },
      {
        text: "Cancelar",
        style: "cancel",
        onPress: () => {
          console.log(
            "ℹ️ [IMAGE-SELECTION] Usuário cancelou a seleção de imagem"
          );
        }
      }
    ]);
  }, []);

  const processSelectedImage = useCallback(async (imageUri: string) => {
    console.log(
      "🔄 [IMAGE-PROCESSING] ========== INICIANDO PROCESSAMENTO =========="
    );
    console.log("🔄 [IMAGE-PROCESSING] URI da imagem recebida:", imageUri);
    console.log("🔄 [IMAGE-PROCESSING] Iniciando conversão para WebP...");

    try {
      const processedUri = await convertImageToWebP(imageUri);

      console.log("🔄 [IMAGE-PROCESSING] Conversão WebP concluída com sucesso");
      console.log("🔄 [IMAGE-PROCESSING] Atualizando estado do formulário...");
      setFormData((prev) => ({
        ...prev,
        featuredImage: imageUri,
        processedImageUri: processedUri
      }));

      console.log(
        "✅ [IMAGE-PROCESSING] ========== PROCESSAMENTO CONCLUÍDO =========="
      );
      console.log("✅ [IMAGE-PROCESSING] Estado atualizado:", {
        featuredImageSet: !!imageUri,
        processedImageSet: !!processedUri,
        originalUri: imageUri,
        processedUri: processedUri,
        readyForFormData: true
      });

      showSuccessToast("Sucesso", "Imagem selecionada com sucesso!");
    } catch (error) {
      console.error(
        "❌ [IMAGE-PROCESSING] ========== ERRO NO PROCESSAMENTO =========="
      );
      console.error("❌ [IMAGE-PROCESSING] Detalhes do erro:", error);
      console.error("❌ [IMAGE-PROCESSING] URI que causou erro:", imageUri);

      showErrorToast(
        error,
        "Falha ao processar a imagem. Verifique se o arquivo é uma imagem válida."
      );
    }
  }, []);

  const handleSegmentSelect = useCallback(() => {
    setIsSegmentModalVisible(true);
  }, []);

  const handleSegmentModalSelect = useCallback(
    (value: string) => {
      handleInputChange("segment")(value);
      setIsSegmentModalVisible(false);
    },
    [handleInputChange]
  );

  const handleSegmentModalClose = useCallback(() => {
    setIsSegmentModalVisible(false);
  }, []);

  const handleStageSelect = useCallback(() => {
    setIsStageModalVisible(true);
  }, []);

  const handleStageModalSelect = useCallback(
    (value: string) => {
      handleInputChange("currentStage")(value);
      setIsStageModalVisible(false);
    },
    [handleInputChange]
  );

  const handleStageModalClose = useCallback(() => {
    setIsStageModalVisible(false);
  }, []);

  const handleLocationSelect = useCallback(() => {
    setIsLocationSelectorVisible(true);
  }, []);

  const handleLocationSelectorSelect = useCallback(
    (city: string, state: string) => {
      setFormData((prev) => ({
        ...prev,
        addressCity: city,
        addressState: state,
        location: `${city} - ${state}`
      }));
      setIsLocationSelectorVisible(false);
    },
    []
  );

  const handleLocationSelectorClose = useCallback(() => {
    setIsLocationSelectorVisible(false);
  }, []);

  const getSegmentOptions = useCallback(() => {
    const apiSegments = (segmentsData as any)?.data;
    const availableSegments =
      apiSegments && Array.isArray(apiSegments) ? apiSegments : segments;

    return availableSegments.map((segment) => ({
      label: "name" in segment ? segment.name : segment.label,
      value: segment.id.toString()
    }));
  }, [segmentsData]);

  const getStageOptions = useCallback(() => {
    const apiStages = (stagesData as any)?.data;
    const availableStages =
      apiStages && Array.isArray(apiStages) ? apiStages : stages;

    return availableStages.map((stage) => ({
      label: "name" in stage ? stage.name : stage.label,
      value: stage.id.toString()
    }));
  }, [stagesData]);

  const getSelectedSegmentLabel = () => {
    const apiSegments = (segmentsData as any)?.data;
    const availableSegments =
      apiSegments && Array.isArray(apiSegments) ? apiSegments : segments;
    const selected = availableSegments.find(
      (s) => s.id.toString() === formData.segment
    );
    return selected
      ? "name" in selected
        ? selected.name
        : selected.label
      : "Selecione um segmento";
  };

  const getSelectedStageLabel = () => {
    const apiStages = (stagesData as any)?.data;
    const availableStages =
      apiStages && Array.isArray(apiStages) ? apiStages : stages;
    const selected = availableStages.find(
      (s) => s.id.toString() === formData.currentStage
    );
    return selected
      ? "name" in selected
        ? selected.name
        : selected.label
      : "Selecione o estágio";
  };

  const getSelectedLocationLabel = () => {
    if (formData.addressCity && formData.addressState) {
      const fullLocation = `${formData.addressCity} - ${formData.addressState}`;
      // Truncate location text if it's longer than 18 characters
      if (fullLocation.length > 18) {
        return fullLocation.substring(0, 18) + "...";
      }
      return fullLocation;
    }
    return "Selecione uma localização";
  };

  const dollarIcon = useCallback(() => <DollarIcon />, []);
  const type2Icon = useCallback(() => <Type2Icon />, []);
  const targetIcon = useCallback(() => <TargetIcon />, []);

  const handleGoToPayment = useCallback(async () => {
    const actionType = isEditMode ? "UPDATE" : "CREATE";
    console.log(
      `� [${actionType}-OPPORTUNITY] ========== INICIANDO ${actionType} ==========`
    );
    console.log(
      `🚀 [${actionType}-OPPORTUNITY] Validando dados do formulário...`
    );
    console.log(`🚀 [${actionType}-OPPORTUNITY] Dados do formulário:`, {
      isEditMode,
      opportunityId,
      title: formData.title,
      description: formData.description.substring(0, 50) + "...",
      value: formData.value,
      segment: formData.segment,
      currentStage: formData.currentStage,
      targetMarket: formData.targetMarket,
      hasImage: !!formData.processedImageUri,
      processedImageUri: formData.processedImageUri || "N/A"
    });

    if (formData.processedImageUri) {
      console.log(
        `🖼️ [${actionType}-OPPORTUNITY] ========== IMAGEM DETECTADA ==========`
      );
      console.log(`🖼️ [${actionType}-OPPORTUNITY] Detalhes da imagem:`, {
        hasOriginalImage: !!formData.featuredImage,
        hasProcessedImage: !!formData.processedImageUri,
        originalUri: formData.featuredImage,
        processedUri: formData.processedImageUri,
        readyForFormData: true
      });
    } else {
      console.log(
        `🖼️ [${actionType}-OPPORTUNITY] Nenhuma imagem selecionada - oportunidade será ${
          isEditMode ? "atualizada" : "criada"
        } sem imagem`
      );
    }

    if (validateAndShowError(formData)) {
      return;
    }

    try {
      const segmentId = parseInt(formData.segment);
      const currentStageId = parseInt(formData.currentStage);

      console.log(`🔍 [${actionType}-OPPORTUNITY] Conversão de IDs:`, {
        segmentString: formData.segment,
        stageString: formData.currentStage,
        segmentId: segmentId,
        currentStageId: currentStageId,
        segmentIsNaN: isNaN(segmentId),
        stageIsNaN: isNaN(currentStageId)
      });

      if (isNaN(segmentId)) {
        handleOpportunityApiError({
          status: 400,
          message: "Segmento inválido"
        });
        return;
      }

      if (isNaN(currentStageId)) {
        handleOpportunityApiError({
          status: 400,
          message: "Estágio inválido"
        });
        return;
      }

      console.log(
        `🔧 [${actionType}-OPPORTUNITY] ========== PREPARANDO PAYLOAD ==========`
      );
      console.log(
        `🔧 [${actionType}-OPPORTUNITY] Montando dados para envio à API...`
      );

      // Base opportunity data common to both create and update
      const baseOpportunityData = {
        title: formData.title.trim(),
        description: formData.description.trim(),
        value: parseCurrency(formData.value),
        segmentId: segmentId,
        currentStageId: currentStageId,
        targetMarket: formData.targetMarket.trim(),
        addressState: formData.addressState,
        addressCity: formData.addressCity,
        addressNeighborhood: formData.addressNeighborhood,
        addressStreet: formData.addressStreet,
        addressNumber: formData.addressNumber,
        addressZip: formData.addressZip,
        addressComplement: formData.addressComplement
      };

      // Handle image processing - prioritize processedImageUri (local functionality)
      if (formData.processedImageUri) {
        console.log(
          `🖼️ [${actionType}-OPPORTUNITY] Preparando imagem para FormData...`
        );
        console.log(
          `🖼️ [${actionType}-OPPORTUNITY] URI da imagem processada:`,
          formData.processedImageUri
        );

        const imageFile = {
          uri: formData.processedImageUri,
          type: "image/webp",
          name: "opportunity-image.webp"
        };

        console.log(
          `🖼️ [${actionType}-OPPORTUNITY] Objeto de arquivo criado:`,
          {
            uri: imageFile.uri,
            type: imageFile.type,
            name: imageFile.name,
            readyForFormData: true
          }
        );

        (baseOpportunityData as any).image = imageFile;
      }
      // Create specific data objects for create vs update
      const createOpportunityData = {
        ...baseOpportunityData,
        acceptTerms: true
      };

      const updateOpportunityData = {
        ...baseOpportunityData
      };

      const currentOpportunityData = isEditMode
        ? updateOpportunityData
        : createOpportunityData;
      console.log(`🔧 [${actionType}-OPPORTUNITY] Payload montado com sucesso`);
      console.log(`🔧 [${actionType}-OPPORTUNITY] Dados do payload:`, {
        title: currentOpportunityData.title,
        descriptionLength: currentOpportunityData.description.length,
        value: currentOpportunityData.value,
        segmentId: currentOpportunityData.segmentId,
        currentStageId: currentOpportunityData.currentStageId,
        targetMarket: currentOpportunityData.targetMarket,
        hasImageInPayload: !!(currentOpportunityData as any).image,
        imageFile: (currentOpportunityData as any).image || "N/A",
        acceptTerms:
          (currentOpportunityData as any).acceptTerms || "N/A (edit mode)"
      });

      // Log específico para confirmação de imagem no payload
      if ((currentOpportunityData as any).image) {
        console.log(
          `✅ [${actionType}-OPPORTUNITY] ========== IMAGEM NO PAYLOAD ==========`
        );
        console.log(
          `✅ [${actionType}-OPPORTUNITY] Imagem incluída no payload da API:`,
          {
            imageFormat: "WebP (arquivo)",
            imageFile: (currentOpportunityData as any).image,
            compressionApplied:
              "80% quality WebP + redimensionamento para 800px",
            deliveryMethod: "FormData multipart/form-data"
          }
        );
      } else {
        console.log(
          `ℹ️ [${actionType}-OPPORTUNITY] Oportunidade será ${
            isEditMode ? "atualizada" : "criada"
          } sem imagem`
        );
      }

      console.log(
        `📡 [${actionType}-OPPORTUNITY] ========== ENVIANDO PARA API ==========`
      );
      console.log(
        `📡 [${actionType}-OPPORTUNITY] Iniciando chamada para a API...`
      );

      let result;
      if (isEditMode && opportunityId) {
        console.log(
          `📡 [UPDATE-OPPORTUNITY] Enviando atualização para oportunidade ID: ${opportunityId}`
        );

        result = await updateOpportunityMutation.mutateAsync({
          id: opportunityId,
          data: updateOpportunityData
        });

        console.log(
          "🎉 [UPDATE-OPPORTUNITY] ========== ATUALIZAÇÃO CONCLUÍDA =========="
        );
        console.log(
          "🎉 [UPDATE-OPPORTUNITY] Oportunidade atualizada com sucesso:",
          {
            opportunityId: result.id,
            title: result.title,
            hasImage: !!(updateOpportunityData as any).image,
            imageIncluded: (updateOpportunityData as any).image
              ? "Sim - WebP via FormData"
              : "Não",
            imageFile: (updateOpportunityData as any).image || "N/A",
            responseReceived: true
          }
        );

        console.log(
          "🎉 [UPDATE-OPPORTUNITY] Navegando de volta para lista de oportunidades..."
        );
        router.back();
      } else {
        console.log(
          `📡 [CREATE-OPPORTUNITY] Enviando nova oportunidade para criação`
        );

        result = await createOpportunityMutation.mutateAsync(
          createOpportunityData
        );
        setCreatedOpportunityId(result.id);

        console.log(
          "🎉 [CREATE-OPPORTUNITY] ========== CRIAÇÃO CONCLUÍDA =========="
        );
        console.log(
          "🎉 [CREATE-OPPORTUNITY] Oportunidade criada com sucesso:",
          {
            opportunityId: result.id,
            title: result.title,
            hasImage: !!(createOpportunityData as any).image,
            imageIncluded: (createOpportunityData as any).image
              ? "Sim - WebP via FormData"
              : "Não",
            imageFile: (createOpportunityData as any).image || "N/A",
            responseReceived: true
          }
        );

        console.log(
          "🎉 [CREATE-OPPORTUNITY] Oportunidade criada, aguardando valor de publicação..."
        );
      }
    } catch (error) {
      console.error(
        `❌ [${actionType}-OPPORTUNITY] ========== ERRO NA API ==========`
      );
      console.error(
        `❌ [${actionType}-OPPORTUNITY] Erro ao ${
          isEditMode ? "atualizar" : "criar"
        } oportunidade:`,
        error
      );
      console.error(`❌ [${actionType}-OPPORTUNITY] Dados que causaram erro:`, {
        title: formData.title,
        hasImage: !!formData.processedImageUri,
        processedImageUri: formData.processedImageUri || "N/A",
        segmentId: parseInt(formData.segment),
        currentStageId: parseInt(formData.currentStage)
      });

      handleOpportunityApiError(error);
    }
  }, [
    formData,
    isEditMode,
    opportunityId,
    createOpportunityMutation,
    updateOpportunityMutation,
    validateAndShowError,
    handleOpportunityApiError,
    router
  ]);

  useEffect(() => {
    if (publicationValue && createdOpportunityId) {
      console.log(
        "🎯 [CREATE-OPPORTUNITY] Redirecionando para tela de pagamento principal:",
        {
          opportunityId: createdOpportunityId,
          publicationValue: publicationValue.value,
          opportunityTitle: formData.title
        }
      );

      // Redireciona para a tela de pagamento principal usando os parâmetros padrão
      router.push({
        pathname: "/(logged-stack)/payment",
        params: {
          entityId: createdOpportunityId.toString(),
          entity: "2", // PaymentEntity.Opportunity = 2
          amount: (publicationValue.value / 100).toString(), // Converte de centavos para reais
          entityTitle: formData.title,
          entityDescription: "Publicação da oportunidade",
          entityImageUrl: formData.featuredImage || ""
        }
      });
    }
  }, [
    publicationValue,
    createdOpportunityId,
    formData.title,
    formData.featuredImage
  ]);

  return (
    <ScreenWithHeader
      screenTitle={
        isEditMode ? "Editar oportunidade" : "Criar nova oportunidade"
      }
      backButton
    >
      <ScrollView style={styles.contentContainer}>
        <TouchableOpacity
          style={styles.imageUploadContainer}
          onPress={handleImageUpload}
        >
          {formData.featuredImage ? (
            <>
              <Image
                source={{uri: formData.featuredImage}}
                style={{
                  position: "absolute",
                  top: 0,
                  left: 0,
                  right: 0,
                  bottom: 0,
                  borderRadius: 8,
                  opacity: 0.8
                }}
                resizeMode="cover"
              />
              <View
                style={{
                  position: "absolute",
                  bottom: 16,
                  left: 0,
                  right: 0,
                  alignItems: "center",
                  backgroundColor: "rgba(0,0,0,0.6)",
                  paddingVertical: 8,
                  borderBottomLeftRadius: 8,
                  borderBottomRightRadius: 8
                }}
              >
                <Text style={[styles.imageUploadText, {color: "white"}]}>
                  Alterar foto destaque
                </Text>
              </View>
            </>
          ) : (
            <>
              <ImagePlusIcon />
              <Text style={styles.imageUploadText}>
                Selecionar foto destaque
              </Text>
              <Text style={styles.imageUploadSubtext}>(opcional)</Text>
            </>
          )}
        </TouchableOpacity>

        <View style={styles.formContainer}>
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Título</Text>
            <InputField
              value={formData.title}
              onChangeText={handleInputChange("title")}
              placeholder="Insira o título da oportunidade"
              maxLength={150}
              showCharacterCount={true}
              icon={type2Icon}
              style={styles.customInputField}
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Descrição</Text>
            <InputField
              value={formData.description}
              onChangeText={handleInputChange("description")}
              placeholder="Insira uma descrição"
              multiline={true}
              numberOfLines={4}
              maxLength={250}
              showCharacterCount={true}
              style={[styles.textArea, styles.customInputField]}
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Valor da oportunidade</Text>
            <InputField
              value={formData.value}
              onChangeText={handleInputChange("value")}
              placeholder="0,00"
              icon={dollarIcon}
              inputMode="numeric"
              style={styles.customInputField}
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Segmento</Text>
            <TouchableOpacity
              style={styles.pickerContainer}
              onPress={handleSegmentSelect}
            >
              <ClockIcon />
              <Text
                style={[
                  styles.pickerText,
                  !formData.segment && styles.pickerPlaceholder
                ]}
              >
                {getSelectedSegmentLabel()}
              </Text>
              <ChevronDownIcon />
            </TouchableOpacity>
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Estágio atual</Text>
            <TouchableOpacity
              style={styles.pickerContainer}
              onPress={handleStageSelect}
            >
              <BarChartIcon />
              <Text
                style={[
                  styles.pickerText,
                  !formData.currentStage && styles.pickerPlaceholder
                ]}
              >
                {getSelectedStageLabel()}
              </Text>
              <ChevronDownIcon />
            </TouchableOpacity>
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Mercado alvo</Text>
            <InputField
              value={formData.targetMarket}
              onChangeText={handleInputChange("targetMarket")}
              placeholder="Insira o mercado que deseja atingir"
              icon={targetIcon}
              style={styles.customInputField}
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Localização</Text>
            <TouchableOpacity
              style={styles.pickerContainer}
              onPress={handleLocationSelect}
            >
              <MarkerPinIcon />
              <Text
                style={[
                  styles.pickerText,
                  !formData.addressState && styles.pickerPlaceholder
                ]}
              >
                {getSelectedLocationLabel()}
              </Text>
              <ChevronDownIcon />
            </TouchableOpacity>
          </View>
        </View>

        <View style={styles.buttonContainer}>
          <FullSizeButton
            text={
              createOpportunityMutation.isPending
                ? "Criando oportunidade..."
                : updateOpportunityMutation.isPending
                ? "Atualizando oportunidade..."
                : isLoadingValue
                ? "Consultando valor..."
                : isEditMode
                ? "Salvar alterações"
                : "Ir para o pagamento"
            }
            onPress={handleGoToPayment}
            disabled={
              createOpportunityMutation.isPending ||
              updateOpportunityMutation.isPending ||
              isLoadingValue ||
              (isEditMode && isLoadingOpportunity)
            }
            loading={
              createOpportunityMutation.isPending ||
              updateOpportunityMutation.isPending ||
              isLoadingValue ||
              (isEditMode && isLoadingOpportunity)
            }
          />
          <InvisibleFullSizeButton
            text="Cancelar"
            onPress={() => router.back()}
          />
        </View>
      </ScrollView>

      <BottomDrawer
        visible={isSegmentModalVisible}
        title="Selecione um segmento"
        options={getSegmentOptions()}
        selectedValue={formData.segment}
        onSelect={handleSegmentModalSelect}
        onClose={handleSegmentModalClose}
      />

      <BottomDrawer
        visible={isStageModalVisible}
        title="Selecione o estágio"
        options={getStageOptions()}
        selectedValue={formData.currentStage}
        onSelect={handleStageModalSelect}
        onClose={handleStageModalClose}
      />

      <LocationSelector
        visible={isLocationSelectorVisible}
        selectedCity={formData.addressCity}
        selectedState={formData.addressState}
        onLocationSelect={handleLocationSelectorSelect}
        onClose={handleLocationSelectorClose}
      />
    </ScreenWithHeader>
  );
};

export default CreateOpportunity;
