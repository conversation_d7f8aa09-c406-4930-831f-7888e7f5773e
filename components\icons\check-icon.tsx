import React, {useMemo} from "react";
import Svg, {Path, SvgProps} from "react-native-svg";
import {ColorValue} from "react-native";

export interface CheckIconProps extends SvgProps {
  replaceColor?: ColorValue;
}

const CheckIcon: React.FC<CheckIconProps> = (props) => {
  const color = useMemo(
    () => props.replaceColor ?? "#3CCB7F",
    [props.replaceColor]
  );

  return (
    <Svg
      width={props.width ?? 20}
      height={props.height ?? 20}
      viewBox="0 0 20 20"
      fill="none"
      {...props}
    >
      <Path
        stroke={color}
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M16.667 5 7.5 14.167 3.333 10"
      />
    </Svg>
  );
};

export default CheckIcon;
