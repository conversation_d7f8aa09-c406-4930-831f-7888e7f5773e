import React from "react";
import {useTranslation} from "react-i18next";
import {Text, View, ActivityIndicator} from "react-native";
import styles from "@/styles/components/user/objectives.style";
import ObjectiveCard from "./objective-card";
import {useCurrentUser} from "@/hooks/api/use-users";
import {BadgeType} from "@/models/api/user-badges.models";

const Objectives: React.FC = () => {
  const {t} = useTranslation();

  // Fetch user data to get badges data for progress calculation
  const {data: userData, isLoading, error} = useCurrentUser();

  // Extract badges array directly (not badges.data)
  const badgesData = userData?.badges || [];

  // Debug logs
  console.log("🔍 Objectives Debug - userData:", userData);
  console.log("🔍 Objectives Debug - badges:", userData?.badges);
  console.log("🔍 Objectives Debug - badgesData:", badgesData);

  // Show loading state
  if (isLoading) {
    return (
      <View>
        <View style={styles.titleContainer}>
          <Text style={styles.title}>{t("objectives.title")}</Text>
        </View>
        <View style={styles.cardsContainer}>
          <ActivityIndicator size="small" color="#fff" />
        </View>
      </View>
    );
  }

  // Show error state
  if (error) {
    return (
      <View>
        <View style={styles.titleContainer}>
          <Text style={styles.title}>{t("objectives.title")}</Text>
        </View>
        <View style={styles.cardsContainer}>
          <Text style={styles.errorText}>{t("objectives.error")}</Text>
        </View>
      </View>
    );
  }

  // Show empty state when no badges are available
  if (badgesData.length === 0) {
    return (
      <View>
        <View style={styles.titleContainer}>
          <Text style={styles.title}>{t("objectives.title")}</Text>
        </View>
        <View style={styles.emptyContainer}>
          <Text style={styles.emptyText}>
            {t("objectives.empty.title", "Nenhum objetivo disponível")}
          </Text>
        </View>
      </View>
    );
  }

  return (
    <View>
      <View style={styles.titleContainer}>
        <Text style={styles.title}>{t("objectives.title")}</Text>
      </View>
      <View style={styles.cardsContainer}>
        {badgesData.map((badge) => {
          // Calculate progress for each individual badge
          const badgeCount = badge.count || 0;
          const badgeNextLevelCount = badge.nextLevelCount || 1;
          const progressPercentage = Math.min(
            (badgeCount / badgeNextLevelCount) * 100,
            100
          );
          const progressDecimal = progressPercentage / 100;

          // Map API badge type to enum (indexação baseada em zero)
          const mapApiBadgeTypeToEnum = (apiType: number): BadgeType => {
            switch (apiType) {
              case 0:
                return BadgeType.BRONZE;
              case 1:
                return BadgeType.SILVER;
              case 2:
                return BadgeType.GOLD;
              case 3:
                return BadgeType.PLATINUM;
              case 4:
                return BadgeType.DIAMOND;
              default:
                return BadgeType.BRONZE; // Fallback para Bronze
            }
          };

          const badgeType = mapApiBadgeTypeToEnum(badge.type);

          return (
            <ObjectiveCard
              key={badge.id}
              title={badge.name}
              description={`Progresso do ${badge.name}`}
              progress={progressDecimal}
              accomplished={`Você fez ${badgeCount} vezes desse objetivo!`}
              level={badge.level}
              badgeType={badgeType}
            />
          );
        })}
      </View>
    </View>
  );
};

export default Objectives;
