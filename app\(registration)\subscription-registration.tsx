import React, {useState, useCallback} from "react";
import {
  Text,
  View,
  ImageBackground,
  ScrollView,
  TouchableOpacity,
  Alert,
  ActivityIndicator
} from "react-native";
import Screen from "@/components/screen";
import BackButton from "@/components/back-button";
import {useTranslation} from "react-i18next";
import FullSizeButton from "@/components/full-size-button";
import CheckIcon from "@/components/icons/check-icon";
import styles from "@/styles/registration/subscription-registration.style";
import {useRouter} from "expo-router";
import {useSubscriptionValidation} from "@/hooks/use-registration-validation";
import {usePlansPublic} from "@/hooks/api";
import {PlanViewModel} from "@/services/api/plans/plans.service";
import BottomModal from "@/components/bottom-modal";
import {useBottomModal} from "@/contexts/bottom-modal-context";
import {useRegulationTerm} from "@/hooks/api/use-regulation-term";
import {getLatestVersionFromApiTerm} from "@/models/api/terms.models";

// Use the API interface directly
type SubscriptionPlan = PlanViewModel & {
  isPopular?: boolean;
  features?: string[];
};

const SubscriptionRegistration: React.FC = () => {
  const {t} = useTranslation();
  const router = useRouter();
  const modal = useBottomModal();
  const [selectedPlan, setSelectedPlan] = useState<number | null>(null);

  // Fetch plans from API
  const {
    data: plansResponse,
    isLoading: isLoadingPlans,
    error: plansError
  } = usePlansPublic({page: 1, pageSize: 10});
  const {data: regulation, isLoading: isLoadingRegulation} =
    useRegulationTerm(true);

  // Initialize validation
  const validation = useSubscriptionValidation(
    {selectedPlan: ""},
    (validData) => {
      const selectedPlanData = plans.find(
        (plan) => plan.id === parseInt(validData.selectedPlan)
      );
      console.log(
        "Navegando para personal-data-registration com plano:",
        validData.selectedPlan
      );
      router.push({
        pathname: "/(registration)/personal-data-registration",
        params: {
          selectedPlanId: validData.selectedPlan,
          selectedPlanTermId: (selectedPlanData as any)?.termId ?? ""
        }
      });
    }
  );

  // Transform API data to include UI-specific properties
  const plans: SubscriptionPlan[] = React.useMemo(() => {
    const apiPlans = plansResponse?.data ?? [];
    // Use only API data; do not fabricate or mock plans
    return apiPlans.map((plan) => ({
      ...plan,
      features: plan.description ? [plan.description] : [],
      // Preserve API-provided popularity flags as-is
      isPopular: !!plan.isPopular
    }));
  }, [plansResponse]);

  const handlePlanSelect = useCallback(
    (planId: number) => {
      setSelectedPlan(planId);
      validation.updateField("selectedPlan", planId.toString());
    },
    [validation]
  );

  const handleNext = useCallback(() => {
    if (!selectedPlan) {
      Alert.alert(
        t("subscriptionRegistration.error.title", "Erro de validação"),
        t(
          "subscriptionRegistration.error.selectPlan",
          "Você deve selecionar um plano para continuar"
        )
      );
      return;
    }

    // Validate and proceed
    const isValid = validation.handleSubmit();
    if (!isValid) {
      Alert.alert(
        t("subscriptionRegistration.error.title", "Erro de validação"),
        t(
          "subscriptionRegistration.error.selectPlan",
          "Você deve selecionar um plano para continuar"
        )
      );
    }
  }, [selectedPlan, validation, t]);

  const handleBack = useCallback(() => {
    console.log("Voltando da tela de subscription-registration");
    router.back();
  }, [router]);

  const cleanHtmlContent = (html: string) => {
    return html
      .replace(/<[^>]+>/g, " ")
      .replace(/&nbsp;/g, " ")
      .replace(/&amp;/g, "&")
      .replace(/&lt;/g, "<")
      .replace(/&gt;/g, ">")
      .replace(/&quot;/g, '"')
      .trim();
  };

  const handleOpenRegulamentDrawer = useCallback(() => {
    console.log("📋 [SUBSCRIPTION] Abrindo drawer de regulamento");

    const latest = regulation ? getLatestVersionFromApiTerm(regulation) : null;

    let body: React.ReactNode;
    if (isLoadingRegulation) {
      body = (
        <View style={{paddingVertical: 24, alignItems: "center"}}>
          <ActivityIndicator size="small" color="#FFFFFF" />
          <Text style={{color: "#FFFFFF", marginTop: 12}}>
            Carregando regulamento...
          </Text>
        </View>
      );
    } else if (latest?.content) {
      body = (
        <Text
          style={{
            color: "#FFFFFF",
            fontSize: 14,
            lineHeight: 20,
            fontFamily: "Ubuntu",
            marginBottom: 16
          }}
        >
          {cleanHtmlContent(latest.content)}
        </Text>
      );
    } else {
      body = (
        <Text
          style={{
            color: "#FF6B6B",
            fontSize: 14,
            lineHeight: 20,
            fontFamily: "Ubuntu",
            marginBottom: 16
          }}
        >
          Não foi possível carregar o regulamento.
        </Text>
      );
    }

    modal.openModal({
      title: "Regulamento",
      children: (
        <BottomModal.Container>
          <ScrollView
            style={{maxHeight: 400, paddingHorizontal: 16}}
            showsVerticalScrollIndicator={false}
          >
            {body}
          </ScrollView>
        </BottomModal.Container>
      )
    });
  }, [modal, regulation, isLoadingRegulation]);

  const renderPlanCard = useCallback(
    (plan: SubscriptionPlan) => {
      const isSelected = selectedPlan === plan.id;

      return (
        <TouchableOpacity
          key={plan.id}
          style={[styles.planCard, isSelected && styles.planCardSelected]}
          onPress={() => handlePlanSelect(plan.id)}
          activeOpacity={0.8}
        >
          {isSelected && (
            <View style={styles.greenSection}>
              <View style={styles.checkIcon}>
                <CheckIcon width={24} height={24} replaceColor="#FFFFFF" />
              </View>
            </View>
          )}

          <View
            style={[
              styles.planContent,
              isSelected && styles.planContentSelected
            ]}
          >
            <View style={styles.planHeader}>
              <Text style={styles.planName}>{plan.name}</Text>
              {plan.isPopular && (
                <View
                  style={[
                    styles.popularBadge,
                    isSelected && styles.popularBadgeSelected
                  ]}
                >
                  <Text style={styles.popularText}>
                    {t("subscriptionRegistration.popular", "Popular")}
                  </Text>
                </View>
              )}
            </View>

            <View style={styles.planFeatures}>
              {plan.features?.map((feature, index) => (
                <Text
                  key={`${plan.id}-feature-${index}`}
                  style={[
                    styles.featureText,
                    feature.startsWith("Obs.") && styles.obsText
                  ]}
                >
                  {feature}
                </Text>
              )) || null}
            </View>
          </View>
        </TouchableOpacity>
      );
    },
    [selectedPlan, handlePlanSelect, t]
  );

  return (
    <Screen>
      <ImageBackground
        source={require("@/assets/images/backgroundregistration.png")}
        style={styles.backgroundImage}
        imageStyle={styles.backgroundImageStyle}
      >
        <View style={styles.overlay} />
        <View style={styles.contentContainer}>
          <BackButton />

          <View style={styles.progressContainer}>
            <Text style={styles.progressTitle}>
              {t("subscriptionRegistration.createAccount", "Criar conta")}
            </Text>
            <Text style={styles.progressStep}>
              {t(
                "subscriptionRegistration.stepProgress",
                "1 / 7 Selecionar plano"
              )}
            </Text>
            <View style={styles.progressBar}>
              <View style={[styles.progressFill, {width: "14%"}]} />
            </View>
          </View>

          <View style={styles.headerContainer}>
            <Text style={styles.subtitle}>
              {t(
                "subscriptionRegistration.description",
                "Escolha um plano no qual você mais se identifica."
              )}
            </Text>
          </View>

          <ScrollView
            style={styles.plansContainer}
            showsVerticalScrollIndicator={false}
          >
            {isLoadingPlans ? (
              <View
                style={{
                  flex: 1,
                  justifyContent: "center",
                  alignItems: "center",
                  paddingVertical: 40
                }}
              >
                <ActivityIndicator size="large" color="#007AFF" />
                <Text
                  style={{
                    marginTop: 16,
                    fontSize: 16,
                    color: "#FFFFFF",
                    textAlign: "center"
                  }}
                >
                  {t(
                    "subscriptionRegistration.loading",
                    "Carregando planos..."
                  )}
                </Text>
              </View>
            ) : plansError ? (
              <View
                style={{
                  backgroundColor: "rgba(255, 0, 0, 0.1)",
                  padding: 16,
                  borderRadius: 8,
                  marginBottom: 16
                }}
              >
                <Text
                  style={{
                    color: "#FF6B6B",
                    fontSize: 14,
                    textAlign: "center"
                  }}
                >
                  {t(
                    "subscriptionRegistration.error.loadPlans",
                    "Erro ao carregar planos. Usando dados padrão."
                  )}
                </Text>
              </View>
            ) : null}
            {plans.map(renderPlanCard)}
          </ScrollView>

          <View style={styles.bottomSection}>
            <Text style={styles.regulamentText}>
              {t("subscriptionRegistration.regulament", "Confira o ")}
              <Text
                style={styles.regulamentLink}
                onPress={handleOpenRegulamentDrawer}
              >
                {t("subscriptionRegistration.regulamentLink", "regulamento")}
              </Text>
              {t("subscriptionRegistration.complete", " completo.")}
            </Text>
          </View>

          <View style={styles.buttonContainer}>
            <View style={styles.backButton}>
              <FullSizeButton
                text={t("subscriptionRegistration.back", "Voltar")}
                variant="secondary"
                onPress={handleBack}
              />
            </View>
            <View style={styles.nextButton}>
              <FullSizeButton
                text={t("subscriptionRegistration.next", "Avançar")}
                onPress={handleNext}
                disabled={!selectedPlan}
              />
            </View>
          </View>
        </View>
      </ImageBackground>
    </Screen>
  );
};

export default SubscriptionRegistration;
