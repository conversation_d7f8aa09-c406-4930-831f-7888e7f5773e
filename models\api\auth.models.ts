/**
 * Modelos de autenticação baseados na API ClubM
 */

import {z} from "zod";

// Tipos de requisição de login
export interface LoginRequest {
  email: string;
  password: string;
  activatedBiometrics?: boolean;
  firebaseToken?: string; // Push notification token
}

// Tipos de requisição de login para app (usando documento)
export interface AppLoginRequest {
  document: string;
  password: string;
  activatedBiometrics?: boolean;
  firebaseToken?: string; // Push notification token
}

export const LoginRequestSchema = z.object({
  email: z.string().email("Email inválido").min(1, "Email é obrigatório"),
  password: z.string().min(1, "Senha é obrigatória"),
  activatedBiometrics: z.boolean().optional(),
  firebaseToken: z.string().optional()
});

export const AppLoginRequestSchema = z.object({
  document: z.string().min(1, "Documento é obrigatório"),
  password: z.string().min(1, "Senha é obrigatória"),
  activatedBiometrics: z.boolean().optional(),
  firebaseToken: z.string().optional()
});

// Tipos de resposta de login
export interface LoginResponse {
  tokenType: string;
  accessToken: string;
  expiresIn: number;
  refreshToken?: string;
  user?: UserInfo;
  activatedBiometrics?: boolean;
}

export interface UserInfo {
  id: string;
  name: string;
  email: string;
  document: string;
  phone?: string;
  phoneNumber?: string;
  avatar?: string;
  avatarId?: string;
  isActive: boolean;
  roles: string[];
  permissions: string[];
  preferences?: UserPreferences;
  birthDate?: string;
  companyName?: string;
  createdAt?: string;
  socialMedias?: any[];
  badges?: Array<{
    name: string;
    id: number;
    type: number;
    level: number;
    count: number;
    nextLevelCount: number;
  }>;
  objectives?: Array<{
    id: number;
    title?: string;
    name?: string;
    description?: string;
  }>;
  specializations?: Array<{
    id: number;
    name: string;
    description?: string;
  }>;
  areaInterests?: Array<{
    id: number;
    name: string;
    description?: string;
  }>;
}

export interface UserPreferences {
  language: string;
  timezone: string;
  notifications: NotificationPreferences;
  theme: "light" | "dark" | "auto";
}

export interface NotificationPreferences {
  email: boolean;
  push: boolean;
  sms: boolean;
  marketing: boolean;
  events: boolean;
  chats: boolean;
}

// Tipos de refresh token
export interface RefreshTokenRequest {
  refreshToken: string;
}

export interface RefreshTokenResponse {
  tokenType: string;
  accessToken: string;
  expiresIn: number;
  refreshToken?: string;
}

// Tipos de recuperação de senha
export interface ForgotPasswordRequest {
  email: string;
}

export interface ForgotPasswordResponse {
  message: string;
  type: string;
  property: string;
  key: string;
}

export const ForgotPasswordRequestSchema = z.object({
  email: z.string().email("Email inválido").min(1, "Email é obrigatório")
});

// Tipos de reset de senha
export interface ResetPasswordRequest {
  code: string;
  password: string;
  confirmPassword: string;
}

export interface ResetPasswordResponse {
  success: boolean;
  message: string;
}

export const ResetPasswordRequestSchema = z
  .object({
    code: z.string().length(6, "Código deve ter 6 dígitos"),
    password: z
      .string()
      .min(8, "Senha deve ter pelo menos 8 caracteres")
      .regex(
        /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
        "Senha deve conter ao menos uma letra minúscula, uma maiúscula e um número"
      ),
    confirmPassword: z.string()
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "Senhas não coincidem",
    path: ["confirmPassword"]
  });

// Tipos de logout
export interface LogoutRequest {
  refreshToken?: string;
}

export interface LogoutResponse {
  success: boolean;
  message: string;
}

// Tipos de verificação de token
export interface TokenValidationResponse {
  valid: boolean;
  expiresAt: string;
  user?: UserInfo;
}

// Tipos de mudança de senha
export interface ChangePasswordRequest {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

export interface ChangePasswordResponse {
  success: boolean;
  message: string;
}

export const ChangePasswordRequestSchema = z
  .object({
    currentPassword: z.string().min(1, "Senha atual é obrigatória"),
    newPassword: z
      .string()
      .min(8, "Nova senha deve ter pelo menos 8 caracteres")
      .regex(
        /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
        "Nova senha deve conter ao menos uma letra minúscula, uma maiúscula e um número"
      ),
    confirmPassword: z.string()
  })
  .refine((data) => data.newPassword === data.confirmPassword, {
    message: "Senhas não coincidem",
    path: ["confirmPassword"]
  });

// Tipos de perfil do usuário
export interface UpdateProfileRequest {
  name?: string;
  email?: string;
  phone?: string;
  avatar?: string;
  preferences?: Partial<UserPreferences>;
}

export interface UpdateProfileResponse {
  success: boolean;
  message: string;
  user: UserInfo;
}

export const UpdateProfileRequestSchema = z.object({
  name: z.string().min(2, "Nome deve ter pelo menos 2 caracteres").optional(),
  email: z.string().email("Email inválido").optional(),
  phone: z.string().min(10, "Telefone inválido").optional(),
  avatar: z.string().url("URL do avatar inválida").optional(),
  preferences: z
    .object({
      language: z.string().optional(),
      timezone: z.string().optional(),
      theme: z.enum(["light", "dark", "auto"]).optional(),
      notifications: z
        .object({
          email: z.boolean().optional(),
          push: z.boolean().optional(),
          sms: z.boolean().optional(),
          marketing: z.boolean().optional(),
          events: z.boolean().optional(),
          chats: z.boolean().optional()
        })
        .optional()
    })
    .optional()
});

// Tipos de sessão
export interface SessionData {
  token: string;
  refreshToken?: string;
  user: UserInfo;
  expiresAt: Date;
  issuedAt: Date;
}

// Tipos de autenticação biométrica
export interface BiometricAuthRequest {
  userId: string;
  biometricData: string;
  deviceId: string;
}

export interface BiometricAuthResponse {
  success: boolean;
  tokenType: string;
  accessToken: string;
  expiresIn: number;
  refreshToken?: string;
}

// Tipos de registro de dispositivo
export interface DeviceRegistrationRequest {
  deviceId: string;
  deviceName: string;
  platform: "ios" | "android";
  pushToken?: string;
  biometricEnabled: boolean;
}

export interface DeviceRegistrationResponse {
  success: boolean;
  deviceId: string;
  message: string;
}

// Tipos de auditoria de autenticação
export interface AuthAuditLog {
  id: string;
  userId: string;
  action: "login" | "logout" | "refresh" | "password_change" | "password_reset";
  success: boolean;
  ipAddress: string;
  userAgent: string;
  deviceId?: string;
  timestamp: string;
  details?: Record<string, any>;
}

// Tipos de erro de autenticação
export interface AuthError {
  code: string;
  message: string;
  details?: {
    field?: string;
    attempts?: number;
    lockoutTime?: string;
  };
}

// Enums
export enum AuthAction {
  LOGIN = "login",
  LOGOUT = "logout",
  REFRESH = "refresh",
  FORGOT_PASSWORD = "forgot_password",
  RESET_PASSWORD = "reset_password",
  CHANGE_PASSWORD = "change_password",
  UPDATE_PROFILE = "update_profile",
  BIOMETRIC_AUTH = "biometric_auth"
}

export enum AuthErrorCode {
  INVALID_CREDENTIALS = "INVALID_CREDENTIALS",
  ACCOUNT_LOCKED = "ACCOUNT_LOCKED",
  TOKEN_EXPIRED = "TOKEN_EXPIRED",
  TOKEN_INVALID = "TOKEN_INVALID",
  REFRESH_TOKEN_EXPIRED = "REFRESH_TOKEN_EXPIRED",
  PASSWORD_TOO_WEAK = "PASSWORD_TOO_WEAK",
  EMAIL_NOT_VERIFIED = "EMAIL_NOT_VERIFIED",
  BIOMETRIC_NOT_AVAILABLE = "BIOMETRIC_NOT_AVAILABLE",
  DEVICE_NOT_REGISTERED = "DEVICE_NOT_REGISTERED"
}

// Tipos de validação
export type ValidatedLoginRequest = z.infer<typeof LoginRequestSchema>;
export type ValidatedForgotPasswordRequest = z.infer<
  typeof ForgotPasswordRequestSchema
>;
export type ValidatedResetPasswordRequest = z.infer<
  typeof ResetPasswordRequestSchema
>;
export type ValidatedChangePasswordRequest = z.infer<
  typeof ChangePasswordRequestSchema
>;
export type ValidatedUpdateProfileRequest = z.infer<
  typeof UpdateProfileRequestSchema
>;
