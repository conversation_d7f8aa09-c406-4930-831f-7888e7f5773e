import {ApiResponse} from "@/models/api/common.models";
import {
  CreateIndicationRequest,
  CreateIndicationResponse,
  Indication,
  IndicationStats,
  IndicationsListParams,
  IndicationsListResponse
} from "@/models/api/indications.models";
import {apiClient} from "@/services/api/base/api-client";
import {firstValueFrom} from "rxjs";

/**
 * Serviço para gerenciar indicações
 */
export class IndicationsService {
  private static readonly BASE_PATH = "/api/app/indications";

  /**
   * Cria uma nova indicação
   */
  static async createIndication(
    data: CreateIndicationRequest
  ): Promise<ApiResponse<CreateIndicationResponse>> {
    try {
      console.log("📤 [INDICATIONS] Criando indicação...");

      const response = await firstValueFrom(
        apiClient.post<CreateIndicationResponse>(this.BASE_PATH, data)
      );

      console.log("✅ [INDICATIONS] Indicação criada com sucesso");

      return {
        success: true,
        data: response,
        message: "Indicação criada com sucesso!"
      };
    } catch (error: any) {
      console.error("❌ [INDICATIONS] Erro ao criar indicação:", error);

      return {
        success: false,
        data: null as any,
        message:
          error.response?.data?.message ||
          error.message ||
          "Erro ao criar indicação. Tente novamente.",
        errors: error.response?.data || error.message
      };
    }
  }

  /**
   * Lista as indicações do usuário
   */
  static async getIndications(
    params?: IndicationsListParams
  ): Promise<ApiResponse<IndicationsListResponse>> {
    try {
      console.log("📤 [INDICATIONS] Buscando indicações...", params);

      const response = await firstValueFrom(
        apiClient.get<IndicationsListResponse>(this.BASE_PATH, params)
      );

      console.log("✅ [INDICATIONS] Indicações carregadas:", response);

      return {
        success: true,
        data: response,
        message: "Indicações carregadas com sucesso!"
      };
    } catch (error: any) {
      console.error("❌ [INDICATIONS] Erro ao carregar indicações:", error);

      return {
        success: false,
        data: null as any,
        message:
          error.response?.data?.message ||
          error.message ||
          "Erro ao carregar indicações. Tente novamente.",
        errors: error.response?.data || error.message
      };
    }
  }

  /**
   * Busca uma indicação específica por ID
   */
  static async getIndicationById(id: string): Promise<ApiResponse<Indication>> {
    try {
      const response = await firstValueFrom(
        apiClient.get<Indication>(`${this.BASE_PATH}/${id}`)
      );

      return {
        success: true,
        data: response,
        message: "Indicação carregada com sucesso!"
      };
    } catch (error: any) {
      console.error("❌ [INDICATIONS] Erro ao carregar indicação:", error);

      return {
        success: false,
        data: null as any,
        message:
          error.response?.data?.message ||
          error.message ||
          "Erro ao carregar indicação. Tente novamente.",
        errors: error.response?.data || error.message
      };
    }
  }

  /**
   * Calcula estatísticas de indicações baseado nos dados existentes
   * Como a API não possui endpoint /stats, calculamos localmente
   */
  static async getIndicationStats(): Promise<ApiResponse<IndicationStats>> {
    try {
      console.log("📊 [INDICATIONS] Calculando estatísticas localmente...");

      // Buscar todas as indicações do usuário
      const indicationsResponse = await this.getIndications({
        page: 1,
        pageSize: 1000 // Buscar um número grande para ter todas as indicações
      });

      if (!indicationsResponse.success || !indicationsResponse.data) {
        return {
          success: false,
          data: null as any,
          message: "Erro ao buscar indicações para calcular estatísticas",
          errors: indicationsResponse.errors
        };
      }

      const indications = indicationsResponse.data.items || [];

      // Calcular estatísticas
      const totalIndications = indications.length;
      const pendingIndications = indications.filter(
        (i) => i.status === "pending"
      ).length;
      const acceptedIndications = indications.filter(
        (i) => i.status === "accepted"
      ).length;
      const rejectedIndications = indications.filter(
        (i) => i.status === "rejected"
      ).length;
      const cancelledIndications = indications.filter(
        (i) => i.status === "cancelled"
      ).length;
      const expiredIndications = indications.filter(
        (i) => i.status === "expired"
      ).length;

      // Calcular taxa de conversão
      const conversionRate =
        totalIndications > 0 ? acceptedIndications / totalIndications : 0;

      // Calcular indicações deste mês
      const now = new Date();
      const thisMonthStart = new Date(now.getFullYear(), now.getMonth(), 1);
      const thisMonthIndications = indications.filter(
        (i) => new Date(i.createdAt) >= thisMonthStart
      ).length;

      // Calcular indicações do mês passado
      const lastMonthStart = new Date(now.getFullYear(), now.getMonth() - 1, 1);
      const lastMonthEnd = new Date(now.getFullYear(), now.getMonth(), 0);
      const lastMonthIndications = indications.filter((i) => {
        const createdAt = new Date(i.createdAt);
        return createdAt >= lastMonthStart && createdAt <= lastMonthEnd;
      }).length;

      const stats: IndicationStats = {
        totalIndications,
        pendingIndications,
        acceptedIndications,
        rejectedIndications,
        cancelledIndications,
        expiredIndications,
        conversionRate,
        thisMonthIndications,
        lastMonthIndications
      };

      console.log("✅ [INDICATIONS] Estatísticas calculadas:", stats);

      return {
        success: true,
        data: stats,
        message: "Estatísticas calculadas com sucesso!"
      };
    } catch (error: any) {
      console.error("❌ [INDICATIONS] Erro ao calcular estatísticas:", error);

      return {
        success: false,
        data: null as any,
        message:
          error.response?.data?.message ||
          error.message ||
          "Erro ao calcular estatísticas. Tente novamente.",
        errors: error.response?.data || error.message
      };
    }
  }
}
