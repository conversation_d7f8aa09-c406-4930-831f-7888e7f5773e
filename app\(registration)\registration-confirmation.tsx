import React, {useCallback} from "react";
import {Text, View, ImageBackground, TouchableOpacity} from "react-native";
import Screen from "../../components/screen";
import {useTranslation} from "react-i18next";
import HandshakeIcon from "../../components/icons/handshake-icon";
import styles from "@/styles/registration/registration-confirmation.style";
import {useRegistrationAnalysis} from "@/contexts/registration-analysis-context";
import {useRouter} from "expo-router";

const RegistrationConfirmation: React.FC = () => {
  const {t} = useTranslation();
  const {activate} = useRegistrationAnalysis();
  const router = useRouter();

  const handleViewStatus = useCallback(async () => {
    try {
      await activate();
    } catch {}
    router.replace("/(registration)/registration-status");
  }, [activate, router]);

  const handleBackToStart = useCallback(() => {
    router.replace("/(auth)/login");
  }, [router]);

  return (
    <Screen>
      <ImageBackground
        source={require("../../assets/images/backgroundregistration.png")}
        style={styles.backgroundImage}
        imageStyle={styles.backgroundImageStyle}
      >
        <View style={styles.overlay} />
        <View style={styles.contentContainer}>
          <View style={styles.progressContainer}>
            <Text style={styles.progressTitle}>
              {t("registrationConfirmation.createAccount", "Criar conta")}
            </Text>
            <Text style={styles.progressStep}>
              {t("registrationConfirmation.stepProgress", "7 / 7 Conclusão")}
            </Text>
            <View style={styles.progressBar}>
              <View style={[styles.progressFill, {width: "100%"}]} />
            </View>
          </View>

          <View style={styles.mainContent}>
            <View style={styles.iconContainer}>
              <HandshakeIcon width={72} height={72} stroke="#fff" />
            </View>

            <View style={styles.titleContainer}>
              <Text style={styles.title}>
                {t(
                  "registrationConfirmation.title",
                  "Agradecemos seu interesse em fazer parte do clube!"
                )}
              </Text>
            </View>

            <View style={styles.descriptionContainer}>
              <Text style={styles.description}>
                {t(
                  "registrationConfirmation.description",
                  "Suas informações foram recebidas e estão em análise."
                )}
              </Text>
            </View>

            <View style={styles.buttonContainer}>
              <TouchableOpacity
                style={styles.primaryButton}
                onPress={handleViewStatus}
              >
                <Text style={styles.primaryButtonText}>
                  {t(
                    "registrationConfirmation.checkStatus",
                    "Ver status de aprovação"
                  )}
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.secondaryButton}
                onPress={handleBackToStart}
              >
                <Text style={styles.secondaryButtonText}>
                  {t(
                    "registrationConfirmation.backToStart",
                    "Voltar ao início"
                  )}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </ImageBackground>
    </Screen>
  );
};

export default RegistrationConfirmation;
