/**
 * Serviço de autenticação integrado com TokenManager
 */

import {apiClient} from "../base/api-client";
import {tokenManager} from "./token-manager";
import {ApiLogger} from "../base/api-logger";
import {firstValueFrom} from "rxjs";
import {
  LoginRequest,
  AppLoginRequest,
  LoginResponse,
  LogoutRequest,
  LogoutResponse,
  RefreshTokenResponse,
  ForgotPasswordRequest,
  ForgotPasswordResponse,
  ResetPasswordRequest,
  ResetPasswordResponse,
  ChangePasswordRequest,
  ChangePasswordResponse,
  TokenValidationResponse,
  UserInfo,
  BiometricAuthRequest,
  BiometricAuthResponse,
  DeviceRegistrationRequest,
  DeviceRegistrationResponse
} from "@/models/api/auth.models";
import {TokenData} from "../base/api-types";

export class AuthService {
  /**
   * Realizar login (web - com email)
   */
  static async login(request: LoginRequest): Promise<LoginResponse> {
    try {
      ApiLogger.info("Iniciando processo de login", {email: request.email});

      const response = await firstValueFrom(
        apiClient.post<LoginResponse>("/auth/login", request)
      );

      // Configurar token manager com os dados recebidos
      if (response.accessToken) {
        let finalTokenData: TokenData;

        if (response.activatedBiometrics === true) {
          // Token é para "login rápido", precisa fazer refresh para obter token real
          ApiLogger.info(
            "Biometria ativada detectada, fazendo refresh do token"
          );

          const refreshResponse = await this.refreshTokenWithQuickLogin(
            response.accessToken
          );

          finalTokenData = {
            accessToken: refreshResponse.accessToken,
            refreshToken: refreshResponse.refreshToken,
            tokenType: refreshResponse.tokenType,
            expiresIn: refreshResponse.expiresIn,
            expiresAt: new Date(Date.now() + refreshResponse.expiresIn * 1000)
          };

          ApiLogger.info(
            "Token de autenticação obtido via refresh (biometria ativada)"
          );
        } else {
          // Token pode ser usado diretamente
          ApiLogger.info("Biometria não ativada, usando token diretamente");

          finalTokenData = {
            accessToken: response.accessToken,
            refreshToken: response.refreshToken,
            tokenType: response.tokenType,
            expiresIn: response.expiresIn,
            expiresAt: new Date(Date.now() + response.expiresIn * 1000)
          };
        }

        await tokenManager.setTokenData(finalTokenData);

        // Configurar token no API client também
        apiClient.setAuthToken(finalTokenData);

        // Configurar callbacks do token manager
        tokenManager.setOnTokenExpired(() => {
          ApiLogger.warn("Token expirado, redirecionando para login");
          // Aqui você pode adicionar lógica para redirecionar para login
        });

        ApiLogger.info("Login realizado com sucesso", {
          userId: response.user?.id,
          tokenType: finalTokenData.tokenType,
          biometricFlow: response.activatedBiometrics === true
        });
      }

      return response;
    } catch (error) {
      ApiLogger.error("Erro no login", error as Error);
      throw error;
    }
  }

  /**
   * Realizar login do app (com documento)
   */
  static async appLogin(request: AppLoginRequest): Promise<LoginResponse> {
    try {
      ApiLogger.info("Iniciando processo de login do app", {
        document: request.document
      });

      const response = await firstValueFrom(
        apiClient.post<LoginResponse>("/api/app/auth/login", request)
      );

      // Configurar token manager com os dados recebidos
      if (response.accessToken) {
        let finalTokenData: TokenData;

        if (response.activatedBiometrics === true) {
          // Token é para "login rápido", precisa fazer refresh para obter token real
          ApiLogger.info(
            "Biometria ativada detectada no app login, fazendo refresh do token"
          );

          const refreshResponse = await this.refreshTokenWithQuickLogin(
            response.accessToken
          );

          finalTokenData = {
            accessToken: refreshResponse.accessToken,
            refreshToken: refreshResponse.refreshToken,
            tokenType: refreshResponse.tokenType,
            expiresIn: refreshResponse.expiresIn,
            expiresAt: new Date(Date.now() + refreshResponse.expiresIn * 1000)
          };

          ApiLogger.info(
            "Token de autenticação obtido via refresh no app login (biometria ativada)"
          );
        } else {
          // Token pode ser usado diretamente
          ApiLogger.info(
            "Biometria não ativada no app login, usando token diretamente"
          );

          finalTokenData = {
            accessToken: response.accessToken,
            refreshToken: response.refreshToken,
            tokenType: response.tokenType,
            expiresIn: response.expiresIn,
            expiresAt: new Date(Date.now() + response.expiresIn * 1000)
          };
        }

        await tokenManager.setTokenData(finalTokenData);

        // Configurar token no API client também
        apiClient.setAuthToken(finalTokenData);

        // Configurar callbacks do token manager
        tokenManager.setOnTokenExpired(() => {
          ApiLogger.warn("Token expirado, redirecionando para login");
          // Aqui você pode adicionar lógica para redirecionar para login
        });

        ApiLogger.info("Login do app realizado com sucesso", {
          userId: response.user?.id,
          tokenType: finalTokenData.tokenType,
          biometricFlow: response.activatedBiometrics === true
        });
      } else {
        throw new Error("Token de acesso não recebido");
      }

      return response;
    } catch (error) {
      ApiLogger.error("Erro no login do app", error as Error);
      throw error;
    }
  }

  /**
   * Realizar logout
   */
  static async logout(request?: LogoutRequest): Promise<LogoutResponse> {
    try {
      ApiLogger.info("Iniciando processo de logout");

      // Tentar fazer logout no servidor
      let response: LogoutResponse;
      try {
        response = await firstValueFrom(
          apiClient.post<LogoutResponse>("/api/app/auth/logout", request || {})
        );
      } catch (error) {
        // Se falhar no servidor, ainda assim limpar dados locais
        ApiLogger.warn(
          "Erro no logout do servidor, limpando dados locais",
          error
        );
        response = {success: true, message: "Logout local realizado"};
      }

      // Limpar token manager
      await tokenManager.clearTokenData();

      ApiLogger.info("Logout realizado com sucesso");
      return response;
    } catch (error) {
      ApiLogger.error("Erro no logout", error as Error);
      // Mesmo com erro, limpar dados locais
      await tokenManager.clearTokenData();
      throw error;
    }
  }

  /**
   * Renovar token
   */
  static async refreshToken(): Promise<RefreshTokenResponse> {
    try {
      ApiLogger.info("Renovando token de acesso");

      await tokenManager.refreshToken();
      const tokenData = tokenManager.getTokenData();

      if (!tokenData) {
        throw new Error("Token data não disponível após renovação");
      }

      const response: RefreshTokenResponse = {
        tokenType: tokenData.tokenType,
        accessToken: tokenData.accessToken,
        expiresIn: tokenData.expiresIn,
        refreshToken: tokenData.refreshToken
      };

      ApiLogger.info("Token renovado com sucesso");
      return response;
    } catch (error) {
      ApiLogger.error("Erro na renovação do token", error as Error);
      throw error;
    }
  }

  /**
   * Fazer refresh usando token de login rápido (para biometria ativada)
   */
  private static async refreshTokenWithQuickLogin(
    quickLoginToken: string
  ): Promise<RefreshTokenResponse> {
    try {
      ApiLogger.info(
        "Fazendo refresh com token de login rápido (biometria ativada)"
      );

      const response = await fetch(
        `${process.env.EXPO_PUBLIC_API_BASE_URL}/api/auth/refresh`,
        {
          method: "GET",
          headers: {
            Authorization: `Bearer ${quickLoginToken}`,
            Accept: "application/json",
            "Accept-Language": "pt-br"
          }
        }
      );

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          errorData.message || `HTTP ${response.status}: ${response.statusText}`
        );
      }

      const refreshData = await response.json();

      ApiLogger.info("Refresh com token de login rápido realizado com sucesso");
      return refreshData;
    } catch (error) {
      ApiLogger.error(
        "Erro no refresh com token de login rápido",
        error as Error
      );
      throw error;
    }
  }

  /**
   * Validar token atual
   */
  static async validateToken(): Promise<TokenValidationResponse> {
    try {
      const response = await firstValueFrom(
        apiClient.get<TokenValidationResponse>("/auth/validate")
      );

      ApiLogger.info("Token validado", {valid: response.valid});
      return response;
    } catch (error) {
      ApiLogger.error("Erro na validação do token", error as Error);
      throw error;
    }
  }

  /**
   * Obter informações do usuário atual
   */
  static async getCurrentUser(): Promise<UserInfo> {
    try {
      console.log("🔍 [AUTH-SERVICE] getCurrentUser iniciado");

      // Verificar se há token
      const token = await tokenManager.getAccessToken();
      console.log("🔍 [AUTH-SERVICE] Token disponível:", token ? "Sim" : "Não");

      const response = await firstValueFrom(
        apiClient.get<UserInfo>("/api/users/@me")
      );

      console.log("✅ [AUTH-SERVICE] Usuário obtido:", response);
      ApiLogger.info("Informações do usuário obtidas", {userId: response.id});
      return response;
    } catch (error) {
      console.error("❌ [AUTH-SERVICE] Erro ao obter usuário:", error);
      ApiLogger.error("Erro ao obter informações do usuário", error as Error);
      throw error;
    }
  }

  /**
   * Solicitar recuperação de senha
   */
  static async forgotPassword(
    request: ForgotPasswordRequest
  ): Promise<ForgotPasswordResponse> {
    try {
      ApiLogger.info("Solicitando recuperação de senha", {
        email: request.email
      });

      const response = await firstValueFrom(
        apiClient.post<ForgotPasswordResponse>("/app/auth/forgot", request)
      );

      ApiLogger.info("Solicitação de recuperação enviada", {
        type: response.type
      });
      return response;
    } catch (error) {
      ApiLogger.error(
        "Erro na solicitação de recuperação de senha",
        error as Error
      );
      throw error;
    }
  }

  /**
   * Redefinir senha
   */
  static async resetPassword(
    request: ResetPasswordRequest
  ): Promise<ResetPasswordResponse> {
    try {
      ApiLogger.info("Redefinindo senha");

      const response = await firstValueFrom(
        apiClient.post<ResetPasswordResponse>("/auth/reset", request)
      );

      ApiLogger.info("Senha redefinida com sucesso");
      return response;
    } catch (error) {
      ApiLogger.error("Erro na redefinição de senha", error as Error);
      throw error;
    }
  }

  /**
   * Alterar senha
   * NOTA: Este endpoint não existe na API atual.
   * A API só oferece reset de senha via email.
   */
  static async changePassword(
    _request: ChangePasswordRequest
  ): Promise<ChangePasswordResponse> {
    try {
      ApiLogger.info("Alterando senha");

      // O endpoint /auth/change-password não existe na API
      // Vamos lançar um erro informativo
      throw new Error(
        "Endpoint de alteração de senha não está disponível na API. Use o fluxo de reset de senha."
      );
    } catch (error) {
      ApiLogger.error("Erro na alteração de senha", error as Error);
      throw error;
    }
  }

  /**
   * Autenticação biométrica
   */
  static async biometricAuth(
    request: BiometricAuthRequest
  ): Promise<BiometricAuthResponse> {
    try {
      ApiLogger.info("Iniciando autenticação biométrica", {
        userId: request.userId
      });

      const response = await firstValueFrom(
        apiClient.post<BiometricAuthResponse>("/auth/biometric", request)
      );

      // Configurar token manager se autenticação for bem-sucedida
      if (response.success && response.accessToken) {
        const tokenData: TokenData = {
          accessToken: response.accessToken,
          refreshToken: response.refreshToken,
          tokenType: response.tokenType,
          expiresIn: response.expiresIn,
          expiresAt: new Date(Date.now() + response.expiresIn * 1000)
        };

        await tokenManager.setTokenData(tokenData);
      }

      ApiLogger.info("Autenticação biométrica realizada", {
        success: response.success
      });
      return response;
    } catch (error) {
      ApiLogger.error("Erro na autenticação biométrica", error as Error);
      throw error;
    }
  }

  /**
   * Registrar dispositivo
   */
  static async registerDevice(
    request: DeviceRegistrationRequest
  ): Promise<DeviceRegistrationResponse> {
    try {
      ApiLogger.info("Registrando dispositivo", {
        deviceId: request.deviceId,
        platform: request.platform
      });

      const response = await firstValueFrom(
        apiClient.post<DeviceRegistrationResponse>(
          "/auth/register-device",
          request
        )
      );

      ApiLogger.info("Dispositivo registrado com sucesso", {
        deviceId: response.deviceId
      });
      return response;
    } catch (error) {
      ApiLogger.error("Erro no registro do dispositivo", error as Error);
      throw error;
    }
  }

  /**
   * Verificar se há sessão ativa
   */
  static async hasActiveSession(): Promise<boolean> {
    try {
      // Carregar token do storage
      const tokenData = await tokenManager.loadTokenFromStorage();

      if (!tokenData) {
        return false;
      }

      // Verificar se token não está expirado
      if (tokenManager.isTokenExpired()) {
        // Tentar renovar se possível
        if (tokenData.refreshToken) {
          try {
            await tokenManager.refreshToken();
            return true;
          } catch {
            return false;
          }
        }
        return false;
      }

      return true;
    } catch (error) {
      ApiLogger.error("Erro ao verificar sessão ativa", error as Error);
      return false;
    }
  }

  /**
   * Obter status da autenticação
   */
  static getAuthStatus() {
    const tokenStatus = tokenManager.getStatus();

    return {
      isAuthenticated: tokenStatus.hasToken && !tokenStatus.isExpired,
      tokenStatus,
      accessToken: tokenManager.getAccessToken()
    };
  }
}

export default AuthService;
