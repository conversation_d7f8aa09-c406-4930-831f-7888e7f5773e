export interface CreditCardData {
  number: string;
  expiryDate: string;
  cvv: string;
  holderName: string;
  installments: number;
}

export interface InstallmentOption {
  installments: number;
  value: number;
  totalValue: number;
  label: string;
  hasInterest: boolean;
}

export interface CreditCardValidation {
  isValid: boolean;
  errors: string[];
}

/**
 * Masks credit card number with spaces for display
 * @param value - Raw card number input
 * @returns Formatted card number (e.g., "1234 5678 9012 3456")
 */
export const maskCardNumber = (value: string): string => {
  // Remove all non-numeric characters
  const numericValue = value.replace(/\D/g, "");

  // Limit to 16 digits
  const limitedValue = numericValue.slice(0, 16);

  // Add spaces every 4 digits
  return limitedValue.replace(/(\d{4})(?=\d)/g, "$1 ");
};

/**
 * Masks expiry date in MM/YY format
 * @param value - Raw expiry date input
 * @returns Formatted expiry date (e.g., "12/25")
 */
export const maskExpiryDate = (value: string): string => {
  // Remove all non-numeric characters
  const numericValue = value.replace(/\D/g, "");

  // Limit to 4 digits
  const limitedValue = numericValue.slice(0, 4);

  // Add slash after 2 digits
  if (limitedValue.length >= 2) {
    return `${limitedValue.slice(0, 2)}/${limitedValue.slice(2)}`;
  }

  return limitedValue;
};

/**
 * Masks CVV based on card type
 * @param value - Raw CVV input
 * @param cardNumber - Card number to determine CVV length
 * @returns Formatted CVV
 */
export const maskCVV = (value: string, cardNumber: string): string => {
  // Remove all non-numeric characters
  const numericValue = value.replace(/\D/g, "");

  // American Express cards have 4-digit CVV, others have 3
  const maxLength = getCardBrand(cardNumber) === "amex" ? 4 : 3;

  return numericValue.slice(0, maxLength);
};

/**
 * Determines card brand based on card number
 * @param cardNumber - Card number
 * @returns Card brand identifier
 */
export const getCardBrand = (cardNumber: string): string => {
  const numericValue = cardNumber.replace(/\D/g, "");

  if (numericValue.startsWith("4")) {
    return "visa";
  } else if (numericValue.startsWith("5") || numericValue.startsWith("2")) {
    return "mastercard";
  } else if (numericValue.startsWith("34") || numericValue.startsWith("37")) {
    return "amex";
  } else if (numericValue.startsWith("6")) {
    return "discover";
  }

  return "unknown";
};

/**
 * Formats card number for display (shows only last 4 digits)
 * @param cardNumber - Full card number
 * @returns Formatted display string (e.g., "**** **** **** 1234")
 */
export const formatCardNumberForDisplay = (cardNumber: string): string => {
  const numericValue = cardNumber.replace(/\D/g, "");

  if (numericValue.length < 4) {
    return "**** **** **** ****";
  }

  const lastFour = numericValue.slice(-4);
  return `**** **** **** ${lastFour}`;
};

/**
 * Validates credit card data
 * @param cardData - Credit card data to validate
 * @returns Validation result with errors
 */
export const validateCreditCard = (
  cardData: CreditCardData
): CreditCardValidation => {
  const errors: string[] = [];

  // Validate card number
  const numericNumber = cardData.number.replace(/\D/g, "");
  if (
    !numericNumber ||
    numericNumber.length < 13 ||
    numericNumber.length > 19
  ) {
    errors.push("Número do cartão deve ter entre 13 e 19 dígitos");
  } else if (!isValidCardNumber(numericNumber)) {
    errors.push("Número do cartão inválido");
  }

  // Validate expiry date
  if (!cardData.expiryDate || !isValidExpiryDate(cardData.expiryDate)) {
    errors.push("Data de validade inválida");
  }

  // Validate CVV
  const expectedCvvLength = getCardBrand(cardData.number) === "amex" ? 4 : 3;
  if (!cardData.cvv || cardData.cvv.length !== expectedCvvLength) {
    errors.push(`CVV deve ter ${expectedCvvLength} dígitos`);
  }

  // Validate holder name - require at least two words
  const holderWords = (cardData.holderName || "")
    .trim()
    .split(/\s+/)
    .filter(Boolean);
  if (holderWords.length < 2) {
    errors.push("Informe nome e sobrenome (pelo menos 2 nomes)");
  }

  // Validate installments
  if (
    !cardData.installments ||
    cardData.installments < 1 ||
    cardData.installments > 12
  ) {
    errors.push("Número de parcelas deve ser entre 1 e 12");
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

/**
 * Validates card number using Luhn algorithm
 * @param cardNumber - Numeric card number
 * @returns True if valid
 */
const isValidCardNumber = (cardNumber: string): boolean => {
  let sum = 0;
  let isEven = false;

  // Process digits from right to left
  for (let i = cardNumber.length - 1; i >= 0; i--) {
    let digit = parseInt(cardNumber[i]);

    if (isEven) {
      digit *= 2;
      if (digit > 9) {
        digit -= 9;
      }
    }

    sum += digit;
    isEven = !isEven;
  }

  return sum % 10 === 0;
};

/**
 * Validates expiry date
 * @param expiryDate - Expiry date in MM/YY format
 * @returns True if valid and not expired
 */
const isValidExpiryDate = (expiryDate: string): boolean => {
  const regex = /^(\d{2})\/(\d{2})$/;
  const match = regex.exec(expiryDate);
  if (!match) return false;

  const month = parseInt(match[1]);
  const year = parseInt(match[2]) + 2000; // Convert YY to YYYY

  if (month < 1 || month > 12) return false;

  const now = new Date();
  const currentYear = now.getFullYear();
  const currentMonth = now.getMonth() + 1;

  if (year < currentYear || (year === currentYear && month < currentMonth)) {
    return false;
  }

  return true;
};

export interface InstallmentConfig {
  maxInstallmentsWithoutFee: number;
  maxInstallmentsWithFee: number;
  feePercentage: number;
}

/**
 * Generates installment options for a given amount
 * @param amount - Total amount
 * @param config - Installment configuration (optional, uses defaults if not provided)
 * @returns Array of installment options
 */
export const generateInstallmentOptions = (
  amount: number,
  config?: InstallmentConfig
): InstallmentOption[] => {
  console.log("🔢 [CREDIT-CARD-UTILS] Gerando opções de parcelamento:", {
    amount,
    amountType: typeof amount,
    isValidAmount: !isNaN(amount) && amount > 0,
    config
  });

  // Validate amount
  if (!amount || isNaN(amount) || amount <= 0) {
    console.warn(
      "🔢 [CREDIT-CARD-UTILS] Valor inválido para gerar parcelas:",
      amount
    );
    return [];
  }

  const options: InstallmentOption[] = [];

  // Use provided config or defaults
  const installmentConfig = config || {
    maxInstallmentsWithoutFee: 6,
    maxInstallmentsWithFee: 12,
    feePercentage: 2.99
  };

  const maxInstallments = installmentConfig.maxInstallmentsWithFee;
  const maxWithoutFee = installmentConfig.maxInstallmentsWithoutFee;
  const feePercentage = installmentConfig.feePercentage / 100; // Convert percentage to decimal

  console.log("🔢 [CREDIT-CARD-UTILS] Configuração de parcelamento:", {
    maxInstallments,
    maxWithoutFee,
    feePercentage
  });

  for (let i = 1; i <= maxInstallments; i++) {
    const hasInterest = i > maxWithoutFee;
    const interestRate = hasInterest ? feePercentage : 0;

    let installmentValue: number;
    let totalValue: number;

    if (hasInterest) {
      // Calculate with compound interest
      const factor = Math.pow(1 + interestRate, i);
      installmentValue = (amount * interestRate * factor) / (factor - 1);
      totalValue = installmentValue * i;
    } else {
      installmentValue = amount / i;
      totalValue = amount;
    }

    // Validate calculated values
    if (
      !installmentValue ||
      isNaN(installmentValue) ||
      !totalValue ||
      isNaN(totalValue)
    ) {
      console.warn("🔢 [CREDIT-CARD-UTILS] Valores calculados inválidos:", {
        installment: i,
        installmentValue,
        totalValue,
        amount,
        hasInterest,
        interestRate
      });
      continue; // Skip this installment option
    }

    console.log("🔢 [CREDIT-CARD-UTILS] Calculando parcela:", {
      installment: i,
      installmentValue,
      totalValue,
      hasInterest
    });

    let label: string;
    if (i === 1) {
      label = `À vista - R$ ${installmentValue.toFixed(2).replace(".", ",")}`;
    } else if (hasInterest) {
      label = `${i}x de R$ ${installmentValue
        .toFixed(2)
        .replace(".", ",")} com juros`;
    } else {
      label = `${i}x de R$ ${installmentValue
        .toFixed(2)
        .replace(".", ",")} sem juros`;
    }

    options.push({
      installments: i,
      value: installmentValue,
      totalValue,
      label,
      hasInterest
    });
  }

  console.log("🔢 [CREDIT-CARD-UTILS] Opções geradas:", options);

  return options;
};

/**
 * Legacy function for backward compatibility
 * @deprecated Use generateInstallmentOptions with InstallmentConfig instead
 */
export const generateInstallmentOptionsLegacy = (
  amount: number,
  maxInstallments: number = 12
): InstallmentOption[] => {
  return generateInstallmentOptions(amount, {
    maxInstallmentsWithoutFee: 1,
    maxInstallmentsWithFee: maxInstallments,
    feePercentage: 2.99
  });
};
