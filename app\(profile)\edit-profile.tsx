import React, {useState, useCallback, useEffect} from "react";
import {
  Text,
  View,
  ScrollView,
  TouchableOpacity,
  Image,
  ColorValue,
  ActivityIndicator
} from "react-native";
import * as ImagePicker from "expo-image-picker";
import * as ImageManipulator from "expo-image-manipulator";
import Screen<PERSON><PERSON>Header from "@/components/screen-with-header";
import {useTranslation} from "react-i18next";
import {useRouter} from "expo-router";
import InputField from "@/components/input-field";
import FullSizeButton from "@/components/full-size-button";
import InvisibleFullSizeButton from "@/components/invisible-full-size-button";
import UserIcon from "@/components/icons/user-icon";
import PhoneIcon from "@/components/icons/phone-icon";
import CalendarIcon from "@/components/icons/calendar-icon";
import LicenceThirdPartyIcon from "@/components/icons/licence-third-party-icon";
import PasswordIcon from "@/components/icons/password-icon";
import UserTabs from "@/components/user/user-tabs";
import {useCurrentUser, useUpdateProfile} from "@/hooks/api/use-users";
import stylesConstants from "@/styles/styles-constants";
import {useBottomModal} from "@/contexts/bottom-modal-context";
import BottomModal from "@/components/bottom-modal";
import styles from "@/styles/profile/edit-profile.style";
import EmailIcon from "@/components/icons/email-icon";
import {formatCpf} from "@/utils/cpf";
import {
  showSuccessToast,
  showErrorToast,
  showWarningToast
} from "@/utils/error-handler";

interface EditableProfile {
  name: string;
  email: string;
  cpf: string;
  phone: string;
  birthDate: string;
}

interface Tab {
  title: string;
  id: number;
}

enum ProfileTabsEnum {
  PersonalData = 1,
  ChangePassword = 2
}

/**
 * Converte uma imagem para formato WebP e retorna em base64
 * @param imageUri URI da imagem selecionada
 * @returns Promise<string> Imagem processada em base64 (WebP)
 */
const convertImageToWebP = async (imageUri: string): Promise<string> => {
  try {
    console.log("🖼️ [WEBP-CONVERTER] Iniciando conversão da imagem:", imageUri);

    // Redimensionar e converter para WebP usando apenas expo-image-manipulator
    const manipulatedImage = await ImageManipulator.manipulateAsync(
      imageUri,
      [
        {resize: {width: 400}} // Redimensionar para largura máxima de 400px para avatar
      ],
      {
        compress: 0.8, // 80% de qualidade
        format: ImageManipulator.SaveFormat.WEBP, // Formato WebP nativo
        base64: true // Obter resultado em base64
      }
    );

    console.log("🖼️ [WEBP-CONVERTER] Conversão WebP concluída:", {
      originalUri: imageUri,
      newWidth: manipulatedImage.width,
      newHeight: manipulatedImage.height,
      hasBase64: !!manipulatedImage.base64,
      format: "WebP (expo-image-manipulator)"
    });

    if (!manipulatedImage.base64) {
      throw new Error("Falha ao obter base64 da imagem WebP");
    }

    console.log("🖼️ [WEBP-CONVERTER] Base64 WebP pronto para envio:", {
      base64Length: manipulatedImage.base64.length,
      compressionApplied: "80% quality WebP + redimensionamento para 400px",
      readyForAPI: true
    });

    return manipulatedImage.base64;
  } catch (error) {
    console.error("❌ [WEBP-CONVERTER] Erro na conversão da imagem:", error);
    throw new Error("Falha ao converter imagem para WebP");
  }
};

interface PhotoSelectionModalProps {
  onSelectFromGallery: () => void;
  onTakePhoto: () => void;
  onCancel: () => void;
}

const PhotoSelectionModal: React.FC<PhotoSelectionModalProps> = ({
  onSelectFromGallery,
  onTakePhoto,
  onCancel
}) => {
  const {t} = useTranslation();

  return (
    <BottomModal.Container>
      <BottomModal.ButtonsContainer>
        <FullSizeButton
          text={t("profile.selectFromGallery", "Selecionar da galeria")}
          onPress={onSelectFromGallery}
        />
        <InvisibleFullSizeButton
          text={t("profile.takePhoto", "Tirar uma foto")}
          onPress={onTakePhoto}
        />
        <InvisibleFullSizeButton
          text={t("profile.cancel", "Cancelar")}
          onPress={onCancel}
        />
      </BottomModal.ButtonsContainer>
    </BottomModal.Container>
  );
};

const EditProfile: React.FC = () => {
  const {t} = useTranslation();
  const router = useRouter();
  const {openModal, closeModal} = useBottomModal();
  const [activeTab, setActiveTab] = useState(ProfileTabsEnum.PersonalData);

  // Hooks para dados do usuário
  const {data: user, isLoading: isLoadingUser} = useCurrentUser();
  const updateProfileMutation = useUpdateProfile();

  // Estado local do formulário
  const [profile, setProfile] = useState<EditableProfile>({
    name: "",
    email: "",
    cpf: "",
    phone: "",
    birthDate: ""
  });

  const [avatar, setAvatar] = useState<string>("");
  const [avatarBase64, setAvatarBase64] = useState<string>("");
  const [currentPassword, setCurrentPassword] = useState("");
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");

  // Carregar dados do usuário quando disponível
  useEffect(() => {
    if (user) {
      setProfile({
        name: user.name || "",
        email: user.email || "",
        cpf: formatCpf(user.document || ""),
        phone: user.phone || "",
        birthDate: user.birthDate || ""
      });
      setAvatar(user.avatar || "");
    }
  }, [user]);

  const tabs: Tab[] = [
    {
      title: t("profile.personalData", "Dados pessoais"),
      id: ProfileTabsEnum.PersonalData
    },
    {
      title: t("profile.changePassword", "Alterar senha"),
      id: ProfileTabsEnum.ChangePassword
    }
  ];

  const handleInputChange =
    (field: keyof EditableProfile) => (value: string) => {
      setProfile((prev) => ({
        ...prev,
        [field]: value
      }));
    };

  const handleTabChange = useCallback((id: number) => {
    setActiveTab(id);
  }, []);

  const handleCpfChange = useCallback((value: string) => {
    setProfile((prev) => ({
      ...prev,
      cpf: formatCpf(value)
    }));
  }, []);

  const handleSaveProfile = async () => {
    // Validate required fields
    if (!profile.name.trim()) {
      console.log("Nome é obrigatório");
      return;
    }

    if (!profile.email.trim()) {
      console.log("Email é obrigatório");
      return;
    }

    try {
      // Usar mutation para atualizar perfil
      const updateData: any = {
        name: profile.name,
        email: profile.email
      };

      // Só incluir phone se não estiver vazio
      if (profile.phone && profile.phone.trim() !== "") {
        updateData.phone = profile.phone;
      }

      // Só incluir avatar se não estiver vazio (priorizar base64 se disponível)
      if (avatarBase64 && avatarBase64.trim() !== "") {
        updateData.avatar = avatarBase64;
        console.log("🖼️ [PROFILE-UPDATE] Enviando avatar base64 WebP:", {
          base64Length: avatarBase64.length,
          format: "WebP base64"
        });
      } else if (avatar && avatar.trim() !== "") {
        updateData.avatar = avatar;
        console.log("🖼️ [PROFILE-UPDATE] Enviando avatar existente:", avatar);
      }

      await updateProfileMutation.mutateAsync(updateData);

      console.log("Perfil atualizado com sucesso");

      // Mostrar feedback de sucesso para o usuário
      showSuccessToast(
        t("profile.success", "Sucesso"),
        t("profile.updateSuccess", "Perfil atualizado com sucesso!")
      );
    } catch (error: any) {
      console.error("Erro ao atualizar perfil:", error);

      // Mostrar erro específico para o usuário
      const errorMessage =
        error?.message ||
        t("profile.updateError", "Erro ao atualizar perfil. Tente novamente.");

      showErrorToast(error, errorMessage);
    }
  };

  const handleChangePassword = async () => {
    // Mostrar alerta informando que a funcionalidade não está disponível
    showWarningToast(
      t("profile.info", "Informação"),
      t(
        "profile.changePasswordNotAvailable",
        "A alteração de senha não está disponível no momento. Para redefinir sua senha, use a opção 'Esqueci minha senha' na tela de login."
      )
    );
  };

  const processSelectedImage = useCallback(
    async (imageUri: string) => {
      console.log(
        "🔄 [IMAGE-PROCESSING] Processando imagem selecionada:",
        imageUri
      );

      try {
        // Converter imagem para WebP
        const webpBase64 = await convertImageToWebP(imageUri);

        // Atualizar estado
        setAvatar(imageUri); // URI local para preview
        setAvatarBase64(webpBase64); // Base64 WebP para envio à API

        console.log("✅ [IMAGE-PROCESSING] Imagem processada com sucesso");
        showSuccessToast(
          t("profile.success", "Sucesso"),
          t("profile.imageProcessedSuccess", "Imagem selecionada com sucesso!")
        );
      } catch (error) {
        console.error("❌ [IMAGE-PROCESSING] Erro ao processar imagem:", error);
        showErrorToast(
          error,
          t(
            "profile.imageProcessingError",
            "Falha ao processar a imagem. Verifique se o arquivo é uma imagem válida."
          )
        );
      }
    },
    [t]
  );

  const handleImageUpload = useCallback(
    async (source: "camera" | "gallery") => {
      console.log(`📸 [IMAGE-UPLOAD] Iniciando seleção de imagem: ${source}`);

      try {
        let result;

        if (source === "camera") {
          // Solicitar permissão da câmera
          const cameraPermission =
            await ImagePicker.requestCameraPermissionsAsync();
          if (cameraPermission.status !== "granted") {
            showWarningToast(
              t("profile.permissionRequired", "Permissão necessária"),
              t(
                "profile.cameraPermissionMessage",
                "Precisamos de permissão para usar a câmera."
              )
            );
            return;
          }

          result = await ImagePicker.launchCameraAsync({
            mediaTypes: ["images"],
            allowsEditing: true,
            aspect: [1, 1], // Proporção 1:1 para avatar
            quality: 0.8
          });
        } else {
          // Solicitar permissão da galeria
          const {status} =
            await ImagePicker.requestMediaLibraryPermissionsAsync();
          if (status !== "granted") {
            showWarningToast(
              t("profile.permissionRequired", "Permissão necessária"),
              t(
                "profile.galleryPermissionMessage",
                "Precisamos de permissão para acessar suas fotos."
              )
            );
            return;
          }

          result = await ImagePicker.launchImageLibraryAsync({
            mediaTypes: ["images"],
            allowsEditing: true,
            aspect: [1, 1], // Proporção 1:1 para avatar
            quality: 0.8
          });
        }

        if (!result.canceled && result.assets[0]) {
          await processSelectedImage(result.assets[0].uri);
        }
      } catch (error) {
        console.error(`❌ [IMAGE-UPLOAD] Erro ao abrir ${source}:`, error);
        showErrorToast(
          error,
          t(
            "profile.imageUploadError",
            `Falha ao abrir ${
              source === "camera" ? "a câmera" : "a galeria"
            }. Tente novamente.`
          )
        );
      }
    },
    [t, processSelectedImage]
  );

  const handleChangePhoto = () => {
    openModal({
      title: "",
      children: (
        <PhotoSelectionModal
          onSelectFromGallery={() => {
            closeModal();
            handleImageUpload("gallery");
          }}
          onTakePhoto={() => {
            closeModal();
            handleImageUpload("camera");
          }}
          onCancel={closeModal}
        />
      )
    });
  };

  const userIcon = useCallback(
    (errorColor?: ColorValue) => <UserIcon replaceColor={errorColor} />,
    []
  );

  const emailIcon = useCallback(() => <EmailIcon />, []);

  const phoneIcon = useCallback(
    (errorColor?: ColorValue) => <PhoneIcon replaceColor={errorColor} />,
    []
  );

  const calendarIcon = useCallback(
    (errorColor?: ColorValue) => <CalendarIcon replaceColor={errorColor} />,
    []
  );

  const documentIcon = useCallback(
    (errorColor?: ColorValue) => (
      <LicenceThirdPartyIcon replaceColor={errorColor} />
    ),
    []
  );
  const passwordIcon = useCallback(
    (errorColor?: ColorValue) => <PasswordIcon replaceColor={errorColor} />,
    []
  );

  return (
    <ScreenWithHeader
      screenTitle={t("profile.profileInfo", "Informações de perfil")}
      backButton
    >
      <View style={styles.container}>
        {/* Tabs */}
        <UserTabs
          tabs={tabs}
          currentTab={activeTab}
          onTabChange={handleTabChange}
          style={styles.tabsContainer}
        />

        <ScrollView style={styles.contentContainer}>
          {isLoadingUser ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator
                size="large"
                color={stylesConstants.colors.primary}
              />
              <Text style={styles.loadingText}>
                {t("common.loading", "Carregando...")}
              </Text>
            </View>
          ) : activeTab === ProfileTabsEnum.PersonalData ? (
            <>
              {/* Profile Photo */}
              <View style={styles.headerContainer}>
                <TouchableOpacity
                  style={styles.avatarContainer}
                  onPress={handleChangePhoto}
                >
                  {avatar ? (
                    <Image source={{uri: avatar}} style={styles.avatar} />
                  ) : (
                    <View style={[styles.avatar, styles.avatarPlaceholder]}>
                      <UserIcon />
                    </View>
                  )}
                </TouchableOpacity>
                <TouchableOpacity onPress={handleChangePhoto}>
                  <Text style={styles.changePhotoText}>
                    {avatar
                      ? t("profile.changePhoto", "Alterar foto")
                      : t("profile.addPhoto", "Adicionar foto")}
                  </Text>
                </TouchableOpacity>
              </View>

              {/* Form Fields */}
              <View style={styles.formContainer}>
                <View style={styles.inputGroup}>
                  <Text style={styles.label}>
                    {t("profile.fullName", "Nome completo")}
                  </Text>
                  <InputField
                    value={profile.name}
                    onChangeText={handleInputChange("name")}
                    placeholder={t("profile.enterName", "Maria Aparecida")}
                    icon={userIcon}
                  />
                </View>

                <View style={styles.inputGroup}>
                  <Text style={styles.label}>
                    {t("profile.email", "E-mail")}
                  </Text>
                  <InputField
                    value={profile.email}
                    onChangeText={handleInputChange("email")}
                    placeholder={t(
                      "profile.enterEmail",
                      "<EMAIL>"
                    )}
                    inputMode="email"
                    icon={emailIcon}
                  />
                </View>

                <View style={styles.inputGroup}>
                  <Text style={styles.label}>{t("profile.cpf", "CPF")}</Text>
                  <InputField
                    value={profile.cpf}
                    onChangeText={handleCpfChange}
                    placeholder={t("profile.enterCpf", "000.000.000-00")}
                    icon={documentIcon}
                    inputMode="numeric"
                    maxLength={14}
                  />
                </View>

                <View style={styles.inputGroup}>
                  <Text style={styles.label}>
                    {t("profile.phone", "Telefone")}
                  </Text>
                  <InputField
                    value={profile.phone}
                    onChangeText={handleInputChange("phone")}
                    placeholder={t("profile.enterPhone", "+55 47 0000-0000")}
                    icon={phoneIcon}
                    inputMode="tel"
                  />
                </View>

                <View style={styles.inputGroup}>
                  <Text style={styles.label}>
                    {t("profile.birthDate", "Data de nascimento")}
                  </Text>
                  <InputField
                    value={profile.birthDate}
                    onChangeText={handleInputChange("birthDate")}
                    placeholder={t("profile.enterBirthDate", "07/04/1976")}
                    icon={calendarIcon}
                  />
                </View>
              </View>

              {/* Buttons */}
              <View style={styles.buttonContainer}>
                <FullSizeButton
                  text={
                    updateProfileMutation.isPending
                      ? t("common.saving", "Salvando...")
                      : t("profile.saveChanges", "Salvar alterações")
                  }
                  onPress={
                    updateProfileMutation.isPending
                      ? undefined
                      : handleSaveProfile
                  }
                />
                <InvisibleFullSizeButton
                  text={t("profile.backToProfile", "Voltar para perfil")}
                  onPress={() => router.back()}
                />
              </View>
            </>
          ) : (
            <>
              {/* Change Password Form */}
              <View style={styles.formContainer}>
                <View style={styles.inputGroup}>
                  <Text style={styles.label}>
                    {t("profile.currentPassword", "Senha atual")}
                  </Text>
                  <InputField
                    value={currentPassword}
                    onChangeText={setCurrentPassword}
                    placeholder={t(
                      "profile.enterCurrentPassword",
                      "Digite sua senha atual"
                    )}
                    isPassword
                    icon={passwordIcon}
                  />
                </View>

                <View style={styles.inputGroup}>
                  <Text style={styles.label}>
                    {t("profile.newPassword", "Nova senha")}
                  </Text>
                  <InputField
                    value={newPassword}
                    onChangeText={setNewPassword}
                    placeholder={t(
                      "profile.enterNewPassword",
                      "Digite sua nova senha"
                    )}
                    isPassword
                    icon={passwordIcon}
                  />
                </View>

                <View style={styles.inputGroup}>
                  <Text style={styles.label}>
                    {t("profile.confirmPassword", "Nova senha")}
                  </Text>
                  <InputField
                    value={confirmPassword}
                    onChangeText={setConfirmPassword}
                    placeholder={t(
                      "profile.enterConfirmPassword",
                      "Confirme sua nova senha"
                    )}
                    isPassword
                    icon={passwordIcon}
                  />
                </View>

                <Text style={styles.passwordHint}>
                  {t(
                    "profile.passwordHint",
                    "Sua senha deve conter ao menos 8 caracteres."
                  )}
                </Text>
              </View>

              {/* Password Change Buttons */}
              <View style={styles.buttonContainer}>
                <FullSizeButton
                  text={t("profile.changePassword", "Alterar senha")}
                  onPress={handleChangePassword}
                />
                <InvisibleFullSizeButton
                  text={t("profile.back", "Voltar")}
                  onPress={() => router.back()}
                />
              </View>
            </>
          )}
        </ScrollView>
      </View>
    </ScreenWithHeader>
  );
};

export default EditProfile;
