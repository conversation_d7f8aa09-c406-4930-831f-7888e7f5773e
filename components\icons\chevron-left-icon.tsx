import React, {useMemo} from "react";
import Svg, {Path, SvgProps} from "react-native-svg";
import {ColorValue} from "react-native";

export interface ChevronLeftIconProps extends SvgProps {
  stroke?: ColorValue;
}

const ChevronLeftIcon: React.FC<ChevronLeftIconProps> = (props) => {
  const strokeColor = useMemo(() => props.stroke ?? "#FCFCFD", [props.stroke]);

  return (
    <Svg width={20} height={20} viewBox="0 0 20 20" fill="none" {...props}>
      <Path
        d="M12.5 15l-5-5 5-5"
        stroke={strokeColor}
        strokeWidth={2}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </Svg>
  );
};

export default ChevronLeftIcon;
