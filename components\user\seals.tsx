import React, {useMemo} from "react";
import {useTranslation} from "react-i18next";
import {Text, View, ActivityIndicator, StyleSheet} from "react-native";
import styles from "../../styles/components/user/seals.style";
import BigSeal from "../seals/big-seal";
import {BadgeType} from "@/models/api/user-badges.models";
import stylesConstants from "@/styles/styles-constants";

export interface Seal {
  id: number;
  title: string;
  description?: string;
  imageUrl?: string;
  earnedAt: string;
  category?: string;
}

export interface SealsProps {
  badges?: Array<{
    id: string | number;
    title?: string; // Dados transformados pelo serviço
    name?: string; // Dados diretos da API
    type?: number;
    level?: number;
    count?: number;
    nextLevelCount?: number;
  }>;
  loading?: boolean;
  error?: string;
}

const Seals: React.FC<SealsProps> = (props) => {
  const {badges, loading, error} = props;
  const {t} = useTranslation();

  const gridStyles = StyleSheet.create({
    container: {
      backgroundColor: "#111828",
      borderRadius: 8,
      borderWidth: 1,
      borderColor: "#101828",
      paddingHorizontal: 6, // Arredondado de 5.75 para 6
      paddingBottom: 1,
      gap: 17,
      paddingTop: 17 // Adicionar padding top para corresponder ao design
    },
    row: {
      flexDirection: "row",
      alignItems: "stretch",
      gap: 21
    },
    sealItem: {
      alignItems: "stretch",
      gap: 4,
      width: 64 // Fixed width instead of flex: 1
    },
    sealIcon: {
      width: 64,
      height: 64,
      justifyContent: "center",
      alignItems: "center"
    },
    sealText: {
      color: "#DFE9F0",
      fontSize: 12,
      lineHeight: 18,
      textAlign: "center",
      fontFamily: stylesConstants.fonts.openSans,
      minHeight: 36,
      width: 64
    }
  });

  const renderSealItem = (seal: any, index: number, uniqueKey: string) => {
    // Mapear tipos de badges da API para os tipos do enum (indexação baseada em zero)
    const mapApiBadgeTypeToEnum = (apiType: number): BadgeType => {
      switch (apiType) {
        case 0:
          return BadgeType.BRONZE;
        case 1:
          return BadgeType.SILVER;
        case 2:
          return BadgeType.GOLD;
        case 3:
          return BadgeType.PLATINUM;
        case 4:
          return BadgeType.DIAMOND;
        default: {
          // Criar variação baseada no índice e ID do selo
          const sealId = seal.id || 0;
          const combinedIndex = (index + sealId) % 5;
          const badgeTypes = [
            BadgeType.BRONZE,
            BadgeType.SILVER,
            BadgeType.GOLD,
            BadgeType.PLATINUM,
            BadgeType.DIAMOND
          ];
          return badgeTypes[combinedIndex];
        }
      }
    };

    const badgeType = seal.type
      ? mapApiBadgeTypeToEnum(seal.type)
      : mapApiBadgeTypeToEnum(index);

    // Lógica mais inteligente para variantes:
    // - Nível 0: sempre circular (Bronze - iniciantes)
    // - Nível 1+: quadrado (Prata, Ouro, Platina - avançados)
    // - Se não há nível definido, alternar baseado no índice
    let variant: "square" | "circle";
    if (seal.level !== undefined) {
      variant = seal.level === 0 ? "circle" : "square";
    } else {
      // Alternar entre circular e quadrado para criar mais variedade
      variant = index % 3 === 0 ? "circle" : "square";
    }

    return (
      <View key={uniqueKey} style={gridStyles.sealItem}>
        <View style={gridStyles.sealIcon}>
          <BigSeal
            badgeType={badgeType}
            variant={variant}
            level={seal.level ?? 0} // Use 0 como padrão (Bronze)
          />
        </View>
        <View style={{justifyContent: "center", minHeight: 36}}>
          <Text style={gridStyles.sealText}>{seal.title}</Text>
        </View>
      </View>
    );
  };

  const displayItems = useMemo(() => {
    // Use only real API badges data
    const items = badges || [];

    // Group items into rows of 4
    const rows = [];
    for (let i = 0; i < items.length; i += 4) {
      rows.push(items.slice(i, i + 4));
    }

    return rows.map((row, rowIndex) => {
      // Criar uma key única para a linha baseada nos IDs dos itens
      const itemIds = row.map((item) =>
        "id" in item ? item.id : `mock-${rowIndex}`
      );
      const rowKey = `row-${rowIndex}-${itemIds.join("-")}`;

      return (
        <View key={rowKey} style={gridStyles.row}>
          {row.map((item, itemIndex) => {
            const sealData = {
              id: item.id,
              title: item.title || item.name || `Badge ${item.id}`, // Usar title primeiro, depois name como fallback
              type: item.type,
              level: item.level
            };
            const globalIndex = rowIndex * 4 + itemIndex; // Calcular índice global para variação
            const uniqueKey = `seal-${item.id}-${rowIndex}-${itemIndex}`;
            return renderSealItem(sealData, globalIndex, uniqueKey);
          })}
          {/* Fill empty spaces in the last row to maintain grid structure */}
          {row.length < 4 &&
            Array.from({length: 4 - row.length}).map((_, index) => (
              <View
                key={`empty-${rowKey}-${index}`}
                style={[gridStyles.sealItem, {opacity: 0}]}
              />
            ))}
        </View>
      );
    });
  }, [badges]);

  // Show loading state
  if (loading) {
    return (
      <View style={gridStyles.container}>
        <ActivityIndicator size="small" color="#fff" />
      </View>
    );
  }

  // Show error state
  if (error) {
    return (
      <View style={gridStyles.container}>
        <Text style={styles.errorText}>{t("user.sealsError")}</Text>
      </View>
    );
  }

  return (
    <View style={gridStyles.container}>
      {displayItems.length > 0 ? (
        displayItems
      ) : (
        <Text style={styles.emptyText}>{t("user.noSeals")}</Text>
      )}
    </View>
  );
};

export default Seals;
