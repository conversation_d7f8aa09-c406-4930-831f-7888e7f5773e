import React, {useState, useCallback, useEffect, useMemo} from "react";
import {
  Text,
  View,
  ColorValue,
  ImageBackground,
  <PERSON>ert,
  ScrollView
} from "react-native";
import Screen from "../../components/screen";
import BackButton from "../../components/back-button";
import {useTranslation} from "react-i18next";
import FullSizeButton from "../../components/full-size-button";
import InputField from "../../components/input-field";
import SelectField from "../../components/select-field";
import MarkerPinIcon from "../../components/icons/marker-pin-icon";
import HomeIcon from "../../components/icons/home-icon";
import HashIcon from "../../components/icons/hash-icon";
import styles from "../../styles/registration/address-registration.style";
import Svg, {Path} from "react-native-svg";
import {useRouter, useLocalSearchParams} from "expo-router";
import {useAddressValidation} from "@/hooks/use-registration-validation";
import {
  useCepLookup,
  useStates,
  useCitiesByState
} from "@/hooks/api/use-addresses";
import {validateCEP} from "@/utils/registration-validation";
import {useRegistrationContext} from "@/contexts/RegistrationContext";

const AddressRegistration: React.FC = () => {
  const {t} = useTranslation();
  const router = useRouter();
  const params = useLocalSearchParams();
  const {updateAddressData} = useRegistrationContext();

  const validation = useAddressValidation(
    {
      zipCode: "",
      address: "",
      number: "",
      complement: "",
      state: "",
      neighborhood: "",
      city: ""
    },
    (validData) => {
      console.log(
        "Navegando para contacts-social-registration com dados:",
        validData
      );

      const addressData = {
        addressStreet: validData.address,
        addressNumber: validData.number,
        addressComplement: validData.complement,
        addressNeighborhood: validData.neighborhood,
        addressCity: validData.city,
        addressState: validData.state,
        addressZip: validData.zipCode
      };

      updateAddressData(addressData);

      router.push("/(registration)/contacts-social-registration");
    }
  );

  const cepLookup = useCepLookup();

  const {data: states, isLoading: isLoadingStates} = useStates();
  const {data: cities, isLoading: isLoadingCities} = useCitiesByState(
    validation.data.state,
    {
      enabled: !!validation.data.state
    }
  );
  const stateOptions = useMemo(() => {
    if (!states) return [];
    return states.map((state) => ({
      value: state.uf,
      label: `${state.name} (${state.uf})`
    }));
  }, [states]);

  const cityOptions = useMemo(() => {
    if (!cities) return [];
    return cities.map((city) => ({
      value: city.name,
      label: city.name
    }));
  }, [cities]);

  const handleCepLookup = useCallback(
    async (cep: string) => {
      try {
        const addressData = await cepLookup.mutateAsync(cep);

        validation.updateData({
          address: addressData.street,
          neighborhood: addressData.neighborhood,
          city: addressData.city,
          state: addressData.state
        });

        console.log("Endereço preenchido automaticamente:", addressData);
      } catch (error) {
        console.warn("Erro ao buscar CEP:", error);
      }
    },
    [cepLookup, validation]
  );

  const handleInputChange = useCallback(
    (field: keyof typeof validation.data) => (value: string) => {
      let processedValue = value;

      if (field === "zipCode") {
        const digits = value.replace(/\D/g, "").slice(0, 8);
        processedValue =
          digits.length <= 5
            ? digits
            : `${digits.slice(0, 5)}-${digits.slice(5)}`;
      }

      validation.updateField(field, processedValue);

      if (field === "zipCode") {
        const cleanCep = processedValue.replace(/\D/g, "");
        if (validateCEP(processedValue) && cleanCep.length === 8) {
          handleCepLookup(cleanCep);
        }
      }
    },
    [validation, handleCepLookup]
  );

  const handleStateSelect = useCallback(
    (stateUf: string) => {
      validation.updateField("state", stateUf);
      if (validation.data.city) {
        validation.updateField("city", "");
      }
    },
    [validation]
  );

  // Garantir que a cidade fique selecionada após buscar pelo CEP
  useEffect(() => {
    if (!cities || !cities.length || !validation.data.city) return;

    const current = validation.data.city;
    const hasExact = cities.some((c) => c.name === current);
    if (hasExact) return;

    const normalize = (s: string) =>
      s
        .toLowerCase()
        .normalize("NFD")
        .replace(/[\u0300-\u036f]/g, "");

    const match = cities.find((c) => normalize(c.name) === normalize(current));
    if (match) {
      validation.updateField("city", match.name);
    }
  }, [cities, validation.data.city, validation]);

  const handleCitySelect = useCallback(
    (cityName: string) => {
      validation.updateField("city", cityName);
    },
    [validation]
  );

  const markerIcon = useCallback(
    (errorColor?: ColorValue) => <MarkerPinIcon replaceColor={errorColor} />,
    []
  );

  const homeIcon = useCallback(
    (errorColor?: ColorValue) => <HomeIcon replaceColor={errorColor} />,
    []
  );

  const hashIcon = useCallback(
    (errorColor?: ColorValue) => <HashIcon replaceColor={errorColor} />,
    []
  );

  const handleNext = useCallback(() => {
    const isValid = validation.handleSubmit();
    if (!isValid) {
      const errorMessages = Object.values(validation.errors).join("\n");
      Alert.alert(
        t("addressRegistration.error.title", "Erro de validação"),
        errorMessages ||
          t(
            "addressRegistration.error.fillRequired",
            "Preencha todos os campos obrigatórios"
          )
      );
    }
  }, [validation, t]);

  const handleBack = useCallback(() => {
    console.log("Voltando da tela de address-registration");
    router.back();
  }, [router]);

  return (
    <ImageBackground
      source={require("../../assets/images/backgroundregistration.png")}
      style={styles.backgroundImage}
      imageStyle={styles.backgroundImageStyle}
    >
      <View style={styles.contentContainer}>
        <View style={styles.progressContainer}>
          <View style={styles.backButtonTop}>
            <Text>
              <Svg width="8" height="14" viewBox="0 0 8 14" fill="none">
                <Path
                  d="M7 13L1 7L7 1"
                  stroke="#FCFCFD"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </Svg>
            </Text>
          </View>
          <Text style={styles.progressTitle}>
            {t("addressRegistration.createAccount", "Criar conta")}
          </Text>
          <Text style={styles.progressStep}>
            {t("addressRegistration.stepProgress", "4 / 7 Endereço")}
          </Text>
          <View style={styles.progressBar}>
            <View style={[styles.progressFill, {width: "57%"}]} />
          </View>
        </View>

        <ScrollView
          style={styles.scrollContainer}
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
        >
          <View style={styles.headerContainer}>
            <Text style={styles.subtitle}>
              {t(
                "addressRegistration.description",
                "Lorem ipsum mauris amet dui donec nibh aliquam faucibus nulla."
              )}
            </Text>
          </View>

          <View style={styles.formContainer}>
            <InputField
              label={t("addressRegistration.zipCode", "CEP")}
              value={validation.data.zipCode}
              onChangeText={handleInputChange("zipCode")}
              placeholder={
                cepLookup.isPending
                  ? "Buscando endereço..."
                  : t(
                      "addressRegistration.zipCodePlaceholder",
                      "Insira seu CEP"
                    )
              }
              icon={markerIcon}
              inputMode="numeric"
              maxLength={9} // 99999-999
              error={validation.errors.zipCode}
              editable={!cepLookup.isPending}
            />
            <InputField
              label={t("addressRegistration.address", "Endereço")}
              value={validation.data.address}
              onChangeText={handleInputChange("address")}
              placeholder={t(
                "addressRegistration.addressPlaceholder",
                "Insira seu endereço"
              )}
              icon={homeIcon}
              error={validation.errors.address}
            />
            <InputField
              label={t("addressRegistration.number", "Número")}
              value={validation.data.number}
              onChangeText={handleInputChange("number")}
              placeholder={t(
                "addressRegistration.numberPlaceholder",
                "Insira o número do seu endereço"
              )}
              icon={hashIcon}
              inputMode="numeric"
              error={validation.errors.number}
            />
            <InputField
              label={t(
                "addressRegistration.complement",
                "Complemento (Opcional)"
              )}
              value={validation.data.complement}
              onChangeText={handleInputChange("complement")}
              placeholder={t(
                "addressRegistration.complementPlaceholder",
                "Apto, bloco, casa, etc."
              )}
              icon={homeIcon}
              error={validation.errors.complement}
            />
            <InputField
              label={t("addressRegistration.neighborhood", "Bairro")}
              value={validation.data.neighborhood}
              onChangeText={handleInputChange("neighborhood")}
              placeholder=""
              error={validation.errors.neighborhood}
            />
            <SelectField
              label={t("addressRegistration.state", "Estado")}
              value={validation.data.state}
              placeholder={t(
                "addressRegistration.selectState",
                "Selecione o estado"
              )}
              options={stateOptions}
              onSelect={handleStateSelect}
              error={validation.errors.state}
              loading={isLoadingStates}
            />
            <SelectField
              label={t("addressRegistration.city", "Cidade")}
              value={validation.data.city}
              placeholder={t(
                "addressRegistration.selectCity",
                "Selecione a cidade"
              )}
              options={cityOptions}
              onSelect={handleCitySelect}
              error={validation.errors.city}
              loading={isLoadingCities}
              disabled={!validation.data.state}
            />
          </View>
        </ScrollView>

        <View style={styles.buttonContainer}>
          <View style={styles.backButton}>
            <FullSizeButton
              text={t("addressRegistration.back", "Voltar")}
              variant="secondary"
              onPress={handleBack}
            />
          </View>
          <View style={styles.nextButton}>
            <FullSizeButton
              text={t("addressRegistration.next", "Avançar")}
              onPress={handleNext}
            />
          </View>
        </View>
      </View>
    </ImageBackground>
  );
};

export default AddressRegistration;
