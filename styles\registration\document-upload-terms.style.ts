import {StyleSheet} from "react-native";
import stylesConstants from "../styles-constants";

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: stylesConstants.colors.primary
  },
  backgroundImage: {
    flex: 1,
    width: "100%",
    height: "100%"
  },
  backgroundImageStyle: {
    resizeMode: "cover" as const
  },
  overlay: {
    position: "absolute" as const,
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: stylesConstants.colors.logoTextureBlackOpacity
  },
  contentContainer: {
    flex: 1,
    paddingHorizontal: 24,
    paddingVertical: 40
  },
  progressContainer: {
    marginBottom: 24
  },
  progressTitle: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: "600" as const,
    lineHeight: 24,
    textAlign: "center" as const
  },
  progressStep: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 18,
    fontWeight: "700" as const,
    lineHeight: 24,
    textAlign: "center" as const,
    marginBottom: 8
  },
  progressBar: {
    height: 4,
    backgroundColor: stylesConstants.colors.gray300,
    borderRadius: 2,
    marginBottom: 8
  },
  progressFill: {
    height: "100%",
    backgroundColor: "#E3691B", // Orange color from design
    borderRadius: 2
  },
  headerContainer: {
    marginBottom: 32,
    marginTop: 24
  },
  title: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 24,
    fontWeight: "700" as const,
    lineHeight: 32,
    textAlign: "center" as const,
    marginBottom: 8
  },
  subtitle: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: "400" as const,
    lineHeight: 24,
    textAlign: "center" as const
  },
  documentsSection: {
    marginBottom: 32
  },
  sectionTitle: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 18,
    fontWeight: "600" as const,
    lineHeight: 24,
    marginBottom: 16
  },
  uploadArea: {
    backgroundColor: "rgba(255, 255, 255, 0.1)",
    borderRadius: 12,
    borderWidth: 2,
    borderColor: "rgba(255, 255, 255, 0.3)",
    borderStyle: "dashed" as const,
    padding: 24,
    alignItems: "center" as const,
    justifyContent: "center" as const,
    marginBottom: 8
  },
  uploadIconContainer: {
    marginBottom: 8
  },
  uploadText: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: "600" as const,
    lineHeight: 24
  },
  fileSizeText: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "400" as const,
    lineHeight: 20,
    textAlign: "center" as const
  },
  selectedDocumentsContainer: {
    marginTop: 16,
    backgroundColor: "rgba(255, 255, 255, 0.05)",
    borderRadius: 12,
    padding: 16
  },
  selectedDocumentsTitle: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: "600" as const,
    lineHeight: 24,
    marginBottom: 12
  },
  documentsScrollView: {
    maxHeight: 200
  },
  documentItem: {
    backgroundColor: "rgba(255, 255, 255, 0.1)",
    borderRadius: 8,
    padding: 12,
    marginBottom: 8,
    flexDirection: "row" as const,
    alignItems: "center" as const,
    justifyContent: "space-between" as const
  },
  documentInfo: {
    flexDirection: "row" as const,
    alignItems: "center" as const,
    flex: 1
  },
  documentDetails: {
    marginLeft: 12,
    flex: 1
  },
  documentName: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "600" as const,
    lineHeight: 20
  },
  documentSize: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: "400" as const,
    lineHeight: 16
  },
  removeButton: {
    padding: 8,
    borderRadius: 6,
    backgroundColor: "rgba(255, 59, 48, 0.1)"
  },
  documentCard: {
    backgroundColor: "rgba(255, 255, 255, 0.1)",
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    flexDirection: "row" as const,
    alignItems: "center" as const
  },
  documentIconContainer: {
    marginRight: 12
  },
  documentContent: {
    flex: 1
  },
  documentTitle: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: "600" as const,
    lineHeight: 24,
    marginBottom: 4
  },
  documentDescription: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "400" as const,
    lineHeight: 20
  },
  termsSection: {
    marginBottom: 32
  },
  checkboxContainer: {
    flexDirection: "row" as const,
    alignItems: "flex-start" as const,
    marginBottom: 16
  },
  checkbox: {
    width: 20,
    height: 20,
    borderWidth: 2,
    borderColor: stylesConstants.colors.fullWhite,
    borderRadius: 4,
    marginRight: 12,
    marginTop: 2,
    justifyContent: "center" as const,
    alignItems: "center" as const
  },
  checkboxChecked: {
    backgroundColor: stylesConstants.colors.fullWhite,
    borderColor: "#E3691B"
  },
  checkboxText: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "400" as const,
    lineHeight: 20,
    flex: 1
  },
  linkText: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "400" as const,
    lineHeight: 20,
    textDecorationLine: "underline" as const
  },
  buttonContainer: {
    flexDirection: "row" as const,
    gap: 12,
    paddingTop: 16
  },
  backButton: {
    flex: 1
  },
  nextButton: {
    flex: 1
  }
});

export default styles;
