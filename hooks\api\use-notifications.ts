/**
 * Hooks React Query para Notificações
 * Implementa cache, invalidação e otimizações para notificações
 */

import {
  useQuery,
  useMutation,
  useQueryClient,
  useInfiniteQuery,
  UseQueryOptions,
  UseMutationOptions,
  UseInfiniteQueryOptions
} from "@tanstack/react-query";
import {
  Notification,
  NotificationPreferences,
  NotificationStats,
  NotificationsListParams,
  UpdateNotificationPreferencesRequest,
  PushNotificationRegistration,
  NotificationAnalytics,
  NotificationType,
  NotificationStatus,
  NotificationPriority
} from "@/models/api/notifications.models";
import {PaginationResponse} from "@/models/api/common.models";
import NotificationsService from "@/services/api/notifications/notifications.service";
import {BaseApiError} from "@/services/api/base/api-errors";
import {useGuestUser} from "@/contexts/guest-user-context";

// Chaves de query para cache
export const notificationsKeys = {
  all: ["notifications"] as const,
  lists: () => [...notificationsKeys.all, "list"] as const,
  list: (params?: NotificationsListParams) =>
    [...notificationsKeys.lists(), params] as const,
  details: () => [...notificationsKeys.all, "detail"] as const,
  detail: (id: string) => [...notificationsKeys.details(), id] as const,
  stats: () => [...notificationsKeys.all, "stats"] as const,
  preferences: () => [...notificationsKeys.all, "preferences"] as const,
  analytics: (period?: string) =>
    [...notificationsKeys.all, "analytics", period] as const,
  unread: () => [...notificationsKeys.all, "unread"] as const
};

/**
 * Hook para buscar lista de notificações
 */
export const useNotifications = (
  params?: NotificationsListParams,
  options?: UseQueryOptions<PaginationResponse<Notification>, BaseApiError>
) => {
  const {isGuest, isLoading: guestLoading} = useGuestUser();
  const isEnabled = options?.enabled !== false && !isGuest && !guestLoading;

  return useQuery({
    queryKey: notificationsKeys.list(params),
    queryFn: () => NotificationsService.getNotifications(params),
    staleTime: 2 * 60 * 1000, // 2 minutos
    gcTime: 5 * 60 * 1000, // 5 minutos
    enabled: isEnabled,
    refetchOnMount: isEnabled,
    refetchOnWindowFocus: isEnabled,
    refetchOnReconnect: isEnabled,
    ...options
  });
};

/**
 * Hook para buscar notificações com scroll infinito
 */
export const useInfiniteNotifications = (
  params?: Omit<NotificationsListParams, "page">,
  pageSize: number = 20,
  options?: UseInfiniteQueryOptions<
    PaginationResponse<Notification>,
    BaseApiError
  >
) => {
  return useInfiniteQuery({
    queryKey: notificationsKeys.list({...params, pageSize}),
    queryFn: ({pageParam = 1}) =>
      NotificationsService.getNotifications({
        ...params,
        page: pageParam as number,
        pageSize
      }),
    initialPageParam: 1,
    getNextPageParam: (lastPage) => {
      if (lastPage.pagination.hasNextPage) {
        return lastPage.pagination.currentPage + 1;
      }
      return undefined;
    },
    staleTime: 2 * 60 * 1000,
    gcTime: 5 * 60 * 1000,
    ...options
  });
};

/**
 * Hook para buscar notificação específica
 */
export const useNotification = (
  id: string,
  options?: UseQueryOptions<Notification, BaseApiError>
) => {
  return useQuery({
    queryKey: notificationsKeys.detail(id),
    queryFn: () => NotificationsService.getNotificationById(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutos
    gcTime: 10 * 60 * 1000, // 10 minutos
    ...options
  });
};

/**
 * Hook para buscar estatísticas de notificações
 */
export const useNotificationStats = (
  options?: UseQueryOptions<NotificationStats, BaseApiError>
) => {
  return useQuery({
    queryKey: notificationsKeys.stats(),
    queryFn: () => NotificationsService.getNotificationStats(),
    staleTime: 5 * 60 * 1000, // 5 minutos
    gcTime: 10 * 60 * 1000, // 10 minutos
    ...options
  });
};

/**
 * Hook para buscar preferências de notificação
 */
export const useNotificationPreferences = (
  options?: UseQueryOptions<NotificationPreferences, BaseApiError>
) => {
  return useQuery({
    queryKey: notificationsKeys.preferences(),
    queryFn: () => NotificationsService.getNotificationPreferences(),
    staleTime: 10 * 60 * 1000, // 10 minutos
    gcTime: 30 * 60 * 1000, // 30 minutos
    ...options
  });
};

/**
 * Hook para buscar notificações não lidas
 */
export const useUnreadNotifications = (
  options?: UseQueryOptions<Notification[], BaseApiError>
) => {
  const {isGuest, isLoading: guestLoading} = useGuestUser();
  const isEnabled = options?.enabled !== false && !isGuest && !guestLoading;

  return useQuery({
    queryKey: notificationsKeys.unread(),
    queryFn: () => NotificationsService.getUnreadNotifications(),
    staleTime: 1 * 60 * 1000, // 1 minuto
    gcTime: 5 * 60 * 1000, // 5 minutos
    refetchInterval: isEnabled ? 2 * 60 * 1000 : false, // Only poll if enabled
    refetchOnMount: isEnabled,
    refetchOnWindowFocus: isEnabled,
    refetchOnReconnect: isEnabled,
    enabled: isEnabled,
    ...options
  });
};

/**
 * Hook para buscar analytics de notificações
 */
export const useNotificationAnalytics = (
  period: "day" | "week" | "month" | "year" = "week",
  options?: UseQueryOptions<NotificationAnalytics, BaseApiError>
) => {
  return useQuery({
    queryKey: notificationsKeys.analytics(period),
    queryFn: () => NotificationsService.getNotificationAnalytics(period),
    staleTime: 15 * 60 * 1000, // 15 minutos
    gcTime: 60 * 60 * 1000, // 1 hora
    ...options
  });
};

/**
 * Hook para marcar notificação como lida
 */
export const useMarkNotificationAsRead = (
  options?: UseMutationOptions<Notification, BaseApiError, string>
) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => NotificationsService.markAsRead(id),
    onSuccess: (_, id) => {
      // Invalidar cache da notificação específica
      queryClient.invalidateQueries({queryKey: notificationsKeys.detail(id)});
      // Invalidar listas de notificações
      queryClient.invalidateQueries({queryKey: notificationsKeys.lists()});
      // Invalidar estatísticas
      queryClient.invalidateQueries({queryKey: notificationsKeys.stats()});
      // Invalidar notificações não lidas
      queryClient.invalidateQueries({queryKey: notificationsKeys.unread()});
    },
    ...options
  });
};

/**
 * Hook para marcar todas as notificações como lidas
 */
export const useMarkAllNotificationsAsRead = (
  options?: UseMutationOptions<
    {success: boolean; count: number},
    BaseApiError,
    void
  >
) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: () => NotificationsService.markAllAsRead(),
    onSuccess: () => {
      // Invalidar todo o cache de notificações
      queryClient.invalidateQueries({queryKey: notificationsKeys.all});
    },
    ...options
  });
};

/**
 * Hook para arquivar notificação
 */
export const useArchiveNotification = (
  options?: UseMutationOptions<Notification, BaseApiError, string>
) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => NotificationsService.archiveNotification(id),
    onSuccess: (_, id) => {
      // Invalidar cache da notificação específica
      queryClient.invalidateQueries({queryKey: notificationsKeys.detail(id)});
      // Invalidar listas de notificações
      queryClient.invalidateQueries({queryKey: notificationsKeys.lists()});
      // Invalidar estatísticas
      queryClient.invalidateQueries({queryKey: notificationsKeys.stats()});
    },
    ...options
  });
};

/**
 * Hook para deletar notificação
 */
export const useDeleteNotification = (
  options?: UseMutationOptions<void, BaseApiError, string>
) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => NotificationsService.deleteNotification(id),
    onSuccess: (_, id) => {
      // Remover do cache
      queryClient.removeQueries({queryKey: notificationsKeys.detail(id)});
      // Invalidar listas de notificações
      queryClient.invalidateQueries({queryKey: notificationsKeys.lists()});
      // Invalidar estatísticas
      queryClient.invalidateQueries({queryKey: notificationsKeys.stats()});
    },
    ...options
  });
};

/**
 * Hook para atualizar preferências de notificação
 */
export const useUpdateNotificationPreferences = (
  options?: UseMutationOptions<
    NotificationPreferences,
    BaseApiError,
    UpdateNotificationPreferencesRequest
  >
) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (preferences: UpdateNotificationPreferencesRequest) =>
      NotificationsService.updateNotificationPreferences(preferences),
    onSuccess: () => {
      // Invalidar cache de preferências
      queryClient.invalidateQueries({
        queryKey: notificationsKeys.preferences()
      });
    },
    ...options
  });
};

/**
 * Hook para registrar dispositivo para push notifications
 */
export const useRegisterDevice = (
  options?: UseMutationOptions<
    {success: boolean},
    BaseApiError,
    PushNotificationRegistration
  >
) => {
  return useMutation({
    mutationFn: (registration: PushNotificationRegistration) =>
      NotificationsService.registerDevice(registration),
    ...options
  });
};

/**
 * Hook para remover registro de dispositivo
 */
export const useUnregisterDevice = (
  options?: UseMutationOptions<{success: boolean}, BaseApiError, string>
) => {
  return useMutation({
    mutationFn: (deviceToken: string) =>
      NotificationsService.unregisterDevice(deviceToken),
    ...options
  });
};

/**
 * Hook para testar push notification
 */
export const useTestPushNotification = (
  options?: UseMutationOptions<
    {success: boolean; message: string},
    BaseApiError,
    void
  >
) => {
  return useMutation({
    mutationFn: () => NotificationsService.testPushNotification(),
    ...options
  });
};

/**
 * Hook para limpar notificações antigas
 */
export const useClearOldNotifications = (
  options?: UseMutationOptions<
    {success: boolean; deletedCount: number},
    BaseApiError,
    number
  >
) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (olderThanDays: number = 30) =>
      NotificationsService.clearOldNotifications(olderThanDays),
    onSuccess: () => {
      // Invalidar todo o cache de notificações
      queryClient.invalidateQueries({queryKey: notificationsKeys.all});
    },
    ...options
  });
};

/**
 * Hook para atualização otimista de notificação
 */
export const useOptimisticNotificationUpdate = () => {
  const queryClient = useQueryClient();

  const markAsReadOptimistic = (id: string) => {
    // Atualização otimista
    queryClient.setQueryData(
      notificationsKeys.detail(id),
      (old: Notification | undefined) => {
        if (old) {
          return {
            ...old,
            status: NotificationStatus.READ,
            readAt: new Date().toISOString()
          };
        }
        return old;
      }
    );

    // Atualizar listas
    queryClient.setQueriesData(
      {queryKey: notificationsKeys.lists()},
      (old: PaginationResponse<Notification> | undefined) => {
        if (old) {
          return {
            ...old,
            data: old.data.map((notification) =>
              notification.id === id
                ? {
                    ...notification,
                    status: NotificationStatus.READ,
                    readAt: new Date().toISOString()
                  }
                : notification
            )
          };
        }
        return old;
      }
    );
  };

  return {markAsReadOptimistic};
};
