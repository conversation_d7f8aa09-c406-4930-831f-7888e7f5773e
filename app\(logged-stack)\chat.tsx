import React, {useCallback, useMemo, useState, useEffect, useRef} from "react";
import {
  FlatList,
  Text,
  TouchableOpacity,
  View,
  ActivityIndicator,
  Alert,
  KeyboardAvoidingView,
  Platform,
  TextInput
} from "react-native";
import {useTranslation} from "react-i18next";
import {useLocalSearchParams} from "expo-router";
import {useQueryClient} from "@tanstack/react-query";
import ScreenWithHeader from "../../components/screen-with-header";
import Avatar from "../../components/user/avatar";

import SendIcon from "../../components/icons/send-icon";
import styles from "../../styles/logged-stack/chat.style";
import {
  useMessages,
  useSendMessage,
  useChat,
  chatsKeys
} from "@/hooks/api/use-chats";
import {MessageType, Message, MessageStatus} from "@/models/api/chats.models";
import {PaginationResponse} from "@/models/api/common.models";
import {useSignalRChatContextOptional} from "@/contexts/signalr-chat-context";
import {useAuth} from "@/contexts/AuthContext";

interface ChatMessageItem {
  id: string;
  text: string;
  isFromCurrentUser: boolean;
  time: string;
  avatar?: string;
  senderName?: string;
  createdAt: string;
  status?: "sending" | "sent" | "delivered" | "read" | "failed";
}

const Chat: React.FC = () => {
  const {t} = useTranslation();
  const [messageText, setMessageText] = useState("");
  const params = useLocalSearchParams();
  const LINE_HEIGHT = 20;
  const MAX_BREAKS = 3; // up to 3 line breaks (4 lines total)
  const [inputHeight, setInputHeight] = useState(LINE_HEIGHT);

  const flatListRef = useRef<FlatList>(null);
  const queryClient = useQueryClient();
  const autoScrollTimeoutRef = useRef<any>(null);

  // Get current user from auth context
  const {user: currentUser} = useAuth();
  const currentUserId = currentUser?.id;

  // Get chatId and name from route params, default to "1" for testing
  const chatId = (params.chatId as string) || "1";
  const fallbackChatName = (params.name as string) || "Phoenix Baker";

  // Fetch chat details to get proper chat information
  const {data: chatDetails} = useChat(chatId);

  // Determine the proper chat title (should show OTHER participant's name)
  const chatName = useMemo(() => {
    if (chatDetails?.name) {
      return chatDetails.name;
    }
    return fallbackChatName;
  }, [chatDetails?.name, fallbackChatName]);

  // SignalR real-time chat functionality
  const signalRChat = useSignalRChatContextOptional();

  // Fetch messages for this chat with SignalR real-time updates
  const {
    data: messagesData,
    isLoading: isLoadingMessages,
    isError: isMessagesError,
    refetch: refetchMessages
  } = useMessages(
    chatId,
    {
      pageSize: 50
    },
    {
      enabled: true // Re-enabled with SignalR support
    } as any
  );

  // Log SignalR connection status (SignalR is only for receiving messages)
  useEffect(() => {
    if (signalRChat?.isConnected) {
      console.log("✅ SignalR connected - ready to receive real-time messages");
    } else if (signalRChat?.connectionState.status === "connecting") {
      console.log("🔄 SignalR connecting...");
    } else if (signalRChat?.connectionState.status === "reconnecting") {
      console.log("🔄 SignalR reconnecting...");
    } else if (signalRChat?.connectionState.error) {
      console.log(
        "❌ SignalR connection error:",
        signalRChat.connectionState.error
      );
    } else {
      console.log("⚠️ SignalR disconnected");
    }
  }, [signalRChat?.isConnected, signalRChat?.connectionState]);

  // Send message mutation (disable automatic invalidation since we handle it manually)
  const sendMessageMutation = useSendMessage({
    onSuccess: () => {
      // Don't invalidate queries here - we handle cache updates manually for better UX
      // The optimistic update already shows the message instantly
    }
  });

  // Join/Leave chat room functionality disabled
  // SignalR is only used for receiving real-time notifications, not for room management
  useEffect(() => {
    // TODO: Re-enable when server implements proper chat room management
    // For now, SignalR only listens for incoming messages via ListenChats
    console.log("Chat component mounted for chatId:", chatId);

    return () => {
      console.log("Chat component unmounted for chatId:", chatId);
    };
  }, [chatId]);

  // Get typing users and online status from SignalR
  // Convert SignalR typing users to the format expected by TypingIndicator component
  const typingUsers = useMemo(() => {
    return (signalRChat?.typingUsers || []).map((user) => ({
      userId: user.userId.toString(),
      userName: user.userName,
      timestamp: user.startedAt.getTime()
    }));
  }, [signalRChat?.typingUsers]);

  // Typing indicator state and functions
  const [isTyping, setIsTyping] = useState(false);
  const typingTimeoutRef = React.useRef<any>(null);

  const startTyping = useCallback(() => {
    // Typing indicator disabled - SignalR only for receiving notifications
    // TODO: Implement typing indicator via REST API if needed
    if (!isTyping) {
      setIsTyping(true);
    }

    // Reset typing timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    typingTimeoutRef.current = setTimeout(() => {
      stopTyping();
    }, 3000); // Stop typing after 3 seconds of inactivity
  }, [isTyping]);

  const stopTyping = useCallback(() => {
    // Typing indicator disabled - SignalR only for receiving notifications
    // TODO: Implement typing indicator via REST API if needed
    if (isTyping) {
      setIsTyping(false);
    }

    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
      typingTimeoutRef.current = null;
    }
  }, [isTyping]);

  // Cleanup timeouts on unmount
  useEffect(() => {
    return () => {
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
      if (autoScrollTimeoutRef.current) {
        clearTimeout(autoScrollTimeoutRef.current);
      }
    };
  }, []);

  // Convert API messages to UI format with date grouping
  const chatData = useMemo(() => {
    if (!messagesData?.data || !currentUserId) return [];

    const messages = messagesData.data
      .map((message) => {
        const messageDate = new Date(message.createdAt);
        // Ensure both IDs are strings for comparison
        const isFromCurrentUser =
          String(message.senderId) === String(currentUserId);

        return {
          id: message.id,
          text: message.content, // Legacy Message type uses 'content'
          isFromCurrentUser,
          time: messageDate.toLocaleTimeString("pt-BR", {
            hour: "2-digit",
            minute: "2-digit"
          }),
          createdAt: message.createdAt,
          avatar: !isFromCurrentUser ? message.user?.avatarUrl : undefined,
          senderName: !isFromCurrentUser
            ? message.user?.name || chatName
            : undefined,
          status: "sent" as const
        };
      })
      .sort(
        (a, b) =>
          new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
      );

    // Group messages by date
    const grouped: Array<{type: "date" | "message"; data: any}> = [];
    let currentDate = "";

    messages.forEach((message) => {
      const messageDate = new Date(message.createdAt);
      const today = new Date();
      const yesterday = new Date(today);
      yesterday.setDate(yesterday.getDate() - 1);

      let dateLabel = "";
      if (messageDate.toDateString() === today.toDateString()) {
        dateLabel = "Hoje";
      } else if (messageDate.toDateString() === yesterday.toDateString()) {
        dateLabel = "Ontem";
      } else {
        dateLabel = messageDate.toLocaleDateString("pt-BR");
      }

      if (dateLabel !== currentDate) {
        grouped.push({type: "date", data: dateLabel});
        currentDate = dateLabel;
      }

      grouped.push({type: "message", data: message});
    });

    return grouped;
  }, [messagesData, chatName, currentUserId]);

  // Simple auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (chatData.length > 0) {
      // Clear any existing timeout
      if (autoScrollTimeoutRef.current) {
        clearTimeout(autoScrollTimeoutRef.current);
      }

      // Auto-scroll with a small delay to ensure rendering is complete
      autoScrollTimeoutRef.current = setTimeout(() => {
        flatListRef.current?.scrollToEnd({animated: false});
      }, 100);
    }
  }, [chatData.length]);

  // Render individual message bubble
  const renderMessage = useCallback(
    (message: ChatMessageItem) => (
      <View>
        <View
          style={[
            styles.messageRow,
            message.isFromCurrentUser && styles.messageRowUser
          ]}
        >
          {!message.isFromCurrentUser && (
            <View style={styles.avatarContainer}>
              <Avatar
                url={message.avatar}
                name={message.senderName}
                size={33.5}
                borderSize={0}
              />
            </View>
          )}

          <View
            style={[
              styles.messageContainer,
              message.isFromCurrentUser
                ? styles.messageFromUser
                : styles.messageFromOther
            ]}
          >
            <Text style={styles.messageText}>{message.text}</Text>
          </View>
        </View>

        {/* Timestamp for current user messages (right-aligned outside bubble) */}
        {message.isFromCurrentUser && (
          <View style={styles.timeContainer}>
            <Text style={styles.timeText}>
              {message.time}
              {message.status === "sending" && " • Enviando..."}
            </Text>
          </View>
        )}
      </View>
    ),
    []
  );

  // Render date divider
  const renderDateDivider = useCallback(
    (date: string) => (
      <View style={styles.dateDivider}>
        <Text style={styles.dateDividerText}>{date}</Text>
      </View>
    ),
    []
  );

  // Render list item (date or message)
  const renderItem = useCallback(
    ({item}: {item: {type: "date" | "message"; data: any}}) => {
      if (item.type === "date") {
        return renderDateDivider(item.data);
      }
      return renderMessage(item.data);
    },
    [renderMessage, renderDateDivider]
  );

  const handleSendMessage = useCallback(async () => {
    const messageContent = messageText.trim();

    // Always clear input immediately when function is called
    setMessageText("");
    setInputHeight(LINE_HEIGHT);

    if (messageContent && currentUserId) {
      const tempId = `temp-${Date.now()}-${Math.random()}`;

      // Create optimistic message
      const optimisticMessage: Message = {
        id: tempId,
        chatId,
        senderId: currentUserId,
        content: messageContent,
        type: MessageType.TEXT,
        status: MessageStatus.SENDING,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      // Optimistic update - add message to cache immediately
      const messagesQueryKey = chatsKeys.messagesList(chatId, {pageSize: 50});

      queryClient.setQueryData<PaginationResponse<Message>>(
        messagesQueryKey,
        (oldData) => {
          if (!oldData) return oldData;

          return {
            ...oldData,
            data: [...oldData.data, optimisticMessage]
          };
        }
      );

      try {
        // Send message to server
        const sentMessage = await sendMessageMutation.mutateAsync({
          chatId,
          message: {
            content: messageContent,
            type: MessageType.TEXT
          }
        });

        // Replace optimistic message with real message from server
        queryClient.setQueryData<PaginationResponse<Message>>(
          messagesQueryKey,
          (oldData) => {
            if (!oldData) return oldData;

            return {
              ...oldData,
              data: oldData.data.map((msg) =>
                msg.id === tempId ? sentMessage : msg
              )
            };
          }
        );
      } catch (error) {
        console.error("Erro ao enviar mensagem:", error);

        // Remove optimistic message on error
        queryClient.setQueryData<PaginationResponse<Message>>(
          messagesQueryKey,
          (oldData) => {
            if (!oldData) return oldData;

            return {
              ...oldData,
              data: oldData.data.filter((msg) => msg.id !== tempId)
            };
          }
        );

        Alert.alert(
          "Erro",
          "Não foi possível enviar a mensagem. Tente novamente."
        );
      }
    }
  }, [messageText, chatId, sendMessageMutation, currentUserId, queryClient]);

  // Show loading state
  if (isLoadingMessages) {
    return (
      <ScreenWithHeader
        screenTitle="Carregando..."
        backButton={true}
        disablePadding
        disableScrollView
      >
        <View
          style={[
            styles.container,
            {justifyContent: "center", alignItems: "center"}
          ]}
        >
          <ActivityIndicator size="large" color="#0F7C4D" />
          <Text style={{marginTop: 16, color: "#DFE9F0", fontFamily: "Ubuntu"}}>
            Carregando mensagens...
          </Text>
        </View>
      </ScreenWithHeader>
    );
  }

  // Show error state
  if (isMessagesError) {
    return (
      <ScreenWithHeader
        screenTitle="Erro"
        backButton={true}
        disablePadding
        disableScrollView
      >
        <View
          style={[
            styles.container,
            {justifyContent: "center", alignItems: "center"}
          ]}
        >
          <Text
            style={{
              color: "#DFE9F0",
              marginBottom: 16,
              fontFamily: "Ubuntu"
            }}
          >
            Erro ao carregar mensagens
          </Text>
          <TouchableOpacity
            style={{
              backgroundColor: "#0F7C4D",
              paddingHorizontal: 20,
              paddingVertical: 10,
              borderRadius: 8
            }}
            onPress={() => refetchMessages()}
          >
            <Text style={{color: "white", fontFamily: "Ubuntu"}}>
              Tentar novamente
            </Text>
          </TouchableOpacity>
        </View>
      </ScreenWithHeader>
    );
  }

  return (
    <ScreenWithHeader
      screenTitle={chatName}
      backButton={true}
      disablePadding
      disableScrollView
    >
      <KeyboardAvoidingView
        style={styles.container}
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        keyboardVerticalOffset={Platform.OS === "ios" ? 90 : 0}
      >
        {/* Messages List */}
        <FlatList
          ref={flatListRef}
          data={chatData}
          renderItem={renderItem}
          keyExtractor={(item, index) =>
            item.type === "date" ? `date-${index}` : item.data.id
          }
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.chatContent}
          style={styles.messageListContainer}
          inverted={false}
          removeClippedSubviews={false}
          maxToRenderPerBatch={50}
          windowSize={21}
          initialNumToRender={20}
        />

        {/* Typing Indicator */}
        {typingUsers.length > 0 && (
          <View style={styles.typingContainer}>
            <Text style={styles.typingText}>
              {typingUsers[0].userName} está digitando...
            </Text>
          </View>
        )}

        {/* Message Input */}
        <View style={styles.inputContainer}>
          <View style={styles.inputRow}>
            <View style={styles.textInputContainer}>
              <TextInput
                style={[styles.textInput, {height: inputHeight}]}
                value={messageText}
                onChangeText={(text) => {
                  setMessageText(text);
                  if (text.trim()) {
                    startTyping();
                  } else {
                    stopTyping();
                  }
                }}
                onContentSizeChange={(e) => {
                  const contentHeight = e.nativeEvent.contentSize.height;
                  const minH = LINE_HEIGHT;
                  const maxH = LINE_HEIGHT * (MAX_BREAKS + 1); // up to 3 breaks = 4 lines
                  const clamped = Math.max(minH, Math.min(contentHeight, maxH));
                  setInputHeight(clamped);
                }}
                placeholder="Digite sua mensagem"
                placeholderTextColor="#F2F4F7"
                multiline
                scrollEnabled
                maxLength={1000}
                editable={!sendMessageMutation.isPending}
              />
            </View>
            <TouchableOpacity
              style={[
                styles.sendButton,
                (!messageText.trim() || sendMessageMutation.isPending) &&
                  styles.sendButtonDisabled
              ]}
              onPress={() => {
                if (messageText.trim()) {
                  stopTyping();
                  handleSendMessage();
                }
              }}
              disabled={!messageText.trim() || sendMessageMutation.isPending}
            >
              {sendMessageMutation.isPending ? (
                <ActivityIndicator size="small" color="#FFFFFF" />
              ) : (
                <SendIcon />
              )}
            </TouchableOpacity>
          </View>
        </View>
      </KeyboardAvoidingView>
    </ScreenWithHeader>
  );
};

export default Chat;
