import React, {useCallback, useState} from "react";
import {
  Text,
  View,
  ColorValue,
  ImageBackground,
  TouchableOpacity,
  Alert,
  Sc<PERSON>View
} from "react-native";
import {useTranslation} from "react-i18next";
import FullSizeButton from "../../components/full-size-button";
import InputField from "../../components/input-field";
import PhoneIcon from "../../components/icons/phone-icon";
import MailIcon from "../../components/icons/mail-icon";
import LinkedinIcon from "../../components/icons/linkedin-icon";
import InstagramIcon from "../../components/icons/instagram-icon";
import FacebookIcon from "../../components/icons/facebook-icon";
import GlobeIcon from "../../components/icons/globe-icon";
import CompanyLogoIcon from "../../components/icons/company-logo-icon";
import BottomDrawer, {BottomDrawerOption} from "../../components/bottom-drawer";
import styles from "../../styles/registration/contacts-social-registration.style";
import Svg, {Path} from "react-native-svg";
import {useRouter, useLocalSearchParams} from "expo-router";
import {useContactsSocialValidation} from "@/hooks/use-registration-validation";
import {useRegistrationContext} from "@/contexts/RegistrationContext";

const ContactsSocialRegistration: React.FC = () => {
  const {t} = useTranslation();
  const router = useRouter();
  const params = useLocalSearchParams();
  const {updateContactsData, getAllData} = useRegistrationContext();

  const [isHowDidYouKnowDrawerVisible, setIsHowDidYouKnowDrawerVisible] =
    useState(false);
  const howDidYouKnowOptions: BottomDrawerOption[] = [
    {
      label: t("contactsSocialRegistration.howDidYouKnow.facebook", "Facebook"),
      value: "0"
    },
    {
      label: t(
        "contactsSocialRegistration.howDidYouKnow.instagram",
        "Instagram"
      ),
      value: "1"
    },
    {
      label: t("contactsSocialRegistration.howDidYouKnow.linkedin", "LinkedIn"),
      value: "2"
    },
    {
      label: t("contactsSocialRegistration.howDidYouKnow.others", "Outros"),
      value: "3"
    }
  ];

  const validation = useContactsSocialValidation(
    {
      email: "",
      phone: "",
      howDidYouKnow: "",
      facebook: "",
      instagram: "",
      linkedin: "",
      website: ""
    },
    (validData) => {
      console.log("Navegando para document-upload-terms com dados:", validData);

      const contactsData = {
        email: validData.email,
        phoneNumber: validData.phone,
        whereMet: validData.howDidYouKnow,
        facebook: validData.facebook,
        instagram: validData.instagram,
        linkedin: validData.linkedin,
        website: validData.website
      };

      updateContactsData(contactsData);

      const allData = getAllData();
      router.push({
        pathname: "/(registration)/document-upload-terms",
        params: allData
      });
    }
  );

  const handleInputChange = useCallback(
    (field: keyof typeof validation.data) => (value: string) => {
      let processedValue = value;

      if (field === "phone") {
        const digits = value.replace(/\D/g, "").slice(0, 11);
        if (digits.length <= 2) {
          processedValue = digits;
        } else if (digits.length <= 6) {
          processedValue = `(${digits.slice(0, 2)}) ${digits.slice(2)}`;
        } else if (digits.length <= 10) {
          processedValue = `(${digits.slice(0, 2)}) ${digits.slice(
            2,
            6
          )}-${digits.slice(6)}`;
        } else {
          processedValue = `(${digits.slice(0, 2)}) ${digits.slice(
            2,
            7
          )}-${digits.slice(7)}`;
        }
      }

      validation.updateField(field, processedValue);
    },
    [validation]
  );

  const mailIcon = useCallback(() => <MailIcon />, []);

  const phoneIcon = useCallback(
    (errorColor?: ColorValue) => <PhoneIcon replaceColor={errorColor} />,
    []
  );

  const facebookIcon = useCallback(
    (errorColor?: ColorValue) => <FacebookIcon replaceColor={errorColor} />,
    []
  );

  const instagramIcon = useCallback(
    (errorColor?: ColorValue) => <InstagramIcon replaceColor={errorColor} />,
    []
  );

  const linkedinIcon = useCallback(
    (errorColor?: ColorValue) => <LinkedinIcon replaceColor={errorColor} />,
    []
  );

  const globeIcon = useCallback(
    (errorColor?: ColorValue) => <GlobeIcon replaceColor={errorColor} />,
    []
  );

  const companyLogoIcon = useCallback(
    (errorColor?: ColorValue) => <CompanyLogoIcon replaceColor={errorColor} />,
    []
  );

  const handleHowDidYouKnowSelect = useCallback(() => {
    setIsHowDidYouKnowDrawerVisible(true);
  }, []);

  const handleHowDidYouKnowClose = useCallback(() => {
    setIsHowDidYouKnowDrawerVisible(false);
  }, []);

  const handleHowDidYouKnowOptionSelect = useCallback(
    (value: string) => {
      const selectedOption = howDidYouKnowOptions.find(
        (option) => option.value === value
      );
      if (selectedOption) {
        validation.updateField("howDidYouKnow", selectedOption.value);
      }
      setIsHowDidYouKnowDrawerVisible(false);
    },
    [howDidYouKnowOptions, validation]
  );

  const getHowDidYouKnowLabel = useCallback(() => {
    const selectedOption = howDidYouKnowOptions.find(
      (option) => option.value === validation.data.howDidYouKnow
    );
    return selectedOption?.label || "";
  }, [howDidYouKnowOptions, validation.data.howDidYouKnow]);

  const handleNext = useCallback(() => {
    const isValid = validation.handleSubmit();
    if (!isValid) {
      const errorMessages = Object.values(validation.errors).join("\n");
      Alert.alert(
        t("contactsSocialRegistration.error.title", "Erro de validação"),
        errorMessages ||
          t(
            "contactsSocialRegistration.error.fillRequired",
            "Preencha todos os campos obrigatórios"
          )
      );
    }
  }, [validation, t]);

  const handleBack = useCallback(() => {
    console.log("Voltando da tela de contacts-social-registration");
    router.back();
  }, [router]);

  return (
    <ImageBackground
      source={require("../../assets/images/backgroundregistration.png")}
      style={styles.backgroundImage}
      imageStyle={styles.backgroundImageStyle}
    >
      <View style={styles.contentContainer}>
        <View style={styles.backButtonTop}>
          <Text>
            <Svg width="8" height="14" viewBox="0 0 8 14" fill="none">
              <Path
                d="M7 13L1 7L7 1"
                stroke="#FCFCFD"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </Svg>
          </Text>
        </View>

        <ScrollView
          style={styles.scrollContainer}
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          <View style={styles.progressContainer}>
            <Text style={styles.progressTitle}>
              {t("contactsSocialRegistration.createAccount", "Criar conta")}
            </Text>
            <Text style={styles.progressStep}>
              {t(
                "contactsSocialRegistration.stepProgress",
                "5 / 7 Contatos e Redes Sociais"
              )}
            </Text>
            <View style={styles.progressBar}>
              <View style={[styles.progressFill, {width: "71%"}]} />
            </View>
          </View>

          <View style={styles.headerContainer}>
            <Text style={styles.subtitle}>
              {t(
                "contactsSocialRegistration.description",
                "Lorem ipsum mauris amet dui donec nibh aliquam faucibus nulla."
              )}
            </Text>
          </View>

          <View style={styles.formContainer}>
            <InputField
              label={t("contactsSocialRegistration.email", "E-mail")}
              value={validation.data.email}
              onChangeText={handleInputChange("email")}
              placeholder={t(
                "contactsSocialRegistration.emailPlaceholder",
                "Insira seu e-mail"
              )}
              icon={mailIcon}
              inputMode="email"
              error={validation.errors.email}
            />

            <InputField
              label={t(
                "contactsSocialRegistration.phone",
                "Celular / WhatsApp"
              )}
              value={validation.data.phone}
              onChangeText={handleInputChange("phone")}
              placeholder={t(
                "contactsSocialRegistration.phonePlaceholder",
                "Insira seu número de contato"
              )}
              icon={phoneIcon}
              inputMode="tel"
              maxLength={15} // (99) 99999-9999
              returnKeyType="done"
              error={validation.errors.phone}
            />

            <TouchableOpacity onPress={handleHowDidYouKnowSelect}>
              <InputField
                label={t(
                  "contactsSocialRegistration.howDidYouKnow",
                  "Como você conheceu o Club?"
                )}
                value={getHowDidYouKnowLabel()}
                onChangeText={() => {}}
                placeholder={t(
                  "contactsSocialRegistration.howDidYouKnowPlaceholder",
                  "Selecione uma categoria"
                )}
                icon={companyLogoIcon}
                showChevronDown={true}
                editable={false}
                error={validation.errors.howDidYouKnow}
              />
            </TouchableOpacity>

            <InputField
              label={t("contactsSocialRegistration.facebook", "Facebook")}
              value={validation.data.facebook}
              onChangeText={handleInputChange("facebook")}
              placeholder={t(
                "contactsSocialRegistration.facebookPlaceholder",
                "facebook.com/"
              )}
              icon={facebookIcon}
              error={validation.errors.facebook}
            />

            <InputField
              label={t("contactsSocialRegistration.instagram", "Instagram")}
              value={validation.data.instagram}
              onChangeText={handleInputChange("instagram")}
              placeholder={t(
                "contactsSocialRegistration.instagramPlaceholder",
                "instagram.com/"
              )}
              icon={instagramIcon}
              error={validation.errors.instagram}
            />

            <InputField
              label={t("contactsSocialRegistration.linkedin", "Linkedin")}
              value={validation.data.linkedin}
              onChangeText={handleInputChange("linkedin")}
              placeholder={t(
                "contactsSocialRegistration.linkedinPlaceholder",
                "linkedin.com/in/"
              )}
              icon={linkedinIcon}
              error={validation.errors.linkedin}
            />

            <InputField
              label={t("contactsSocialRegistration.website", "Website")}
              value={validation.data.website}
              onChangeText={handleInputChange("website")}
              placeholder={t(
                "contactsSocialRegistration.websitePlaceholder",
                "Insira o link do site"
              )}
              icon={globeIcon}
              error={validation.errors.website}
            />
          </View>
        </ScrollView>

        <View style={styles.buttonContainer}>
          <View style={styles.backButton}>
            <FullSizeButton
              text={t("contactsSocialRegistration.back", "Voltar")}
              variant="secondary"
              onPress={handleBack}
            />
          </View>
          <View style={styles.nextButton}>
            <FullSizeButton
              text={t("contactsSocialRegistration.next", "Avançar")}
              onPress={handleNext}
            />
          </View>
        </View>
      </View>

      <BottomDrawer
        visible={isHowDidYouKnowDrawerVisible}
        title={t(
          "contactsSocialRegistration.howDidYouKnow",
          "Como você conheceu o Club?"
        )}
        options={howDidYouKnowOptions}
        selectedValue={validation.data.howDidYouKnow}
        onSelect={handleHowDidYouKnowOptionSelect}
        onClose={handleHowDidYouKnowClose}
      />
    </ImageBackground>
  );
};

export default ContactsSocialRegistration;
