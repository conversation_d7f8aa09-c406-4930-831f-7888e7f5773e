import {StyleSheet} from "react-native";
import stylesConstants from "../styles-constants";

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 24,
    paddingTop: 8
  },

  // Header styles
  header: {
    paddingHorizontal: 16,
    paddingVertical: 16,
    backgroundColor: stylesConstants.colors.primaryBackground
  },
  headerTop: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginBottom: 16
  },
  title: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 20,
    fontWeight: 600,
    lineHeight: 28
  },
  placeholder: {
    width: 40
  },

  // Stats styles
  statsContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 16,
    paddingHorizontal: 8
  },
  statItem: {
    alignItems: "center",
    flex: 1
  },
  statNumber: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 24,
    fontWeight: 700,
    lineHeight: 32
  },
  statLabel: {
    color: stylesConstants.colors.textSecondary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: 400,
    lineHeight: 16,
    textAlign: "center"
  },

  // Panel styles (outer block like Motiff)
  panel: {
    backgroundColor: stylesConstants.colors.secondaryBackground,
    borderRadius: 20,
    padding: 16
  },

  // Search styles
  searchContainer: {marginBottom: 12},

  // Contact item styles (row style like Motiff)
  contactItem: {
    backgroundColor: "transparent",
    paddingVertical: 12,
    flexDirection: "row",
    alignItems: "center",
    gap: 12
  },
  planeIconCircle: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: stylesConstants.colors.mainBackgroundBorder,
    alignItems: "center",
    justifyContent: "center"
  },
  planeIcon: {opacity: 0.9},
  avatar: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: stylesConstants.colors.brand.brand400,
    alignItems: "center",
    justifyContent: "center"
  },
  avatarText: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: 600
  },
  contactInfo: {flex: 1},
  contactName: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: 700,
    lineHeight: 20,
    marginBottom: 4
  },
  contactEmail: {
    color: stylesConstants.colors.textSecondary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: 400,
    lineHeight: 16,
    marginBottom: 2,
    includeFontPadding: false
  },
  inviteDate: {
    color: stylesConstants.colors.textSecondary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: 400,
    lineHeight: 16,
    marginBottom: 0
  },
  contactMessage: {
    color: stylesConstants.colors.textSecondary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: 400,
    lineHeight: 16,
    fontStyle: "italic",
    marginTop: 4
  },
  contactActions: {
    alignItems: "flex-end",
    gap: 8,
    flexWrap: "wrap",
    position: "absolute",
    right: 20,
    top: 20
  },

  // Status styles
  statusRow: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8
  },
  statusText: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: 600,
    lineHeight: 16
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4
  },
  statusInvited: {
    backgroundColor: stylesConstants.colors.alert400
  },
  statusActive: {
    backgroundColor: stylesConstants.colors.green400
  },
  statusNotActive: {
    backgroundColor: stylesConstants.colors.error300
  },

  // Actions styles
  actionButtons: {
    flexDirection: "row",
    gap: 8
  },
  actionButton: {
    backgroundColor: stylesConstants.colors.brand.primary,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
    minWidth: 70,
    alignItems: "center"
  },
  actionButtonText: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: 600
  },
  cancelButton: {
    backgroundColor: stylesConstants.colors.error500
  },
  cancelButtonText: {
    color: stylesConstants.colors.fullWhite
  },

  // List styles
  contactsList: {
    flex: 1
  },

  // Loading styles
  loadingContainer: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 40
  },
  loadingText: {
    color: stylesConstants.colors.textSecondary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: 400,
    marginTop: 16,
    textAlign: "center"
  },

  // Empty state styles
  emptyContainer: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 40,
    paddingHorizontal: 20
  },
  emptyText: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: 600,
    textAlign: "center",
    marginBottom: 8
  },
  emptySubtext: {
    color: stylesConstants.colors.textSecondary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: 400,
    textAlign: "center"
  }
});

export default styles;
