/**
 * Cliente API aprimorado para ClubM
 * Substitui o ApiService original com recursos avançados
 */

import {QueryClient} from "@tanstack/react-query";
import axios, {AxiosInstance, AxiosRequestConfig, AxiosResponse} from "axios";
import {from, map, Observable, throwError, retry, timer} from "rxjs";
import {catchError} from "rxjs/operators";

import {
  ApiConfig,
  TokenData,
  RefreshTokenResponse,
  ApiMetrics,
  NetworkStatus,
  LogLevel
} from "./api-types";
import {ApiErrorFactory, ErrorUtils, BaseApiError} from "./api-errors";
import {ApiLogger} from "./api-logger";
import {FormDataClient} from "./form-data-client";

export type Method = "GET" | "POST" | "PUT" | "DELETE" | "PATCH";

export class EnhancedApiService {
  private static instance: EnhancedApiService;
  private axiosInstance: AxiosInstance;
  private readonly queryClient: QueryClient;
  private config: ApiConfig;
  private tokenData: TokenData | null = null;
  private refreshPromise: Promise<string> | null = null;
  private metrics: ApiMetrics;
  private networkStatus: NetworkStatus = NetworkStatus.Unknown;
  private onTokenExpired: (() => void) | null = null;
  private onForbiddenAccess: (() => void) | null = null;
  private onNetworkStatusChange: ((status: NetworkStatus) => void) | null =
    null;

  private constructor() {
    this.config = this.getDefaultConfig();
    this.queryClient = new QueryClient({
      defaultOptions: {
        queries: {
          staleTime: 5 * 60 * 1000, // 5 minutos
          gcTime: 10 * 60 * 1000, // 10 minutos (anteriormente cacheTime)
          retry: (failureCount, error) => {
            return failureCount < 3 && ErrorUtils.isRetryableError(error);
          }
        }
      }
    });
    this.metrics = this.initializeMetrics();
    this.axiosInstance = this.createAxiosInstance();
    this.setupInterceptors();

    // Configurar logger
    ApiLogger.configure({
      maxLogs: 1000,
      enabledLevels: __DEV__
        ? [LogLevel.DEBUG, LogLevel.INFO, LogLevel.WARN, LogLevel.ERROR]
        : [LogLevel.WARN, LogLevel.ERROR]
    });

    // Configurar FormDataClient com auth provider
    FormDataClient.setAuthProvider(() => this.tokenData);
  }

  /**
   * Singleton instance
   */
  static getInstance(): EnhancedApiService {
    if (!this.instance) {
      this.instance = new EnhancedApiService();
    }
    return this.instance;
  }

  /**
   * Configuração padrão
   */
  private getDefaultConfig(): ApiConfig {
    return {
      baseURL: process.env["EXPO_PUBLIC_API_BASE_URL"] ?? "",
      timeout: 30000, // 30 segundos
      retryAttempts: 3,
      retryDelay: 1000, // 1 segundo
      enableLogging: __DEV__
    };
  }

  /**
   * Inicializa métricas
   */
  private initializeMetrics(): ApiMetrics {
    return {
      requestCount: 0,
      errorCount: 0,
      averageResponseTime: 0,
      successRate: 0
    };
  }

  /**
   * Cria instância do Axios
   */
  private createAxiosInstance(): AxiosInstance {
    const instance = axios.create({
      baseURL: this.config.baseURL,
      timeout: this.config.timeout,
      headers: {
        "Content-Type": "application/json",
        Accept: "application/json",
        "Accept-Language": "pt-br"
      }
    });

    return instance;
  }

  /**
   * Configura interceptors
   */
  private setupInterceptors(): void {
    // Request interceptor
    this.axiosInstance.interceptors.request.use(
      async (config) => {
        const startTime = Date.now();
        const requestId = ApiLogger.generateRequestId();
        (config as any).metadata = {startTime, requestId};

        // Adicionar token de autenticação
        if (this.tokenData?.accessToken) {
          config.headers.Authorization = `${this.tokenData.tokenType} ${this.tokenData.accessToken}`;
          console.log("🔑 Token aplicado na requisição:", {
            url: config.url,
            hasToken: !!this.tokenData.accessToken,
            tokenType: this.tokenData.tokenType
          });
        } else {
          console.log(
            "❌ Nenhum token disponível para requisição:",
            config.url
          );
        }

        // Enhanced request logging
        if (this.config.enableLogging) {
          ApiLogger.logRequest(
            {
              url: config.url,
              method: config.method,
              headers: config.headers,
              data: config.data,
              params: config.params
            },
            this.config.baseURL,
            requestId
          );
        }

        return config;
      },
      (error) => {
        ApiLogger.logError(error);
        return Promise.reject(ApiErrorFactory.createFromAxiosError(error));
      }
    );

    // Response interceptor
    this.axiosInstance.interceptors.response.use(
      (response) => {
        const metadata = (response.config as any).metadata;
        const duration = Date.now() - (metadata?.startTime || 0);
        const requestId = metadata?.requestId;

        // Atualizar métricas
        this.updateMetrics(true, duration);

        // Enhanced response logging
        if (this.config.enableLogging) {
          ApiLogger.logResponse(
            {
              data: response.data,
              status: response.status,
              statusText: response.statusText,
              headers: response.headers as Record<string, string>
            },
            duration,
            requestId,
            {
              url: response.config.url,
              method: response.config.method,
              params: response.config.params,
              data: response.config.data
            }
          );
        }

        return response;
      },
      async (error) => {
        const metadata = error.config?.metadata;
        const duration = Date.now() - (metadata?.startTime || 0);
        const requestId = metadata?.requestId;

        // Atualizar métricas
        this.updateMetrics(false, duration);

        // Enhanced error logging
        if (this.config.enableLogging) {
          ApiLogger.logError(
            error,
            {
              url: error.config?.url,
              method: error.config?.method,
              params: error.config?.params,
              data: error.config?.data
            },
            requestId
          );
        }

        // Tentar renovar token se for erro 401
        if (error.response?.status === 401 && this.tokenData) {
          // Somente tentar refresh se houver refreshToken disponível
          if (this.tokenData.refreshToken) {
            try {
              const newToken = await this.refreshToken();
              if (newToken && error.config) {
                error.config.headers.Authorization = `${this.tokenData?.tokenType} ${newToken}`;
                return this.axiosInstance.request(error.config);
              }
            } catch (refreshError) {
              ApiLogger.logTokenRefresh(false, refreshError);
              this.handleTokenExpired();
            }
          } else {
            // Caso típico de usuário upsell/guest sem refresh token: não limpar token
            // apenas propagar o erro 401 para o caller tratar (ex.: esconder recursos)
            ApiLogger.warn(
              "401 recebido mas sem refreshToken - preservando token atual (guest/upsell)",
              {url: error.config?.url}
            );
            return Promise.reject(ApiErrorFactory.createFromAxiosError(error));
          }
        }

        // Lidar com erro 403 (acesso negado)
        if (error.response?.status === 403) {
          ApiLogger.warn("Acesso negado (403)", {
            url: error.config?.url,
            method: error.config?.method,
            userId: this.tokenData?.userId
          });
          // Para 403, não tentamos refresh - é um problema de permissão
          this.handleForbiddenAccess();
        }

        return Promise.reject(ApiErrorFactory.createFromAxiosError(error));
      }
    );
  }

  /**
   * Atualiza métricas
   */
  private updateMetrics(success: boolean, responseTime: number): void {
    this.metrics.requestCount++;
    this.metrics.lastRequestTime = new Date();

    if (!success) {
      this.metrics.errorCount++;
    }

    // Calcular tempo médio de resposta
    const totalTime =
      this.metrics.averageResponseTime * (this.metrics.requestCount - 1) +
      responseTime;
    this.metrics.averageResponseTime = totalTime / this.metrics.requestCount;

    // Calcular taxa de sucesso
    this.metrics.successRate =
      ((this.metrics.requestCount - this.metrics.errorCount) /
        this.metrics.requestCount) *
      100;
  }

  /**
   * Requisição principal com retry automático
   */
  public request<T>(
    method: Method,
    endpoint: string,
    data?: any,
    config?: AxiosRequestConfig
  ): Observable<T> {
    const isGet = method === "GET" || method === "DELETE";
    const cleanData = this.cleanDeep(data);

    const requestConfig: AxiosRequestConfig = {
      url: endpoint,
      method: method,
      data: isGet ? undefined : cleanData,
      params: isGet ? cleanData : undefined,
      ...config
    };

    // Add metadata for logging correlation
    (requestConfig as any).metadata = {
      startTime: Date.now(),
      requestId: ApiLogger.generateRequestId()
    };

    const request$ = from(this.axiosInstance.request<T>(requestConfig)).pipe(
      map((response: AxiosResponse<T>) => response.data)
    );

    // Aplicar retry com backoff exponencial
    return request$.pipe(
      retry({
        count: this.config.retryAttempts,
        delay: (error, retryCount) => {
          if (!ErrorUtils.isRetryableError(error)) {
            throw error;
          }

          const delay = ErrorUtils.getRetryDelay(
            retryCount - 1,
            this.config.retryDelay
          );

          if (this.config.enableLogging) {
            const requestId = (requestConfig as any).metadata?.requestId;
            ApiLogger.logRetry(
              retryCount,
              this.config.retryAttempts,
              error,
              delay,
              requestId
            );
          }

          return timer(delay);
        }
      }),
      catchError((error: any) => {
        return throwError(() =>
          error instanceof BaseApiError
            ? error
            : ApiErrorFactory.createFromAxiosError(error)
        );
      })
    );
  }

  /**
   * Métodos de conveniência
   */
  public get<T>(
    endpoint: string,
    params?: any,
    config?: AxiosRequestConfig
  ): Observable<T> {
    return this.request<T>("GET", endpoint, params, config);
  }

  public post<T>(
    endpoint: string,
    data?: any,
    config?: AxiosRequestConfig
  ): Observable<T> {
    return this.request<T>("POST", endpoint, data, config);
  }

  public put<T>(
    endpoint: string,
    data?: any,
    config?: AxiosRequestConfig
  ): Observable<T> {
    return this.request<T>("PUT", endpoint, data, config);
  }

  public patch<T>(
    endpoint: string,
    data?: any,
    config?: AxiosRequestConfig
  ): Observable<T> {
    return this.request<T>("PATCH", endpoint, data, config);
  }

  public delete<T>(
    endpoint: string,
    params?: any,
    config?: AxiosRequestConfig
  ): Observable<T> {
    return this.request<T>("DELETE", endpoint, params, config);
  }

  /**
   * Configurar token de autenticação
   */
  public setAuthToken(tokenData: TokenData): void {
    console.log("🔧 setAuthToken chamado:", {
      hasAccessToken: !!tokenData.accessToken,
      tokenType: tokenData.tokenType,
      expiresIn: tokenData.expiresIn
    });

    this.tokenData = tokenData;

    // Calcular data de expiração se não fornecida
    if (!tokenData.expiresAt && tokenData.expiresIn) {
      this.tokenData.expiresAt = new Date(
        Date.now() + tokenData.expiresIn * 1000
      );
    }

    console.log("✅ Token configurado no apiClient:", {
      tokenType: this.tokenData.tokenType,
      expiresAt: this.tokenData.expiresAt,
      hasToken: !!this.tokenData.accessToken
    });

    ApiLogger.info("Token de autenticação configurado", {
      tokenType: tokenData.tokenType,
      expiresAt: this.tokenData.expiresAt
    });
  }

  /**
   * Remover token de autenticação
   */
  public clearAuthToken(): void {
    this.tokenData = null;
    this.refreshPromise = null;
    ApiLogger.info("Token de autenticação removido");
  }

  /**
   * Verificar se o token está expirado
   */
  public isTokenExpired(): boolean {
    if (!this.tokenData?.expiresAt) return false;

    // Considerar expirado 5 minutos antes do tempo real
    const bufferTime = 5 * 60 * 1000; // 5 minutos
    return (
      new Date().getTime() > this.tokenData.expiresAt.getTime() - bufferTime
    );
  }

  /**
   * Obter token válido (renovando se necessário)
   */
  public async getValidToken(): Promise<TokenData | null> {
    try {
      // Se não há token, retornar null
      if (!this.tokenData) {
        ApiLogger.warn("Nenhum token disponível");
        return null;
      }

      // Se não há informação de expiração, considerar válido
      if (!this.tokenData.expiresAt) {
        return this.tokenData;
      }

      // Se o token não está expirado, retornar o atual
      if (!this.isTokenExpired()) {
        return this.tokenData;
      }

      // Token considerado expirado
      // Se houver refreshToken, tentar renovar; caso contrário, preservar e retornar token atual (fluxo guest/upsell)
      if (this.tokenData.refreshToken) {
        ApiLogger.info("Token expirado, tentando renovar...");
        const newAccessToken = await this.refreshToken();
        if (newAccessToken && this.tokenData) {
          return this.tokenData;
        }
        // Renovação falhou: limpar token
        this.clearAuthToken();
        return null;
      } else {
        ApiLogger.warn(
          "Token expirado mas sem refreshToken - retornando token atual (guest/upsell)"
        );
        return this.tokenData; // Não limpar nem tentar renovar
      }
    } catch (error) {
      ApiLogger.error("Erro ao obter token válido", error as Error);
      // Somente limpar token se havia refreshToken (indicando sessão completa)
      if (this.tokenData?.refreshToken) {
        this.clearAuthToken();
        return null;
      }
      // Sem refreshToken: preservar token atual (guest/upsell)
      return this.tokenData ?? null;
    }
  }

  /**
   * Renovar token automaticamente
   */
  private async refreshToken(): Promise<string | null> {
    if (this.refreshPromise) {
      return this.refreshPromise;
    }

    if (!this.tokenData?.refreshToken) {
      throw new Error("Refresh token não disponível");
    }

    this.refreshPromise = this.performTokenRefresh();

    try {
      const newToken = await this.refreshPromise;
      this.refreshPromise = null;
      return newToken;
    } catch (error) {
      this.refreshPromise = null;
      throw error;
    }
  }

  /**
   * Executar renovação do token
   */
  private async performTokenRefresh(): Promise<string> {
    try {
      const response = await this.axiosInstance.post<RefreshTokenResponse>(
        "/auth/refresh",
        {
          refreshToken: this.tokenData?.refreshToken
        }
      );

      const {accessToken, tokenType, expiresIn} = response.data;

      // Atualizar token data
      if (this.tokenData) {
        this.tokenData.accessToken = accessToken;
        this.tokenData.tokenType = tokenType;
        this.tokenData.expiresIn = expiresIn;
        this.tokenData.expiresAt = new Date(Date.now() + expiresIn * 1000);
      }

      ApiLogger.logTokenRefresh(true);
      return accessToken;
    } catch (error) {
      ApiLogger.logTokenRefresh(false, error);
      throw error;
    }
  }

  /**
   * Lidar com token expirado
   */
  private handleTokenExpired(): void {
    this.clearAuthToken();
    this.onTokenExpired?.();
  }

  /**
   * Lidar com acesso negado (403)
   */
  private handleForbiddenAccess(): void {
    ApiLogger.warn(
      "Acesso negado - usuário não tem permissão para este recurso"
    );
    this.onForbiddenAccess?.();
  }

  /**
   * Configurar callback para token expirado
   */
  public setOnTokenExpired(callback: (() => void) | null): void {
    this.onTokenExpired = callback;
  }

  /**
   * Configurar callback para acesso negado
   */
  setOnForbiddenAccess(callback: () => void): void {
    this.onForbiddenAccess = callback;
  }

  /**
   * Configurar callback para mudança de status de rede
   */
  public setOnNetworkStatusChange(
    callback: ((status: NetworkStatus) => void) | null
  ): void {
    this.onNetworkStatusChange = callback;
  }

  /**
   * Atualizar status de rede
   */
  public updateNetworkStatus(status: NetworkStatus): void {
    if (this.networkStatus !== status) {
      this.networkStatus = status;
      this.onNetworkStatusChange?.(status);

      ApiLogger.info(`Status de rede alterado para: ${status}`);
    }
  }

  /**
   * Obter métricas da API
   */
  public getMetrics(): ApiMetrics {
    return {...this.metrics};
  }

  /**
   * Resetar métricas
   */
  public resetMetrics(): void {
    this.metrics = this.initializeMetrics();
    ApiLogger.info("Métricas resetadas");
  }

  /**
   * Obter QueryClient
   */
  public getQueryClient(): QueryClient {
    return this.queryClient;
  }

  /**
   * Configurar API
   */
  public configure(newConfig: Partial<ApiConfig>): void {
    this.config = {...this.config, ...newConfig};

    // Recriar instância do Axios se necessário
    if (newConfig.baseURL || newConfig.timeout) {
      this.axiosInstance = this.createAxiosInstance();
      this.setupInterceptors();
    }

    ApiLogger.info("Configuração da API atualizada", newConfig);
  }

  /**
   * Limpar dados profundamente (remove undefined e objetos vazios)
   */
  private cleanDeep(obj: unknown): any {
    if (obj === null || typeof obj !== "object") return obj;

    if (Array.isArray(obj)) {
      return obj
        .map((item) => this.cleanDeep(item))
        .filter((item) => item !== undefined);
    }

    const entries = Object.entries(obj)
      .map(([key, value]) => [
        key,
        typeof value === "object" && value !== null
          ? this.cleanDeep(value)
          : value
      ])
      .filter(
        ([_, value]) =>
          value !== undefined &&
          !(
            typeof value === "object" &&
            value !== null &&
            !Array.isArray(value) &&
            Object.keys(value).length === 0
          )
      );

    return Object.fromEntries(entries);
  }

  /**
   * Verificar saúde da API
   */
  public async healthCheck(): Promise<boolean> {
    try {
      await this.axiosInstance.get("/health");
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Cancelar todas as requisições pendentes
   */
  public cancelAllRequests(): void {
    // Implementar cancelamento se necessário
    ApiLogger.info("Todas as requisições foram canceladas");
  }
}

// Exportar instância singleton
export const apiClient = EnhancedApiService.getInstance();

// Export FormDataClient for easy access
export {FormDataClient} from "./form-data-client";

// Manter compatibilidade com o ApiService original
export default class ApiService {
  static readonly queryClient = apiClient.getQueryClient();

  static request<T>(
    method: Method,
    endpoint: string,
    data?: object
  ): Observable<T> {
    return apiClient.request<T>(method, endpoint, data);
  }

  static set authenticationToken(token: string | null) {
    if (token) {
      apiClient.setAuthToken({
        accessToken: token,
        tokenType: "Bearer",
        expiresIn: 3600 // 1 hora por padrão
      });
    } else {
      apiClient.clearAuthToken();
    }
  }

  static set onExpiredToken(callback: (() => void) | null) {
    apiClient.setOnTokenExpired(callback);
  }

  static getMetrics() {
    return apiClient.getMetrics();
  }

  static configure(config: Partial<ApiConfig>) {
    return apiClient.configure(config);
  }
}
