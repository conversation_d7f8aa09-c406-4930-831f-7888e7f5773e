import React, {useState, useCallback} from "react";
import {
  Text,
  View,
  ColorValue,
  ImageBackground,
  TouchableOpacity,
  Alert
} from "react-native";
import Screen from "../../components/screen";
import BackButton from "../../components/back-button";
import {useTranslation} from "react-i18next";
import FullSizeButton from "../../components/full-size-button";
import InputField from "../../components/input-field";
import UserIcon from "../../components/icons/user-icon";
import LicenceThirdPartyIcon from "../../components/icons/licence-third-party-icon";

import GenderIcon from "../../components/icons/gender-icon";
import BuildingIcon from "../../components/icons/building-icon";
import ChevronDownIcon from "../../components/icons/chevron-down-icon";
import BottomDrawer, {BottomDrawerOption} from "@/components/bottom-drawer";
import DateTimePickerDrawer from "../../components/date-time-picker-drawer";
import styles from "@/styles/registration/personal-data-registration.style";
import {useRouter, useLocalSearchParams} from "expo-router";
import {usePersonalDataValidation} from "@/hooks/use-registration-validation";
import {formatCpf} from "@/utils/cpf";
import {useFranchisesByPlan} from "@/hooks/api";
import {FranchiseViewModel} from "@/services/api/plans/plans.service";
import {useRegistrationContext} from "@/contexts/RegistrationContext";

const PersonalDataRegistration: React.FC = () => {
  const {t} = useTranslation();
  const router = useRouter();
  const params = useLocalSearchParams();
  const {updatePersonalData} = useRegistrationContext();

  const {
    data: franchisesResponse,
    isLoading: isLoadingFranchises,
    error: franchisesError
  } = useFranchisesByPlan(
    params.selectedPlanId ? parseInt(params.selectedPlanId as string) : 0,
    {page: 1, pageSize: 50}
  );

  const franchises = franchisesResponse?.data || [];
  const validation = usePersonalDataValidation(
    {
      fullName: "",
      cpf: "",
      birthDate: "",
      gender: "",
      branch: ""
    },
    (validData) => {
      console.log(
        "🔍 [PERSONAL-DATA] Navegando para professional-registration com dados:",
        validData
      );

      const personalData = {
        fullName: validData.fullName,
        document: validData.cpf,
        birthDate: validData.birthDate,
        sex: validData.gender,
        selectedFranchiseId: validData.branch,
        selectedPlanId: params.selectedPlanId as string,
        selectedPlanTermId: (params.selectedPlanTermId as string) || ""
      } as const;

      updatePersonalData(personalData);

      router.push("/(registration)/professional-registration");
    }
  );

  const [drawerVisible, setDrawerVisible] = useState(false);
  const [drawerType, setDrawerType] = useState<"gender" | "branch" | null>(
    null
  );
  const [showDatePicker, setShowDatePicker] = useState(false);

  const genderOptions: BottomDrawerOption[] = [
    {
      label: t("personalDataRegistration.genderFemale", "Feminino"),
      value: "0"
    },
    {
      label: t("personalDataRegistration.genderMale", "Masculino"),
      value: "1"
    },
    {
      label: t("personalDataRegistration.genderOther", "Outro"),
      value: "2"
    }
  ];

  const branchOptions: BottomDrawerOption[] = franchises.map(
    (franchise: FranchiseViewModel) => ({
      label: `${franchise.name} - ${franchise.addressCity}, ${franchise.addressState}`,
      value: franchise.id.toString()
    })
  );

  const handleInputChange = useCallback(
    (field: keyof typeof validation.data) => (value: string) => {
      let processedValue = value;

      if (field === "cpf") {
        processedValue = formatCpf(value);
      }

      validation.updateField(field, processedValue);
    },
    [validation]
  );

  const openGenderDrawer = useCallback(() => {
    setDrawerType("gender");
    setDrawerVisible(true);
  }, []);

  const openBranchDrawer = useCallback(() => {
    setDrawerType("branch");
    setDrawerVisible(true);
  }, []);

  const closeDrawer = useCallback(() => {
    setDrawerVisible(false);
    setDrawerType(null);
  }, []);

  const handleDrawerSelect = useCallback(
    (value: string) => {
      if (drawerType === "gender") {
        validation.updateField("gender", value);
      } else if (drawerType === "branch") {
        validation.updateField("branch", value);
      }
      setDrawerVisible(false);
      setDrawerType(null);
    },
    [drawerType, validation]
  );

  const getSelectedGenderLabel = useCallback(() => {
    const option = genderOptions.find(
      (opt) => opt.value === validation.data.gender
    );
    return option?.label || "";
  }, [validation.data.gender, genderOptions]);

  const getSelectedBranchLabel = useCallback(() => {
    const option = branchOptions.find(
      (opt) => opt.value === validation.data.branch
    );
    return option?.label || "";
  }, [validation.data.branch, branchOptions]);

  const openDatePicker = useCallback(() => {
    setShowDatePicker(true);
  }, []);

  const closeDatePicker = useCallback(() => {
    setShowDatePicker(false);
  }, []);

  const handleDateSelect = useCallback(
    (date: Date) => {
      const day = date.getDate().toString().padStart(2, "0");
      const month = (date.getMonth() + 1).toString().padStart(2, "0");
      const year = date.getFullYear().toString();
      const formattedDate = `${day}/${month}/${year}`;

      validation.updateField("birthDate", formattedDate);
      setShowDatePicker(false);
    },
    [validation]
  );

  const getSelectedDateObject = useCallback(() => {
    if (!validation.data.birthDate) return undefined;

    const parts = validation.data.birthDate.split("/");
    if (parts.length === 3) {
      const day = parseInt(parts[0], 10);
      const month = parseInt(parts[1], 10) - 1;
      const year = parseInt(parts[2], 10);
      return new Date(year, month, day);
    }
    return undefined;
  }, [validation.data.birthDate]);

  const userIcon = useCallback(
    (errorColor?: ColorValue) => <UserIcon replaceColor={errorColor} />,
    []
  );

  const cpfIcon = useCallback(
    (errorColor?: ColorValue) => (
      <LicenceThirdPartyIcon replaceColor={errorColor} />
    ),
    []
  );

  const calendarIcon = useCallback(
    () => (
      <View
        style={{
          width: 20,
          height: 20,
          backgroundColor: "#666",
          borderRadius: 2,
          justifyContent: "center",
          alignItems: "center"
        }}
      >
        <Text style={{color: "white", fontSize: 10, fontWeight: "bold"}}>
          📅
        </Text>
      </View>
    ),
    []
  );

  const genderIcon = useCallback(
    (errorColor?: ColorValue) => <GenderIcon replaceColor={errorColor} />,
    []
  );

  const buildingIcon = useCallback(
    (errorColor?: ColorValue) => <BuildingIcon replaceColor={errorColor} />,
    []
  );

  const handleNext = useCallback(() => {
    const isValid = validation.handleSubmit();
    if (!isValid) {
      const errorMessages = Object.values(validation.errors).join("\n");
      Alert.alert(
        t("personalDataRegistration.error.title", "Erro de validação"),
        errorMessages ||
          t(
            "personalDataRegistration.error.fillRequired",
            "Preencha todos os campos obrigatórios"
          )
      );
    }
  }, [validation, t]);

  const handleBack = useCallback(() => {
    console.log("Voltando da tela de personal-data-registration");
    router.back();
  }, [router]);

  return (
    <Screen>
      <ImageBackground
        source={require("../../assets/images/backgroundregistration.png")}
        style={styles.backgroundImage}
        imageStyle={styles.backgroundImageStyle}
      >
        <View style={styles.overlay} />
        <View style={styles.contentContainer}>
          <BackButton />

          <View style={styles.progressContainer}>
            <Text style={styles.progressTitle}>
              {t("personalDataRegistration.createAccount", "Criar conta")}
            </Text>
            <Text style={styles.progressStep}>
              {t("personalDataRegistration.stepProgress", "2 / 7 Dados gerais")}
            </Text>
            <View style={styles.progressBar}>
              <View style={[styles.progressFill, {width: "28%"}]} />
            </View>
          </View>

          <View style={styles.headerContainer}>
            <Text style={styles.subtitle}>
              {t(
                "personalDataRegistration.description",
                "Lorem ipsum mauris amet dui donec nibh aliquam faucibus nulla."
              )}
            </Text>
          </View>

          <View style={styles.formContainer}>
            <InputField
              label={t("personalDataRegistration.fullName", "Nome completo")}
              value={validation.data.fullName}
              onChangeText={handleInputChange("fullName")}
              placeholder={t(
                "personalDataRegistration.fullNamePlaceholder",
                "Insira o nome completo"
              )}
              icon={userIcon}
              error={validation.errors.fullName}
            />
            <InputField
              label={t("personalDataRegistration.cpf", "CPF")}
              value={validation.data.cpf}
              onChangeText={handleInputChange("cpf")}
              placeholder={t(
                "personalDataRegistration.cpfPlaceholder",
                "Insira o CPF"
              )}
              icon={cpfIcon}
              inputMode="numeric"
              maxLength={14} // XXX.XXX.XXX-XX
              error={validation.errors.cpf}
            />

            <View style={styles.inputGroup}>
              <Text style={styles.label}>
                {t("personalDataRegistration.birthDate", "Data de nascimento")}
              </Text>
              <TouchableOpacity
                style={[
                  styles.selectContainer,
                  validation.errors.birthDate && {borderColor: "#FF6B6B"}
                ]}
                onPress={openDatePicker}
              >
                {calendarIcon()}
                <Text
                  style={[
                    styles.selectText,
                    !validation.data.birthDate && styles.selectPlaceholder
                  ]}
                >
                  {validation.data.birthDate ||
                    t(
                      "personalDataRegistration.birthDatePlaceholder",
                      "Selecione a data de nascimento"
                    )}
                </Text>
                <ChevronDownIcon />
              </TouchableOpacity>
              {!!validation.errors.birthDate && (
                <Text style={styles.errorText}>
                  {validation.errors.birthDate}
                </Text>
              )}
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.label}>
                {t("personalDataRegistration.gender", "Sexo")}
              </Text>
              <TouchableOpacity
                style={styles.selectContainer}
                onPress={openGenderDrawer}
                activeOpacity={0.7}
              >
                <GenderIcon replaceColor="#F2F4F7" />
                <Text
                  style={[
                    styles.selectText,
                    !validation.data.gender && styles.selectPlaceholder
                  ]}
                >
                  {getSelectedGenderLabel() ||
                    t(
                      "personalDataRegistration.genderPlaceholder",
                      "Selecione um sexo"
                    )}
                </Text>
                <ChevronDownIcon />
              </TouchableOpacity>
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.label}>
                {t("personalDataRegistration.branch", "Filial escolhida")}
              </Text>
              <TouchableOpacity
                style={styles.selectContainer}
                onPress={openBranchDrawer}
                activeOpacity={0.7}
                disabled={isLoadingFranchises}
              >
                <BuildingIcon replaceColor="#F2F4F7" />
                <Text
                  style={[
                    styles.selectText,
                    !validation.data.branch && styles.selectPlaceholder
                  ]}
                >
                  {isLoadingFranchises
                    ? t(
                        "personalDataRegistration.loadingFranchises",
                        "Carregando filiais..."
                      )
                    : getSelectedBranchLabel() ||
                      t(
                        "personalDataRegistration.branchPlaceholder",
                        "Selecione uma filial"
                      )}
                </Text>
                <ChevronDownIcon />
              </TouchableOpacity>
              {franchisesError && (
                <Text style={styles.errorText}>
                  {t(
                    "personalDataRegistration.franchisesError",
                    "Erro ao carregar filiais"
                  )}
                </Text>
              )}
            </View>
          </View>

          <View style={styles.buttonContainer}>
            <View style={styles.backButton}>
              <FullSizeButton
                text={t("personalDataRegistration.back", "Voltar")}
                variant="secondary"
                onPress={handleBack}
              />
            </View>
            <View style={styles.nextButton}>
              <FullSizeButton
                text={t("personalDataRegistration.next", "Avançar")}
                onPress={handleNext}
              />
            </View>
          </View>
        </View>
      </ImageBackground>

      <BottomDrawer
        visible={drawerVisible}
        title={
          drawerType === "gender"
            ? t("personalDataRegistration.selectGender", "Selecionar sexo")
            : t("personalDataRegistration.selectBranch", "Selecionar filial")
        }
        options={drawerType === "gender" ? genderOptions : branchOptions}
        selectedValue={
          drawerType === "gender"
            ? validation.data.gender
            : validation.data.branch
        }
        onSelect={handleDrawerSelect}
        onClose={closeDrawer}
      />

      <DateTimePickerDrawer
        visible={showDatePicker}
        selectedDate={getSelectedDateObject()}
        onDateTimeSelect={handleDateSelect}
        onClose={closeDatePicker}
        title={t("personalDataRegistration.birthDate", "Data de nascimento")}
        hideTimeSection={true}
        allowPastDates={true}
      />
    </Screen>
  );
};

export default PersonalDataRegistration;
