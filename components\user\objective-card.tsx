import React from "react";
import {Text, View} from "react-native";
import styles from "@/styles/components/user/objective-card.style";
import ProgressBar from "@/components/progress-bar";
import SealSVG from "@/components/seals/seal-svg";
import {BadgeType} from "@/models/api/user-badges.models";

export interface ObjectiveCardProps {
  title: string;
  description: string;
  progress: number;
  accomplished: string;
  level?: number; // Novo prop para controlar o nível do badge
  badgeType?: BadgeType; // Novo prop para controlar o tipo do badge
}

const ObjectiveCard: React.FC<ObjectiveCardProps> = (props) => {
  const {level = 0, badgeType = BadgeType.DIAMOND} = props;

  return (
    <View style={styles.container}>
      <View
        style={{
          width: 40,
          height: 40,
          justifyContent: "center",
          alignItems: "center"
        }}
      >
        <SealSVG
          badgeType={badgeType}
          size={40}
          variant="square"
          level={level}
        />
      </View>
      <View style={styles.content}>
        <Text style={styles.title}>{props.title}</Text>
        <Text style={styles.description}>{props.description}</Text>
        <ProgressBar style={styles.progress} progress={props.progress} />
        <View style={styles.footer}>
          <Text style={styles.accomplished}>{props.accomplished}</Text>
        </View>
      </View>
    </View>
  );
};

export default ObjectiveCard;
