import React, {useState, useCallback} from "react";
import {Modal, View, Text} from "react-native";
import {useTranslation} from "react-i18next";
import FullSizeButton from "../full-size-button";
import InvisibleFullSizeButton from "../invisible-full-size-button";
import InputField from "../input-field";
import CheckCircleIcon from "../icons/check-circle-icon";
import UserIcon from "../icons/user-icon";
import LicenceThirdPartyIcon from "../icons/licence-third-party-icon";
import PhoneIcon from "../icons/phone-icon";
import SendIcon from "../icons/send-icon";
import {useCreateIndication} from "@/hooks/api/use-indications";
import {CreateIndicationSchema} from "@/models/api/indications.models";
import styles from "@/styles/modals/invite-friends-modal.style";
import {showErrorToast, showSuccessToast} from "@/utils/error-handler";
import Toast from "react-native-toast-message";
import {toastConfig} from "@/components/toast/custom-toast";

const UserIconComponent = () => <UserIcon />;
const CpfIconComponent = () => <LicenceThirdPartyIcon />;
const PhoneIconComponent = () => <PhoneIcon />;

export interface InviteFriendsModalProps {
  onClose: () => void;
}

const InviteFriendsModal: React.FC<InviteFriendsModalProps> = ({onClose}) => {
  const {t} = useTranslation();
  const [name, setName] = useState("");
  const [cpf, setCpf] = useState("");
  const [phone, setPhone] = useState("");
  const [showSuccess, setShowSuccess] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const createIndicationMutation = useCreateIndication();

  const validateForm = useCallback(() => {
    try {
      // Validate required fields first
      if (!name.trim()) {
        setErrors({name: "Nome é obrigatório"});
        return false;
      }

      if (!cpf.trim()) {
        setErrors({cpf: "CPF é obrigatório"});
        return false;
      }

      if (!validateCpf(cpf)) {
        setErrors({cpf: "CPF inválido"});
        return false;
      }

      if (!phone.trim()) {
        setErrors({phone: "Telefone é obrigatório"});
        return false;
      }

      // Create a more realistic email from CPF
      const cleanCpf = cpf.replace(/\D/g, "");
      const email = `indicacao.${cleanCpf}@clubm.app`;

      CreateIndicationSchema.parse({
        name: name.trim(),
        email: email,
        phoneNumber: phone.replace(/\D/g, ""),
        document: cleanCpf, // CPF como documento
        message: undefined
      });
      setErrors({});
      return true;
    } catch (error: any) {
      const newErrors: Record<string, string> = {};

      if (error.errors) {
        error.errors.forEach((err: any) => {
          const field = err.path[0];
          newErrors[field] = err.message;
        });
      }

      setErrors(newErrors);
      return false;
    }
  }, [name, cpf, phone]);

  const handleSendInvite = useCallback(async () => {
    if (!validateForm()) {
      return;
    }

    try {
      // Create a more realistic email from CPF
      const cleanCpf = cpf.replace(/\D/g, "");
      const email = `indicacao.${cleanCpf}@clubm.app`;

      const cleanCpfForRequest = cpf.replace(/\D/g, "");

      const result = await createIndicationMutation.mutateAsync({
        name: name.trim(),
        email: email,
        phoneNumber: phone.replace(/\D/g, ""),
        document: cleanCpfForRequest, // CPF como documento obrigatório
        message: undefined
      });

      if (result.success) {
        showSuccessToast(
          t("inviteFriends.success.title", "Sucesso!"),
          t(
            "inviteFriends.successMessage",
            "O convite foi enviado com sucesso!"
          )
        );
        setShowSuccess(true);
      } else {
        showErrorToast(
          new Error(result.message || "Erro"),
          t(
            "inviteFriends.error.generic",
            "Não foi possível enviar o convite. Tente novamente."
          )
        );
      }
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : t(
              "inviteFriends.error.generic",
              "Não foi possível enviar o convite. Tente novamente."
            );

      showErrorToast(new Error(errorMessage), errorMessage);
    }
  }, [validateForm, createIndicationMutation, name, cpf, phone, t]);

  const handleBack = useCallback(() => {
    if (showSuccess) {
      setShowSuccess(false);
      setName("");
      setCpf("");
      setPhone("");
      setErrors({});
    } else {
      onClose();
    }
  }, [showSuccess, onClose]);

  const formatPhone = (value: string) => {
    const numbers = value.replace(/\D/g, "");
    return numbers.replace(/(\d{2})(\d{5})(\d{4})/, "($1) $2-$3");
  };

  const formatCpf = (value: string) => {
    const numbers = value.replace(/\D/g, "");
    return numbers.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, "$1.$2.$3-$4");
  };

  const validateCpf = (cpf: string) => {
    const cleanCpf = cpf.replace(/\D/g, "");

    // Check if CPF has 11 digits
    if (cleanCpf.length !== 11) {
      return false;
    }

    // Check if all digits are the same
    if (/^(\d)\1{10}$/.test(cleanCpf)) {
      return false;
    }

    // Calculate first verification digit
    let sum = 0;
    for (let i = 0; i < 9; i++) {
      sum += parseInt(cleanCpf.charAt(i)) * (10 - i);
    }
    let remainder = (sum * 10) % 11;
    if (remainder === 10 || remainder === 11) remainder = 0;
    if (remainder !== parseInt(cleanCpf.charAt(9))) return false;

    // Calculate second verification digit
    sum = 0;
    for (let i = 0; i < 10; i++) {
      sum += parseInt(cleanCpf.charAt(i)) * (11 - i);
    }
    remainder = (sum * 10) % 11;
    if (remainder === 10 || remainder === 11) remainder = 0;
    if (remainder !== parseInt(cleanCpf.charAt(10))) return false;

    return true;
  };

  const handlePhoneChange = useCallback((value: string) => {
    const formatted = formatPhone(value);
    setPhone(formatted);
  }, []);

  const handleCpfChange = useCallback((value: string) => {
    const formatted = formatCpf(value);
    setCpf(formatted);
  }, []);
  return (
    <Modal visible={true} animationType="slide" presentationStyle="pageSheet">
      <View style={styles.container}>
        <View style={styles.header}>
          <View style={styles.headerIndicator} />
          <Text style={styles.headerTitle}>
            {t("inviteFriends.title", "Convidar amigos")}
          </Text>
        </View>

        {showSuccess && (
          <View style={styles.successContainer}>
            <CheckCircleIcon width={24} height={24} />
            <Text style={styles.successText}>
              {t(
                "inviteFriends.successMessage",
                "O convite foi enviado com sucesso!"
              )}
            </Text>
          </View>
        )}

        <View style={styles.content}>
          <Text style={styles.description}>
            {t(
              "inviteFriends.description",
              "Convide amigos para o clube para aumentar seu network no clube e ganhe prêmios!"
            )}
          </Text>

          <View style={styles.formContainer}>
            <InputField
              placeholder={t(
                "inviteFriends.namePlaceholder",
                "Insira o nome completo do indicado"
              )}
              value={name}
              onChangeText={setName}
              error={errors.name}
              icon={UserIconComponent}
            />

            <InputField
              placeholder={t(
                "inviteFriends.cpfPlaceholder",
                "Insira o CPF do indicado"
              )}
              value={cpf}
              onChangeText={handleCpfChange}
              error={errors.cpf}
              icon={CpfIconComponent}
              maxLength={14}
            />

            <InputField
              placeholder={t(
                "inviteFriends.phonePlaceholder",
                "Insira o telefone do indicado"
              )}
              value={phone}
              onChangeText={handlePhoneChange}
              error={errors.phone}
              inputMode="tel"
              maxLength={15}
              icon={PhoneIconComponent}
            />
          </View>
        </View>

        <View style={styles.buttonContainer}>
          <FullSizeButton
            text={t("inviteFriends.sendButton", "Enviar convite")}
            onPress={handleSendInvite}
            disabled={createIndicationMutation.isPending}
            loading={createIndicationMutation.isPending}
            icon={<SendIcon />}
          />
          <InvisibleFullSizeButton
            text={t("inviteFriends.backButton", "Voltar")}
            onPress={handleBack}
            disabled={createIndicationMutation.isPending}
          />
        </View>
      </View>
      <Toast config={toastConfig} />
    </Modal>
  );
};

export default InviteFriendsModal;
