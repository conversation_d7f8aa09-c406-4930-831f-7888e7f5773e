import React from "react";
import {
  StyleProp,
  TextInput,
  TouchableOpacity,
  View,
  ViewStyle
} from "react-native";
import {InputModeOptions} from "react-native/Libraries/Components/TextInput/TextInput";

export interface PaymentInputFieldProps {
  placeholder?: string;
  value: string;
  onChangeText: (value: string) => void;
  style?: StyleProp<ViewStyle>;
  icon?: React.ReactNode | React.JSX.Element;
  rightIcon?: React.ReactNode | React.JSX.Element;
  maxLength?: number;
  inputMode?: InputModeOptions;
  onPress?: () => void;
  editable?: boolean;
}

const PaymentInputField: React.FC<PaymentInputFieldProps> = (props) => {
  const isDisabled = props.editable === false;

  const containerStyle = {
    shadowColor: "rgba(16, 24, 40, 0.05)",
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 1,
    shadowRadius: 2,
    borderWidth: 1,
    borderColor: "#F2F4F7",
    borderRadius: 8,
    flexDirection: "row" as const,
    alignItems: "center" as const,
    gap: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    minHeight: 48
  } as const;

  const disabledContainerStyle = {
    opacity: 0.6,
    borderColor: "#3A3A3A" // subtle darker border to indicate disabled
  } as const;

  const inputStyle = {
    color: "#F2F4F7",
    fontFamily: "Open Sans",
    fontSize: 14,
    fontWeight: "400" as const,
    lineHeight: 20,
    flex: 1,
    paddingVertical: 0
  } as const;

  const content = (
    <View
      style={[
        containerStyle,
        props.style,
        isDisabled ? disabledContainerStyle : undefined
      ]}
    >
      {props.icon}
      <TextInput
        placeholder={props.placeholder}
        placeholderTextColor="#F2F4F7"
        style={inputStyle}
        value={props.value}
        onChangeText={props.onChangeText}
        autoCapitalize="none"
        autoCorrect={false}
        maxLength={props.maxLength}
        inputMode={props.inputMode}
        editable={!isDisabled}
      />
      {props.rightIcon}
    </View>
  );

  if (props.onPress) {
    return (
      <TouchableOpacity onPress={props.onPress} activeOpacity={0.7}>
        {content}
      </TouchableOpacity>
    );
  }

  return content;
};

export default PaymentInputField;
