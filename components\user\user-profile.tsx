import React, {useState} from "react";
import {View, Text, ScrollView, TouchableOpacity, Alert} from "react-native";
import {useTranslation} from "react-i18next";
import {useCurrentUser, useUpdateProfile} from "@/hooks/api/use-users";
import {useGuestUser} from "@/contexts/guest-user-context";

import styles from "@/styles/components/user/user-profile.style";
import Button from "@/components/button";
import UserStats from "./user-stats";
import UserSocialMedia from "./user-social-media";
import ProfileField from "./profile-field";
import UserProfileEdit from "./user-profile-edit";

// Icons (you may need to create these or import from existing icon components)
import EditIcon from "@/components/icons/edit-icon";
import EmailIcon from "@/components/icons/email-icon";
import PhoneIcon from "@/components/icons/phone-icon";
import CompanyIcon from "@/components/icons/company-icon";
import CalendarIcon from "@/components/icons/calendar-icon";

const UserProfile: React.FC = () => {
  const {t} = useTranslation();
  const [isEditing, setIsEditing] = useState(false);
  const {isGuest} = useGuestUser();

  const {data: user, isLoading, error} = useCurrentUser();
  const updateProfileMutation = useUpdateProfile();

  const handleEditProfile = () => {
    setIsEditing(true);
  };

  const handleSaveProfile = async (updatedData: any) => {
    try {
      await updateProfileMutation.mutateAsync(updatedData);
      setIsEditing(false);
      Alert.alert(t("user.profile.success"), t("user.profile.updateSuccess"));
    } catch (error) {
      Alert.alert(t("user.profile.error"), t("user.profile.updateError"));
    }
  };

  const handleCancelEdit = () => {
    setIsEditing(false);
  };

  const formatMemberSince = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("pt-BR", {
      year: "numeric",
      month: "long"
    });
  };

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <Text style={styles.loadingText}>{t("common.loading")}</Text>
      </View>
    );
  }

  if (error || !user) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>{t("user.profile.loadError")}</Text>
      </View>
    );
  }

  if (isEditing) {
    return (
      <UserProfileEdit
        user={user}
        onSave={handleSaveProfile}
        onCancel={handleCancelEdit}
        isLoading={updateProfileMutation.isPending}
      />
    );
  }

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {/* Profile Header */}
      <View style={styles.profileHeader}>
        <View style={styles.profileHeaderContent}>
          <Text style={styles.profileTitle}>{t("user.profile.title")}</Text>
          {/* Only show edit button for registered users, not for guest users */}
          {!isGuest && (
            <TouchableOpacity
              style={styles.editButton}
              onPress={handleEditProfile}
              disabled={updateProfileMutation.isPending}
            >
              <EditIcon />
              <Text style={styles.editButtonText}>
                {t("user.profile.edit")}
              </Text>
            </TouchableOpacity>
          )}
        </View>
      </View>

      {/* Basic Information */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>{t("user.profile.basicInfo")}</Text>

        <ProfileField
          label={t("user.profile.name")}
          value={user.name || t("user.profile.notInformed")}
          icon={<EditIcon />}
        />

        <ProfileField
          label={t("user.profile.email")}
          value={user.email || t("user.profile.notInformed")}
          icon={<EmailIcon />}
        />

        <ProfileField
          label={t("user.profile.phone")}
          value={user.phoneNumber || t("user.profile.notInformed")}
          icon={<PhoneIcon />}
        />

        {user.companyName && (
          <ProfileField
            label={t("user.profile.company")}
            value={user.companyName}
            icon={<CompanyIcon />}
          />
        )}

        <ProfileField
          label={t("user.profile.memberSince")}
          value={formatMemberSince(user.createdAt || new Date().toISOString())}
          icon={<CalendarIcon />}
        />
      </View>

      {/* Social Media */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>{t("user.profile.socialMedia")}</Text>
        <UserSocialMedia
          socialMedias={user.socialMedias || []}
          editable={false}
        />
      </View>

      {/* User Statistics */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>{t("user.profile.statistics")}</Text>
        <UserStats
          stats={{
            eventsAttended: 0,
            eventsRegistered: 0,
            totalPurchases: 0,
            totalSpent: 0,
            memberSince: user.createdAt || new Date().toISOString(),
            lastActivity: new Date().toISOString()
          }}
        />
      </View>

      {/* Account Actions - Only show for registered users, not for guest users */}
      {!isGuest && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>
            {t("user.profile.accountActions")}
          </Text>

          <Button
            text={t("user.profile.changePassword")}
            style={styles.actionButton}
            onPress={() => {
              // Navigate to change password screen
              // This would typically use navigation
            }}
          />

          <Button
            text={t("user.profile.notificationSettings")}
            style={styles.actionButton}
            onPress={() => {
              // Navigate to notification settings
            }}
          />

          <Button
            text={t("user.profile.privacySettings")}
            style={styles.actionButton}
            onPress={() => {
              // Navigate to privacy settings
            }}
          />
        </View>
      )}
    </ScrollView>
  );
};

export default UserProfile;
