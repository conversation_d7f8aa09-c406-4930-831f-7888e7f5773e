import {StyleSheet} from "react-native";

const styles = StyleSheet.create({
  container: {
    width: 196,
    height: 360, // Increased height to make card bigger vertically
    borderRadius: 8,
    flexDirection: "row",
    alignItems: "stretch",
    paddingBottom: 64,
    alignSelf: "center", // Center the card horizontally
    // Sombra para dar profundidade ao cartão
    shadowColor: "#101828",
    shadowOffset: {
      width: 0,
      height: 2
    },
    shadowOpacity: 0.06,
    shadowRadius: 4,
    elevation: 4 // Para Android
  },

  logoSection: {
    width: 39,
    backgroundColor: "#055227", // Verde do ClubM
    borderTopLeftRadius: 8,
    borderBottomLeftRadius: 8,
    paddingTop: 12,
    paddingHorizontal: 2,
    alignItems: "center",
    justifyContent: "flex-start",
    minHeight: 296 // Increased to match new container height
  },

  clubLogo: {
    width: 34.87,
    height: 53
  },

  cardSection: {
    flex: 1,
    position: "relative",
    borderTopRightRadius: 8,
    borderBottomRightRadius: 8,
    overflow: "hidden"
  },

  cardBackground: {
    position: "absolute",
    top: -40, // Start from the very top
    left: 0,
    right: 0,
    bottom: 0,
    width: "100%", // Use full width instead of fixed 157
    height: "100%" // Use full height to fill all available space
  },

  userInfoOverlay: {
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: "#055227", // Verde do ClubM para o overlay
    paddingHorizontal: 12,
    paddingTop: 12,
    paddingBottom: 8,
    gap: 3
  },

  userInfoContainer: {
    width: "100%",
    minHeight: 40,
    justifyContent: "flex-start"
  },

  userName: {
    color: "#DFE9F0", // Cor clara para contraste
    fontSize: 14,
    lineHeight: 20,
    fontWeight: "400",
    textAlign: "left",
    maxWidth: 119 // Baseado no design do Motiff
  },

  membershipContainer: {
    width: "100%",
    minHeight: 18,
    justifyContent: "flex-start"
  },

  membershipType: {
    color: "#DFE9F0", // Cor clara para contraste
    fontSize: 10,
    lineHeight: 18,
    fontWeight: "400",
    textAlign: "left",
    maxWidth: 103 // Baseado no design do Motiff
  },

  // Estados de loading e erro
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: 12,
    gap: 8
  },

  loadingText: {
    color: "#DFE9F0",
    fontSize: 12,
    textAlign: "center"
  },

  errorContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: 12
  },

  errorText: {
    color: "#DFE9F0",
    fontSize: 12,
    textAlign: "center",
    lineHeight: 16
  }
});

export default styles;
