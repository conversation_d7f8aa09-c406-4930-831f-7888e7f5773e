import {UnknownOutputParams, useLocalSearchParams} from "expo-router";
import React, {useMemo, useState} from "react";
import {Text, View} from "react-native";
import ScreenWithHeader from "@/components/screen-with-header";
import {Image} from "expo-image";
import BookIcon from "@/components/icons/book-icon";
import AcquireButton from "@/components/product-page/acquire-button";
import Pill from "@/components/pill";
import DetailsCard from "@/components/product-page/details-card";
import PaymentMethods from "@/components/product-page/payment-methods";
import SimilarProducts from "@/components/product-page/similar-products";
import {useUpsellDrawer} from "@/hooks/use-upsell-drawer";
import UpsellDrawer from "@/components/modals/upsell-drawer";
import styles from "@/styles/logged-stack/product-page.style";
import {useTranslation} from "react-i18next";
import useProduct from "@/hooks/use-product";

export interface ProductPageParams extends UnknownOutputParams {
  id: string;
}

const ProductPage: React.FC = () => {
  const {t} = useTranslation();
  const [isExpanded, setIsExpanded] = useState(false);
  const params = useLocalSearchParams<ProductPageParams>();
  const {productQuery} = useProduct(params.id);
  const {
    isVisible: isUpsellDrawerVisible,
    config: upsellConfig,
    hideUpsellDrawer,
    interceptGuestAction
  } = useUpsellDrawer();

  // Debug logs
  console.log("🎯 ProductPage: Product ID recebido:", params.id);
  console.log(
    "🎯 ProductPage: Hook useProduct - isLoading:",
    productQuery.isLoading,
    "error:",
    productQuery.error,
    "productData:",
    productQuery.data
  );

  const truncatedDescription = useMemo(() => {
    const product = productQuery.data;
    const description = product?.description || "";
    return description.length > 108
      ? description.substring(0, 108) + "..."
      : description;
  }, [productQuery.data]);

  const handleSeeMore = () => {
    setIsExpanded(!isExpanded);
  };

  // Loading state
  if (productQuery.isLoading) {
    return (
      <ScreenWithHeader
        screenTitle="E-Book"
        backButton={true}
        disablePadding={true}
      >
        <View
          style={[
            styles.mainContainer,
            {justifyContent: "center", alignItems: "center"}
          ]}
        >
          <Text style={{color: "#666", textAlign: "center"}}>
            {t("productPage.loading", "Carregando produto...")}
          </Text>
        </View>
      </ScreenWithHeader>
    );
  }

  // Error state
  if (productQuery.error || !productQuery.data) {
    console.log(
      `🚨 ProductPage: Error state - ID: ${params.id}, Error:`,
      productQuery.error
    );

    return (
      <ScreenWithHeader
        screenTitle="E-Book"
        backButton={true}
        disablePadding={true}
      >
        <View
          style={[
            styles.mainContainer,
            {justifyContent: "center", alignItems: "center", padding: 20}
          ]}
        >
          <Text
            style={{
              color: "#ff0000",
              textAlign: "center",
              marginBottom: 16,
              fontSize: 16,
              fontWeight: "bold"
            }}
          >
            {t("productPage.error", "Produto não encontrado")}
          </Text>
          <Text
            style={{
              color: "#666",
              textAlign: "center",
              marginBottom: 20,
              fontSize: 14
            }}
          >
            O produto com ID {params.id} não está disponível no momento. Isso
            pode ser devido a um problema temporário na API.
          </Text>
          <Text
            style={{
              color: "#007AFF",
              textAlign: "center",
              fontSize: 16,
              textDecorationLine: "underline"
            }}
            onPress={() => productQuery.refetch()}
          >
            {t("productPage.retry", "Tentar novamente")}
          </Text>
          <Text
            style={{
              color: "#999",
              textAlign: "center",
              marginTop: 20,
              fontSize: 12
            }}
          >
            Debug: Tentando carregar produto ID {params.id}
          </Text>
        </View>
      </ScreenWithHeader>
    );
  }

  return (
    <ScreenWithHeader
      screenTitle="E-Book"
      backButton={true}
      disablePadding={true}
    >
      <Image
        style={styles.backgroundImage}
        source={require("../../assets/textures/rotated-pattern.png")}
      />

      <View style={styles.mainContainer}>
        <View style={styles.iconContainer}>
          <View style={styles.iconWrapper}>
            <BookIcon width={40} height={40} />
          </View>
        </View>

        <View style={styles.contentContainer}>
          <View style={styles.productInfoContainer}>
            <Text style={styles.productTitle}>{productQuery.data?.name}</Text>
            <View style={styles.descriptionContainer}>
              <Text style={styles.descriptionText}>
                {isExpanded
                  ? productQuery.data?.description
                  : truncatedDescription}
                <Text onPress={handleSeeMore} style={styles.seeMoreText}>
                  {isExpanded
                    ? ` ${t("productPage.seeLess")}`
                    : ` ${t("productPage.seeMore")}`}
                </Text>
              </Text>
            </View>

            <View style={styles.acquire}>
              <AcquireButton
                productId={productQuery.data?.id || 0}
                productName={productQuery.data?.name || ""}
                productDescription={productQuery.data?.description || ""}
                tag={
                  <Pill
                    text="ENCONTRO VIP"
                    textColor="#B93815"
                    backgroundColor="#FEF6EE"
                  />
                }
                price={productQuery.data?.value}
                disabled={!productQuery.data?.value}
                interceptGuestAction={interceptGuestAction}
              />
            </View>
          </View>

          <DetailsCard product={productQuery.data} hideWhereToUse={true} />
        </View>
        <View style={styles.bottomSection}>
          <PaymentMethods product={productQuery.data} />
          <SimilarProducts
            currentProductId={productQuery.data?.id}
            categoryId={productQuery.data?.category}
          />
        </View>
      </View>

      {/* Upsell Drawer for Guest Users */}
      <UpsellDrawer
        visible={isUpsellDrawerVisible}
        onClose={hideUpsellDrawer}
        title={upsellConfig.title}
        description={upsellConfig.description}
      />
    </ScreenWithHeader>
  );
};

export default ProductPage;
