import React, {useMemo, useState} from "react";
import {Text, View, ScrollView} from "react-native";
import {useLocalSearchParams, useRouter} from "expo-router";
import {useTranslation} from "react-i18next";
import Toast from "react-native-toast-message";
import ScreenWithHeader from "@/components/screen-with-header";
import {Image} from "expo-image";
import AnnounceIcon from "@/components/icons/announce-icon";
import EventAcquireButton from "@/components/event-page/event-acquire-button";
import Pill from "@/components/pill";
import EventDetailsCard from "@/components/event-page/event-details-card";
import EventPaymentMethods from "@/components/event-page/event-payment-methods";
import SimilarEvents from "@/components/event-page/similar-events";
import {useUpsellDrawer} from "@/hooks/use-upsell-drawer";
import UpsellDrawer from "@/components/modals/upsell-drawer";
import styles from "@/styles/events/event-sale.style";
import {useEvent} from "../../hooks/api/use-events";
import {useCreatePayment} from "../../hooks/api/use-payments";
import {PaymentEntity, PaymentType} from "../../models/api/payments.models";

export interface EventSaleParams {
  id: string;
}

const EventSale: React.FC = () => {
  const {t} = useTranslation();
  const router = useRouter();
  const [isExpanded, setIsExpanded] = useState(false);
  const params = useLocalSearchParams() as unknown as EventSaleParams;
  const eventId = Array.isArray(params.id) ? params.id[0] : params.id;
  const {
    isVisible: isUpsellDrawerVisible,
    config: upsellConfig,
    interceptGuestAction,
    hideUpsellDrawer
  } = useUpsellDrawer();

  // Buscar dados do evento via API
  const {
    data: eventData,
    isLoading,
    error,
    refetch
  } = useEvent(eventId || "", {
    enabled: !!eventId
  } as any);

  const createPaymentMutation = useCreatePayment();

  const handlePaymentSuccess = (response: any) => {
    if (isEventFree) {
      Toast.show({
        type: "success",
        text1: t("eventSale.registration.completed", "Inscrição concluída"),
        text2: t(
          "eventSale.registration.completedMessage",
          "Sua inscrição foi realizada com sucesso!"
        ),
        position: "top",
        topOffset: 60,
        visibilityTime: 4000
      });
      router.back();
    } else {
      router.push({
        pathname: "/(logged-stack)/payment-confirmation",
        params: {
          paymentId: response.id
        }
      });
    }
  };

  const handlePaymentError = (error: any) => {
    console.error("Payment error:", error);
    Toast.show({
      type: "error",
      text1: t("eventSale.registration.error", "Erro"),
      text2: t(
        "eventSale.registration.errorMessage",
        "Erro ao realizar inscrição. Tente novamente."
      ),
      position: "top",
      topOffset: 60,
      visibilityTime: 4000
    });
  };

  const isEventFree = useMemo(() => {
    if (!eventData) return false;
    return (
      eventData.price?.isFree ||
      eventData.price?.amount === 0 ||
      (eventData as any).value === 0 ||
      (!eventData.price?.amount && !(eventData as any).value)
    );
  }, [eventData]);

  const truncatedDescription = useMemo(() => {
    const event = eventData;
    const description = event?.description || "";
    return description.length > 108
      ? description.substring(0, 108) + "..."
      : description;
  }, [eventData]);

  const handleSeeMore = () => {
    setIsExpanded(!isExpanded);
  };

  const handleAcquire = () => {
    if (!eventData) {
      Toast.show({
        type: "error",
        text1: "Erro",
        text2: "Dados do evento não disponíveis",
        position: "top",
        topOffset: 60,
        visibilityTime: 3000
      });
      return;
    }

    // Interceptar ação para usuários guest (upsell)
    const wasIntercepted = interceptGuestAction(
      () => {
        if (isEventFree) {
          createPaymentMutation.mutate(
            {
              entity: PaymentEntity.Event,
              entityId: parseInt(eventId),
              type: PaymentType.Free,
              termAccepted: true
            },
            {
              onSuccess: handlePaymentSuccess,
              onError: handlePaymentError
            }
          );
        } else {
          let eventAmount = 0;
          if (eventData.price?.amount) {
            eventAmount = eventData.price.amount / 100;
          } else if ((eventData as any).value) {
            eventAmount = (eventData as any).value / 100;
          }

          const paymentParams = {
            entityId: eventId, // Usando entityId correto para eventos
            entity: PaymentEntity.Event.toString(),
            entityTitle: eventData.title || (eventData as any).name || "Evento",
            entityDescription: eventData.description || "",
            entityImageUrl: eventData.images?.[0]?.url || "",
            amount: eventAmount.toString()
          };

          router.push({
            pathname: "/(logged-stack)/payment",
            params: paymentParams
          });
        }
      },
      {
        title: "Quase lá!",
        description:
          "Faça seu cadastro e torne-se membro para adquirir esse evento e outros diversos benefícios no aplicativo."
      }
    );

    if (wasIntercepted) {
      console.log(
        "🚫 [EVENT-SALE] Compra de evento interceptada para usuário guest"
      );
    }
  };

  if (isLoading) {
    return (
      <ScreenWithHeader
        screenTitle="Evento"
        backButton={true}
        disablePadding={true}
      >
        <View
          style={[
            styles.mainContainer,
            {justifyContent: "center", alignItems: "center"}
          ]}
        >
          <Text style={{color: "#666", textAlign: "center"}}>
            {t("eventSale.loading", "Carregando evento...")}
          </Text>
        </View>
      </ScreenWithHeader>
    );
  }

  if (error || !eventData) {
    return (
      <ScreenWithHeader
        screenTitle="Evento"
        backButton={true}
        disablePadding={true}
      >
        <View
          style={[
            styles.mainContainer,
            {justifyContent: "center", alignItems: "center"}
          ]}
        >
          <Text
            style={{color: "#ff0000", textAlign: "center", marginBottom: 16}}
          >
            {t("eventSale.error", "Erro ao carregar evento")}
          </Text>
          <Text
            style={{color: "#007AFF", textAlign: "center"}}
            onPress={() => refetch()}
          >
            {t("eventSale.retry", "Tentar novamente")}
          </Text>
        </View>
      </ScreenWithHeader>
    );
  }

  return (
    <ScreenWithHeader
      screenTitle="Evento"
      backButton={true}
      disablePadding={true}
      disableScrollView={true}
    >
      <ScrollView
        style={styles.mainContainer}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled"
        nestedScrollEnabled={true}
      >
        <Image
          style={styles.backgroundImage}
          source={require("../../assets/textures/rotated-pattern.png")}
        />
        <View style={styles.iconContainer}>
          <View style={styles.iconWrapper}>
            <AnnounceIcon width={40} height={40} />
          </View>
        </View>

        <View style={styles.contentContainer}>
          <View style={styles.eventInfoContainer}>
            <Text style={styles.eventTitle}>
              {eventData?.title || (eventData as any)?.name}
            </Text>
            <View style={styles.descriptionContainer}>
              <Text style={styles.descriptionText}>
                {isExpanded ? eventData?.description : truncatedDescription}
                <Text onPress={handleSeeMore} style={styles.seeMoreText}>
                  {isExpanded
                    ? ` ${t("eventSale.seeLess", "Ver menos")}`
                    : ` ${t("eventSale.seeMore", "Ver mais")}`}
                </Text>
              </Text>
            </View>

            <View style={styles.acquire}>
              <EventAcquireButton
                tag={
                  eventData?.category?.name ? (
                    <Pill
                      text={eventData.category.name.toUpperCase()}
                      textColor="#B93815"
                      backgroundColor="#FEF6EE"
                    />
                  ) : null
                }
                price={
                  eventData?.price ||
                  ((eventData as any)?.value
                    ? {
                        amount: (eventData as any).value,
                        currency: "BRL",
                        isFree: (eventData as any).value === 0
                      }
                    : null)
                }
                onPress={handleAcquire}
                loading={createPaymentMutation.isPending}
                disabled={createPaymentMutation.isPending}
              />
            </View>
          </View>

          <EventDetailsCard event={eventData} />
        </View>
        <View style={styles.bottomSection}>
          <EventPaymentMethods />
          <SimilarEvents currentEventId={eventId} />
        </View>
      </ScrollView>

      {/* Upsell Drawer for Guest Users */}
      <UpsellDrawer
        visible={isUpsellDrawerVisible}
        onClose={hideUpsellDrawer}
        title={upsellConfig.title}
        description={upsellConfig.description}
      />
    </ScreenWithHeader>
  );
};

export default EventSale;
