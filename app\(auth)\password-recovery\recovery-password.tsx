import React, {useCallback, useState} from "react";
import BackgroundLogoTexture from "@/components/logos/background-logo-texture";
import Screen from "@/components/screen";
import {ColorValue, Text, TouchableOpacity, View} from "react-native";
import styles from "@/styles/auth/password-recovery/recovery-password.style";
import BackButton from "@/components/back-button";
import {useTranslation} from "react-i18next";
import RecoveryDigits from "@/components/recovery-password/recovery-digits";
import FullSizeButton from "@/components/full-size-button";
import InputField from "@/components/input-field";
import PasswordIcon from "@/components/icons/password-icon";
import {RecoveryPassword as RecoveryPasswordModel} from "@/models/login";
import {useLocalSearchParams} from "expo-router";
import {UnknownOutputParams} from "expo-router/build/types";
import useForgetPassword from "@/hooks/use-forget-password";
import {ErrorType, useErrorMessage} from "@/contexts/error-dialog-context";

export interface RecoveryPasswordParams extends UnknownOutputParams {
  document: string;
}

const RecoveryPassword: React.FC = () => {
  const params = useLocalSearchParams<RecoveryPasswordParams>();
  const forgetPasswordAction = useForgetPassword();
  const errorAction = useErrorMessage();
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});
  const [recoveryCode, setRecoveryCode] = useState<string>("");
  const [recoveryStep, setRecoveryStep] = useState<number>(1);
  const [resetPasswordForm, setResetPasswordForm] =
    useState<RecoveryPasswordModel>({
      password: "",
      repeatPassword: ""
    });

  const {t} = useTranslation();

  const onRecoveryChange = useCallback((value: string) => {
    setRecoveryCode(value);
  }, []);

  const nextStep = useCallback(() => {
    if (recoveryStep == 1) {
      if (recoveryCode.length != 6) {
        errorAction.emitError({
          errorType: ErrorType.Error,
          title: t("errors.authenticationError"),
          description: t("errors.authenticationErrorDescription")
        });
        return;
      }
      setRecoveryStep((x) => x + 1);
    } else {
      if (resetPasswordForm.password != resetPasswordForm.repeatPassword) {
        setFormErrors({
          repeatPassword: t("errors.passwordNotMatch")
        });
        return;
      }

      setFormErrors({});
      forgetPasswordAction.resetPassword({
        code: recoveryCode,
        password: resetPasswordForm.password
      });
    }
  }, [
    recoveryStep,
    forgetPasswordAction.resetPassword,
    params.document,
    recoveryCode,
    resetPasswordForm.password,
    errorAction.emitError,
    t
  ]);

  const onResetPasswordFormChange = useCallback(
    (field: keyof RecoveryPasswordModel) => (value: string) => {
      setResetPasswordForm((x) => ({...x, [field]: value}));
    },
    []
  );

  const backStep = useCallback(() => {
    setRecoveryStep((x) => x - 1);
  }, []);

  const passwordIcon = useCallback(
    (errorColor?: ColorValue) => <PasswordIcon replaceColor={errorColor} />,
    []
  );

  return (
    <>
      <BackgroundLogoTexture gray={true} />
      <Screen>
        <View style={styles.container}>
          <View style={styles.contentContainer}>
            <BackButton
              style={[styles.backButtonMargin, styles.backButtonCentered]}
              onPress={recoveryStep == 2 ? backStep : undefined}
            />
            <Text style={[styles.text, styles.titleText]}>
              {t("recoveryPassword.title")}
            </Text>
            {recoveryStep == 1 ? (
              <View style={styles.subScreenContainer}>
                <Text style={[styles.text, styles.descriptionText]}>
                  {t("recoveryPassword.description")}
                </Text>
                <Text style={[styles.text, styles.securityCodeText]}>
                  {t("recoveryPassword.securityCode")}
                </Text>
                <RecoveryDigits
                  value={recoveryCode}
                  onChange={onRecoveryChange}
                />
              </View>
            ) : (
              <View style={styles.subScreenContainer}>
                <Text style={[styles.text, styles.descriptionText]}>
                  {t("recoveryPassword.secondStepDescription")}
                </Text>
                <InputField
                  label={t("recoveryPassword.newPassword")}
                  isPassword={true}
                  style={[styles.inputNewPassword, styles.squareInputContainer]}
                  value={resetPasswordForm.password}
                  icon={passwordIcon}
                  error={forgetPasswordAction.errors["password"]}
                  placeholder={t("recoveryPassword.newPasswordPlaceholder")}
                  onChangeText={onResetPasswordFormChange("password")}
                  height={56}
                />
                <InputField
                  label={t("recoveryPassword.repeatNewPassword")}
                  isPassword={true}
                  error={formErrors["repeatPassword"]}
                  style={[
                    styles.repeatPasswordInput,
                    styles.squareInputContainer
                  ]}
                  value={resetPasswordForm.repeatPassword}
                  icon={passwordIcon}
                  placeholder={t(
                    "recoveryPassword.repeatNewPasswordPlaceholder"
                  )}
                  onChangeText={onResetPasswordFormChange("repeatPassword")}
                  height={56}
                />
                <Text style={styles.text}>
                  {t("recoveryPassword.passwordRequisition")}
                </Text>
              </View>
            )}
          </View>

          <View style={styles.footerContainer}>
            {recoveryStep == 1 && (
              <View style={styles.retrySendCode}>
                <Text style={styles.text}>
                  {t("recoveryPassword.retrySendCodeLabel")}
                </Text>
                <TouchableOpacity>
                  <Text style={[styles.text, styles.retrySendButton]}>
                    {t("recoveryPassword.retrySendCodeButton")}
                  </Text>
                </TouchableOpacity>
              </View>
            )}
            <FullSizeButton
              text={
                recoveryStep == 1
                  ? "recoveryPassword.confirmButton"
                  : "recoveryPassword.confirmNewPasswordButton"
              }
              onPress={nextStep}
            />
          </View>
        </View>
      </Screen>
    </>
  );
};

export default RecoveryPassword;
