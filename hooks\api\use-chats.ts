/**
 * Hooks React Query para Chat/Mensagens
 * Implementa cache, invalidação e otimizações para chat e mensagens
 */

import {
  useQuery,
  useMutation,
  useQueryClient,
  useInfiniteQuery,
  UseQueryOptions,
  UseMutationOptions,
  UseInfiniteQueryOptions
} from "@tanstack/react-query";
import {
  Chat,
  Message,
  ChatMember,
  ChatsListParams,
  MessagesListParams,
  CreateChatRequest,
  UpdateChatRequest,
  SendMessageRequest,
  UpdateMessageRequest,
  AddChatMemberRequest,
  FileUploadRequest,
  FileUploadResponse,
  CallRequest,
  CallResponse,
  ChatStats
} from "@/models/api/chats.models";
import {PaginationResponse} from "@/models/api/common.models";
import ChatsService from "@/services/api/chats/chats.service";
import {BaseApiError} from "@/services/api/base/api-errors";
import {useGuestUser} from "@/contexts/guest-user-context";

// Chaves de query para cache
export const chatsKeys = {
  all: ["chats"] as const,
  lists: () => [...chatsKeys.all, "list"] as const,
  list: (params?: ChatsListParams) => [...chatsKeys.lists(), params] as const,
  details: () => [...chatsKeys.all, "detail"] as const,
  detail: (id: string) => [...chatsKeys.details(), id] as const,
  messages: (chatId: string) => [...chatsKeys.all, "messages", chatId] as const,
  messagesList: (chatId: string, params?: MessagesListParams) =>
    [...chatsKeys.messages(chatId), "list", params] as const,
  members: (chatId: string) => [...chatsKeys.all, "members", chatId] as const,
  stats: () => [...chatsKeys.all, "stats"] as const,
  unread: () => [...chatsKeys.all, "unread"] as const
};

/**
 * Hook para buscar lista de chats
 */
export const useChats = (
  params?: ChatsListParams,
  options?: UseQueryOptions<PaginationResponse<Chat>, BaseApiError>
) => {
  const {isGuest, isLoading: guestLoading} = useGuestUser();
  const isEnabled = options?.enabled !== false && !isGuest && !guestLoading;

  return useQuery({
    queryKey: chatsKeys.list(params),
    queryFn: () => ChatsService.getChats(params),
    staleTime: 2 * 60 * 1000, // 2 minutos
    gcTime: 5 * 60 * 1000, // 5 minutos
    enabled: isEnabled,
    refetchOnMount: isEnabled,
    refetchOnWindowFocus: isEnabled,
    refetchOnReconnect: isEnabled,
    ...options
  });
};

/**
 * Hook para buscar chats com scroll infinito
 */
export const useInfiniteChats = (
  params?: Omit<ChatsListParams, "page">,
  pageSize: number = 20,
  options?: UseInfiniteQueryOptions<PaginationResponse<Chat>, BaseApiError>
) => {
  return useInfiniteQuery({
    queryKey: chatsKeys.list({...params, pageSize}),
    queryFn: ({pageParam = 1}) =>
      ChatsService.getChats({...params, page: pageParam as number, pageSize}),
    initialPageParam: 1,
    getNextPageParam: (lastPage) => {
      if (lastPage.pagination.hasNextPage) {
        return lastPage.pagination.currentPage + 1;
      }
      return undefined;
    },
    staleTime: 2 * 60 * 1000,
    gcTime: 5 * 60 * 1000,
    ...options
  });
};

/**
 * Hook para buscar chat específico
 */
export const useChat = (
  id: string,
  options?: UseQueryOptions<Chat, BaseApiError>
) => {
  return useQuery({
    queryKey: chatsKeys.detail(id),
    queryFn: () => ChatsService.getChatById(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutos
    gcTime: 10 * 60 * 1000, // 10 minutos
    ...options
  });
};

/**
 * Hook para buscar mensagens de um chat
 */
export const useMessages = (
  chatId: string,
  params?: MessagesListParams,
  options?: UseQueryOptions<PaginationResponse<Message>, BaseApiError>
) => {
  return useQuery({
    queryKey: chatsKeys.messagesList(chatId, params),
    queryFn: () => ChatsService.getMessages(chatId, params),
    enabled: !!chatId,
    staleTime: 1 * 60 * 1000, // 1 minuto
    gcTime: 5 * 60 * 1000, // 5 minutos
    ...options
  });
};

/**
 * Hook para buscar mensagens com scroll infinito
 */
export const useInfiniteMessages = (
  chatId: string,
  params?: Omit<MessagesListParams, "page">,
  pageSize: number = 50,
  options?: UseInfiniteQueryOptions<PaginationResponse<Message>, BaseApiError>
) => {
  return useInfiniteQuery({
    queryKey: chatsKeys.messagesList(chatId, {...params, pageSize}),
    queryFn: ({pageParam = 1}) =>
      ChatsService.getMessages(chatId, {
        ...params,
        page: pageParam as number,
        pageSize
      }),
    initialPageParam: 1,
    getNextPageParam: (lastPage) => {
      if (lastPage.pagination.hasNextPage) {
        return lastPage.pagination.currentPage + 1;
      }
      return undefined;
    },
    enabled: !!chatId,
    staleTime: 30 * 1000, // 30 segundos
    gcTime: 2 * 60 * 1000, // 2 minutos
    ...options
  });
};

/**
 * Hook para buscar membros do chat
 */
export const useChatMembers = (
  chatId: string,
  params?: {page?: number; pageSize?: number},
  options?: UseQueryOptions<PaginationResponse<ChatMember>, BaseApiError>
) => {
  return useQuery({
    queryKey: chatsKeys.members(chatId),
    queryFn: () => ChatsService.getChatMembers(chatId, params),
    enabled: !!chatId,
    staleTime: 5 * 60 * 1000, // 5 minutos
    gcTime: 10 * 60 * 1000, // 10 minutos
    ...options
  });
};

/**
 * Hook para buscar estatísticas de chat
 */
export const useChatStats = (
  options?: UseQueryOptions<ChatStats, BaseApiError>
) => {
  return useQuery({
    queryKey: chatsKeys.stats(),
    queryFn: () => ChatsService.getChatStats(),
    staleTime: 5 * 60 * 1000, // 5 minutos
    gcTime: 10 * 60 * 1000, // 10 minutos
    ...options
  });
};

/**
 * Hook para buscar chats não lidos
 */
export const useUnreadChats = (
  options?: UseQueryOptions<Chat[], BaseApiError>
) => {
  return useQuery({
    queryKey: chatsKeys.unread(),
    queryFn: () => ChatsService.getUnreadChats(),
    staleTime: 1 * 60 * 1000, // 1 minuto
    gcTime: 5 * 60 * 1000, // 5 minutos
    // refetchInterval: 2 * 60 * 1000, // Temporariamente removido até o endpoint estar funcionando
    retry: (failureCount, error) => {
      // Não tentar novamente se for 404 (endpoint não existe)
      if (error?.status === 404) {
        return false;
      }
      // Tentar até 2 vezes para outros erros
      return failureCount < 2;
    },
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    ...options,
    enabled: false // Temporariamente desabilitado até o endpoint estar funcionando - sempre sobrescrever
  });
};

/**
 * Hook para criar chat
 */
export const useCreateChat = (
  options?: UseMutationOptions<Chat, BaseApiError, CreateChatRequest>
) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (chat: CreateChatRequest) => ChatsService.createChat(chat),
    onSuccess: () => {
      // Invalidar listas de chats
      queryClient.invalidateQueries({queryKey: chatsKeys.lists()});
      // Invalidar estatísticas
      queryClient.invalidateQueries({queryKey: chatsKeys.stats()});
    },
    ...options
  });
};

/**
 * Hook para atualizar chat
 */
export const useUpdateChat = (
  options?: UseMutationOptions<
    Chat,
    BaseApiError,
    {id: string; chat: UpdateChatRequest}
  >
) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({id, chat}) => ChatsService.updateChat(id, chat),
    onSuccess: (_, {id}) => {
      // Invalidar cache do chat específico
      queryClient.invalidateQueries({queryKey: chatsKeys.detail(id)});
      // Invalidar listas de chats
      queryClient.invalidateQueries({queryKey: chatsKeys.lists()});
    },
    ...options
  });
};

/**
 * Hook para deletar chat
 */
export const useDeleteChat = (
  options?: UseMutationOptions<void, BaseApiError, string>
) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => ChatsService.deleteChat(id),
    onSuccess: (_, id) => {
      // Remover do cache
      queryClient.removeQueries({queryKey: chatsKeys.detail(id)});
      queryClient.removeQueries({queryKey: chatsKeys.messages(id)});
      queryClient.removeQueries({queryKey: chatsKeys.members(id)});
      // Invalidar listas de chats
      queryClient.invalidateQueries({queryKey: chatsKeys.lists()});
      // Invalidar estatísticas
      queryClient.invalidateQueries({queryKey: chatsKeys.stats()});
    },
    ...options
  });
};

/**
 * Hook para enviar mensagem
 */
export const useSendMessage = (
  options?: UseMutationOptions<
    Message,
    BaseApiError,
    {chatId: string; message: SendMessageRequest}
  >
) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({chatId, message}) =>
      ChatsService.sendMessage(chatId, message),
    onSuccess: (_, {chatId}) => {
      // Invalidar mensagens do chat
      queryClient.invalidateQueries({queryKey: chatsKeys.messages(chatId)});
      // Invalidar chat específico (para atualizar lastMessage)
      queryClient.invalidateQueries({queryKey: chatsKeys.detail(chatId)});
      // Invalidar listas de chats
      queryClient.invalidateQueries({queryKey: chatsKeys.lists()});
    },
    ...options
  });
};

/**
 * Hook para criar chat com mensagem inicial
 * Combina criação de chat e envio de mensagem em uma operação
 */
export const useCreateChatWithMessage = (
  options?: UseMutationOptions<
    {chat: Chat; message: Message},
    BaseApiError,
    {participantUserId: number; message: string}
  >
) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({participantUserId, message}) => {
      // Validate input parameters
      if (!participantUserId || participantUserId === 0) {
        throw new Error(
          "Invalid participant user ID: cannot create chat without a valid user ID"
        );
      }

      if (!message || message.trim().length === 0) {
        throw new Error(
          "Invalid message: cannot create chat without a message"
        );
      }

      // Debug logging
      if (__DEV__) {
        console.log("Creating chat with message:", {
          participantUserId,
          messageLength: message.length,
          messagePreview: message.substring(0, 50)
        });
      }

      try {
        // Criar o chat primeiro
        const chat = await ChatsService.createChat({
          type: "DIRECT" as any,
          name: "",
          isPrivate: true,
          memberIds: [participantUserId.toString()]
        });

        if (!chat || !chat.id) {
          throw new Error(
            "Failed to create chat: invalid chat response from server"
          );
        }

        // Enviar a mensagem inicial
        const sentMessage = await ChatsService.sendMessage(chat.id, {
          type: "TEXT" as any,
          content: message.trim()
        });

        if (!sentMessage) {
          throw new Error(
            "Failed to send initial message: invalid message response from server"
          );
        }

        return {chat, message: sentMessage};
      } catch (error) {
        console.error("Chat creation failed:", error);

        // Re-throw with more context
        if (error instanceof Error) {
          throw new Error(`Chat creation failed: ${error.message}`);
        } else {
          throw new Error("Chat creation failed: unknown error occurred");
        }
      }
    },
    onSuccess: () => {
      // Invalidar listas de chats
      queryClient.invalidateQueries({queryKey: chatsKeys.lists()});
      // Invalidar estatísticas
      queryClient.invalidateQueries({queryKey: chatsKeys.stats()});
    },
    ...options
  });
};

/**
 * Hook para atualizar mensagem
 */
export const useUpdateMessage = (
  options?: UseMutationOptions<
    Message,
    BaseApiError,
    {
      chatId: string;
      messageId: string;
      message: UpdateMessageRequest;
    }
  >
) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({chatId, messageId, message}) =>
      ChatsService.updateMessage(chatId, messageId, message),
    onSuccess: (_, {chatId}) => {
      // Invalidar mensagens do chat
      queryClient.invalidateQueries({queryKey: chatsKeys.messages(chatId)});
    },
    ...options
  });
};

/**
 * Hook para deletar mensagem
 */
export const useDeleteMessage = (
  options?: UseMutationOptions<
    void,
    BaseApiError,
    {chatId: string; messageId: string}
  >
) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({chatId, messageId}) =>
      ChatsService.deleteMessage(chatId, messageId),
    onSuccess: (_, {chatId}) => {
      // Invalidar mensagens do chat
      queryClient.invalidateQueries({queryKey: chatsKeys.messages(chatId)});
    },
    ...options
  });
};

/**
 * Hook para marcar mensagens como lidas
 */
export const useMarkMessagesAsRead = (
  options?: UseMutationOptions<
    {success: boolean},
    BaseApiError,
    {
      chatId: string;
      messageIds: string[];
    }
  >
) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({chatId, messageIds}) =>
      ChatsService.markMessagesAsRead(chatId, messageIds),
    onSuccess: (_, {chatId}) => {
      // Invalidar mensagens do chat
      queryClient.invalidateQueries({queryKey: chatsKeys.messages(chatId)});
      // Invalidar chat específico (para atualizar unreadCount)
      queryClient.invalidateQueries({queryKey: chatsKeys.detail(chatId)});
      // Invalidar listas de chats
      queryClient.invalidateQueries({queryKey: chatsKeys.lists()});
      // Invalidar chats não lidos
      queryClient.invalidateQueries({queryKey: chatsKeys.unread()});
    },
    ...options
  });
};

/**
 * Hook para adicionar membros ao chat
 */
export const useAddChatMembers = (
  options?: UseMutationOptions<
    {success: boolean},
    BaseApiError,
    {
      chatId: string;
      request: AddChatMemberRequest;
    }
  >
) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({chatId, request}) =>
      ChatsService.addChatMembers(chatId, request),
    onSuccess: (_, {chatId}) => {
      // Invalidar membros do chat
      queryClient.invalidateQueries({queryKey: chatsKeys.members(chatId)});
      // Invalidar chat específico
      queryClient.invalidateQueries({queryKey: chatsKeys.detail(chatId)});
    },
    ...options
  });
};

/**
 * Hook para upload de arquivo
 */
export const useUploadFile = (
  options?: UseMutationOptions<
    FileUploadResponse,
    BaseApiError,
    FileUploadRequest
  >
) => {
  return useMutation({
    mutationFn: (request: FileUploadRequest) =>
      ChatsService.uploadFile(request),
    ...options
  });
};

/**
 * Hook para iniciar chamada
 */
export const useStartCall = (
  options?: UseMutationOptions<CallResponse, BaseApiError, CallRequest>
) => {
  return useMutation({
    mutationFn: (request: CallRequest) => ChatsService.startCall(request),
    ...options
  });
};

/**
 * Hook para sair do chat
 */
export const useLeaveChat = (
  options?: UseMutationOptions<{success: boolean}, BaseApiError, string>
) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (chatId: string) => ChatsService.leaveChat(chatId),
    onSuccess: (_, chatId) => {
      // Remover do cache
      queryClient.removeQueries({queryKey: chatsKeys.detail(chatId)});
      queryClient.removeQueries({queryKey: chatsKeys.messages(chatId)});
      queryClient.removeQueries({queryKey: chatsKeys.members(chatId)});
      // Invalidar listas de chats
      queryClient.invalidateQueries({queryKey: chatsKeys.lists()});
    },
    ...options
  });
};
