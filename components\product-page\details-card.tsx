import React, {useState} from "react";
import {View, Text, TouchableOpacity} from "react-native";
import {useTranslation} from "react-i18next";
import styles from "../../styles/components/product-page/details-card.style";

interface ProductDetail {
  label: string;
  value: string;
}

interface Product {
  id?: number;
  name?: string | null;
  description?: string | null;
  value?: number;
  category?: number;
  periodicity?: number;
  isActive?: boolean;
  termId?: number | null;
  createdAt?: string;
  updatedAt?: string | null;
  createdBy?: string | null;
  updatedBy?: string | null;
  franchiseId?: number | null;
  term?: any;
}

interface DetailsCardProps {
  product?: Product;
  onOpenTermsModal?: () => void;
  hideWhereToUse?: boolean; // Nova prop para ocultar a aba "Onde usar" em produtos
}

const DetailsCard: React.FC<DetailsCardProps> = ({
  product,
  onOpenTermsModal,
  hideWhereToUse = false
}) => {
  const {t} = useTranslation();
  const [activeTab, setActiveTab] = useState<"details" | "whereToUse">(
    "details"
  );

  // Função para formatar data
  const formatDate = (dateString?: string) => {
    if (!dateString) return "Data não informada";
    try {
      return new Date(dateString).toLocaleDateString("pt-BR");
    } catch {
      return "Data não informada";
    }
  };

  // Função para formatar categoria
  const formatCategory = (category?: number) => {
    if (category === undefined || category === null)
      return "Categoria não informada";
    const categoryMap: {[key: number]: string} = {
      0: "E-books e publicações digitais",
      1: "Cursos e conteúdo educacional",
      2: "Reuniões e consultorias",
      3: "Reuniões em grupo e workshops",
      4: "Mentoria e coaching",
      5: "Serviços gerais"
    };
    return categoryMap[category] || "Categoria não informada";
  };

  // Função para formatar periodicidade
  const formatPeriodicity = (periodicity?: number) => {
    if (periodicity === undefined || periodicity === null)
      return "Compra única";
    const periodicityMap: {[key: number]: string} = {
      0: "Compra única",
      1: "Assinatura mensal",
      2: "Assinatura anual"
    };
    return periodicityMap[periodicity] || "Compra única";
  };

  // Função para formatar preço
  const formatPrice = (value?: number) => {
    if (!value) return "Gratuito";
    return `R$ ${(value / 100).toFixed(2).replace(".", ",")}`;
  };

  const productDetails: ProductDetail[] = [
    {
      label: "Autor",
      value: product?.createdBy || "Autor não informado"
    },
    {
      label: "Categoria",
      value: formatCategory(product?.category)
    },
    {
      label: "Formato do arquivo",
      value: "PDF" // Valor padrão já que a API não tem esse campo
    },
    {
      label: "Tipo de acesso",
      value: formatPeriodicity(product?.periodicity)
    },
    {
      label: "Data de publicação",
      value: formatDate(product?.createdAt)
    },
    {
      label: "Preço",
      value: formatPrice(product?.value)
    },
    {
      label: "Informações adicionais",
      value: product?.description || "Sem informações adicionais"
    }
  ];

  const whereToUseDetails: ProductDetail[] = [
    {
      label: "Estabelecimentos parceiros",
      value: "Mais de 500 locais"
    },
    {
      label: "Restaurantes",
      value: "Descontos de 10% a 30%"
    },
    {
      label: "Hotéis e pousadas",
      value: "Descontos especiais"
    },
    {
      label: "Lojas e serviços",
      value: "Benefícios exclusivos"
    },
    {
      label: "Eventos",
      value: "Acesso prioritário"
    },
    {
      label: "Validade",
      value: "12 meses a partir da ativação"
    },
    {
      label: "Região de uso",
      value: "Todo o território nacional"
    }
  ];

  const currentDetails =
    hideWhereToUse || activeTab === "details"
      ? productDetails
      : whereToUseDetails;
  const currentTitle =
    hideWhereToUse || activeTab === "details"
      ? t("products.detailsTitle")
      : "Onde usar";

  return (
    <View style={styles.container}>
      {/* Tab Navigation - só mostra se não for para ocultar "Onde usar" ou se não estiver ocultando */}
      {!hideWhereToUse && (
        <View style={styles.tabContainer}>
          <TouchableOpacity
            style={[styles.tab, activeTab === "details" && styles.activeTab]}
            onPress={() => setActiveTab("details")}
          >
            <Text
              style={[
                styles.tabText,
                activeTab === "details" && styles.activeTabText
              ]}
            >
              Detalhes
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.tab, activeTab === "whereToUse" && styles.activeTab]}
            onPress={() => setActiveTab("whereToUse")}
          >
            <Text
              style={[
                styles.tabText,
                activeTab === "whereToUse" && styles.activeTabText
              ]}
            >
              Onde usar
            </Text>
          </TouchableOpacity>
        </View>
      )}

      {/* Content */}
      <Text style={styles.title}>{currentTitle}</Text>
      <View style={styles.table}>
        {currentDetails.map((detail, index) => (
          <View
            key={index + 1}
            style={[styles.row, index % 2 !== 1 && styles.alternateRow]}
          >
            <Text style={styles.label}>{detail.label}</Text>
            <Text style={styles.value}>{detail.value}</Text>
          </View>
        ))}
        {onOpenTermsModal && (hideWhereToUse || activeTab === "details") && (
          <View style={styles.row}>
            <Text style={styles.label}>Termos</Text>
            <TouchableOpacity onPress={onOpenTermsModal}>
              <Text style={styles.termsLink}>Ver termos</Text>
            </TouchableOpacity>
          </View>
        )}
      </View>
    </View>
  );
};

export default DetailsCard;
