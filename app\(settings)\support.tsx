import React, {useState} from "react";
import {
  Text,
  View,
  ScrollView,
  TouchableOpacity,
  Linking,
  Alert,
  TextInput,
  FlatList
} from "react-native";
import ScreenWithHeader from "../../components/screen-with-header";
import {useTranslation} from "react-i18next";

import FullSizeButton from "../../components/full-size-button";
import SearchIcon from "../../components/icons/search-icon";
import UserIcon from "../../components/icons/user-icon";
import MailIcon from "../../components/icons/mail-icon";
import FileQuestionIcon from "../../components/icons/file-question-icon";
import FileAttachmentIcon from "../../components/icons/file-attachment-icon";
import ChevronDownIcon from "../../components/icons/chevron-down-icon";
import BuildingIcon from "../../components/icons/building-icon";
import PhoneIcon from "../../components/icons/phone-icon";
import ArrowNarrowUpRightIcon from "../../components/icons/arrow-narrow-up-right-icon";
import styles from "@/styles/settings/support.style";
import {
  showErrorToast,
  showSuccessToast,
  showWarningToast
} from "@/utils/error-handler";

interface SupportTicket {
  fullName: string;
  email: string;
  subject: string;
  message: string;
  category: string;
}

const Support: React.FC = () => {
  const {t} = useTranslation();
  const [searchTerm, setSearchTerm] = useState("");
  const [activeTab, setActiveTab] = useState(0); // 0 = Tirar dúvidas, 1 = Entre em contato
  const [ticket, setTicket] = useState<SupportTicket>({
    fullName: "",
    email: "",
    subject: "",
    message: "",
    category: "general"
  });

  const faqOptions = [
    {
      id: "membership",
      title: "Como faço para me tornar membro do Club M?",
      description:
        'Você pode se inscrever diretamente no aplicativo, acessando a aba "Torne-se Membro".',
      action: () => console.log("Open membership FAQ")
    },
    {
      id: "wallet",
      title: "Como acesso minha carteira digital?",
      description:
        'Sua carteira está disponível no página inicial do aplicativo, na opção "Carteira Digital".',
      action: () => console.log("Open wallet FAQ")
    }
  ];

  const contactInfo = [
    {
      id: "sp",
      name: "Club M",
      location: "Unidade São Paulo - SP",
      email: "<EMAIL>",
      phone: "(11) 98765-4321"
    },
    {
      id: "rj",
      name: "Club M",
      location: "Unidade Rio de Janeiro - RJ",
      email: "<EMAIL>",
      phone: "(21) 91234-5678"
    },
    {
      id: "bh",
      name: "Club M",
      location: "Unidade Belo Horizonte - BH",
      email: "<EMAIL>",
      phone: "(31) 98765-5432"
    }
  ];

  const categories = [
    {id: "general", name: t("support.general", "Geral")},
    {id: "account", name: t("support.account", "Conta")},
    {id: "payment", name: t("support.payment", "Pagamento")},
    {id: "technical", name: t("support.technical", "Técnico")},
    {id: "events", name: t("support.events", "Eventos")},
    {id: "networking", name: t("support.networking", "Networking")}
  ];

  const handleInputChange = (field: keyof SupportTicket) => (value: string) => {
    setTicket((prev) => ({
      ...prev,
      [field]: value
    }));
  };

  const handleAttachFile = () => {
    Alert.alert(
      t("support.attachFile", "Anexar Arquivo"),
      t("support.attachFileDesc", "Selecione um arquivo para anexar"),
      [
        {
          text: t("support.camera", "Câmera"),
          onPress: () => console.log("Open camera")
        },
        {
          text: t("support.gallery", "Galeria"),
          onPress: () => console.log("Open gallery")
        },
        {
          text: t("support.cancel", "Cancelar"),
          style: "cancel"
        }
      ]
    );
  };

  const selectCategory = (categoryId: string) => {
    setTicket((prev) => ({...prev, category: categoryId}));
  };

  const handleCategorySelect = () => {
    Alert.alert(
      t("support.selectCategory", "Selecionar Categoria"),
      t(
        "support.chooseCategoryDesc",
        "Escolha a categoria que melhor descreve seu problema"
      ),
      [
        ...categories.map((category) => ({
          text: category.name,
          onPress: () => selectCategory(category.id)
        })),
        {
          text: t("support.cancel", "Cancelar"),
          style: "cancel" as const
        }
      ]
    );
  };

  const handleSubmitTicket = () => {
    if (!ticket.fullName.trim()) {
      showWarningToast(
        t("support.error", "Erro"),
        t("support.nameRequired", "Por favor, informe seu nome completo")
      );
      return;
    }

    if (!ticket.email.trim()) {
      showWarningToast(
        t("support.error", "Erro"),
        t("support.emailRequired", "Por favor, informe seu e-mail")
      );
      return;
    }

    if (!ticket.message.trim()) {
      showWarningToast(
        t("support.error", "Erro"),
        t("support.messageRequired", "Por favor, escreva sua mensagem")
      );
      return;
    }

    showSuccessToast(
      t("support.success", "Sucesso"),
      t(
        "support.ticketSubmitted",
        "Sua mensagem foi enviada para nossa equipe de suporte! Entraremos em contato em breve."
      )
    );
    setTicket({
      fullName: "",
      email: "",
      subject: "",
      message: "",
      category: "general"
    });
  };

  const getCategoryName = (categoryId: string) => {
    const category = categories.find((cat) => cat.id === categoryId);
    return category ? category.name : t("support.general", "Geral");
  };

  const renderFaqCard = ({item}: {item: any}) => (
    <TouchableOpacity style={styles.faqCard} onPress={item.action}>
      <Text style={styles.faqTitle}>{item.title}</Text>
      <Text style={styles.faqDescription}>{item.description}</Text>
    </TouchableOpacity>
  );

  const renderTabContent = () => {
    if (activeTab === 0) {
      // Aba "Tirar dúvidas"
      return (
        <ScrollView style={styles.tabContent}>
          {/* Formulário de contato */}
          <View style={styles.sectionContainer}>
            <Text style={styles.sectionTitle}>
              {t("support.contactForm", "Tire suas dúvidas")}
            </Text>
            <Text style={styles.sectionDescription}>
              {t(
                "support.contactFormDesc",
                "Envie uma mensagem para nossa equipe de suporte e em breve entraremos em contato com você!"
              )}
            </Text>

            <View style={styles.formContainer}>
              <View style={styles.inputFieldContainer}>
                <Text style={styles.inputLabel}>Nome completo</Text>
                <View style={styles.inputWithIcon}>
                  <UserIcon width={20} height={20} />
                  <TextInput
                    style={styles.textInput}
                    value={ticket.fullName}
                    onChangeText={handleInputChange("fullName")}
                    placeholder="Insira seu nome completo"
                    placeholderTextColor="rgba(223, 233, 240, 0.7)"
                  />
                </View>
              </View>

              <View style={styles.inputFieldContainer}>
                <Text style={styles.inputLabel}>E-mail</Text>
                <View style={styles.inputWithIcon}>
                  <MailIcon width={20} height={20} />
                  <TextInput
                    style={styles.textInput}
                    value={ticket.email}
                    onChangeText={handleInputChange("email")}
                    placeholder="Insira seu e-mail"
                    placeholderTextColor="rgba(223, 233, 240, 0.7)"
                    keyboardType="email-address"
                    autoCapitalize="none"
                  />
                </View>
              </View>

              <View style={styles.inputFieldContainer}>
                <Text style={styles.inputLabel}>Assunto</Text>
                <TouchableOpacity
                  style={styles.inputWithIcon}
                  onPress={handleCategorySelect}
                >
                  <FileQuestionIcon width={20} height={20} />
                  <Text style={styles.selectText}>
                    {getCategoryName(ticket.category) === "Geral"
                      ? "Selecione um assunto"
                      : getCategoryName(ticket.category)}
                  </Text>
                  <ChevronDownIcon width={16} height={16} />
                </TouchableOpacity>
              </View>

              <View style={styles.inputFieldContainer}>
                <Text style={styles.inputLabel}>Mensagem</Text>
                <View style={styles.messageInputContainer}>
                  <TextInput
                    style={styles.messageTextInput}
                    value={ticket.message}
                    onChangeText={handleInputChange("message")}
                    placeholder="Insira sua mensagem..."
                    placeholderTextColor="rgba(223, 233, 240, 0.7)"
                    multiline
                    numberOfLines={6}
                    textAlignVertical="top"
                  />
                </View>
                <Text style={styles.characterCount}>
                  {ticket.message.length}/250 caracteres
                </Text>
              </View>

              <View style={styles.inputFieldContainer}>
                <Text style={styles.inputLabel}>Anexar arquivo (opcional)</Text>
                <TouchableOpacity
                  style={styles.attachFileButton}
                  onPress={handleAttachFile}
                >
                  <FileAttachmentIcon width={20} height={20} />
                  <Text style={styles.attachFileText}>Anexar arquivo</Text>
                </TouchableOpacity>
              </View>

              <FullSizeButton
                text={t("support.submit", "Enviar feedback")}
                onPress={handleSubmitTicket}
              />
            </View>
          </View>
        </ScrollView>
      );
    } else {
      // Aba "Entre em contato"
      return (
        <ScrollView style={styles.tabContent}>
          <Text style={styles.sectionTitle}>Entre em contato</Text>
          <Text style={styles.sectionDescription}>
            Confira abaixo as informações de contato das nossas unidades ao
            redor do Brasil.
          </Text>

          <View style={styles.contactList}>
            {contactInfo.map((contact) => (
              <View key={contact.id} style={styles.contactItem}>
                <View style={styles.contactHeader}>
                  <BuildingIcon
                    width={44}
                    height={44}
                    style={styles.contactIcon}
                  />
                  <View style={styles.contactInfo}>
                    <Text style={styles.contactName}>{contact.name}</Text>
                    <Text style={styles.contactLocation}>
                      {contact.location}
                    </Text>
                  </View>
                </View>

                <TouchableOpacity
                  style={styles.contactAction}
                  onPress={() => Linking.openURL(`mailto:${contact.email}`)}
                >
                  <MailIcon
                    width={20}
                    height={20}
                    style={styles.contactActionIcon}
                  />
                  <Text style={styles.contactActionText}>{contact.email}</Text>
                  <ArrowNarrowUpRightIcon
                    width={20}
                    height={20}
                    style={styles.contactActionArrow}
                  />
                </TouchableOpacity>

                <TouchableOpacity
                  style={styles.contactAction}
                  onPress={() =>
                    Linking.openURL(
                      `tel:${contact.phone.replace(/[^\d]/g, "")}`
                    )
                  }
                >
                  <PhoneIcon
                    width={20}
                    height={20}
                    style={styles.contactActionIcon}
                  />
                  <Text style={styles.contactActionText}>{contact.phone}</Text>
                  <ArrowNarrowUpRightIcon
                    width={20}
                    height={20}
                    style={styles.contactActionArrow}
                  />
                </TouchableOpacity>
              </View>
            ))}
          </View>
        </ScrollView>
      );
    }
  };

  return (
    <ScreenWithHeader
      screenTitle={t("support.title", "Suporte e ajuda")}
      backButton
    >
      <View style={styles.container}>
        {/* Search Bar */}
        <View style={styles.searchContainer}>
          <View style={styles.searchInputContainer}>
            <SearchIcon width={20} height={20} />
            <TextInput
              style={styles.searchInput}
              value={searchTerm}
              onChangeText={setSearchTerm}
              placeholder="Busque perguntas ou palavras-chave"
              placeholderTextColor="rgba(223, 233, 240, 0.7)"
              autoCapitalize="none"
              autoCorrect={false}
            />
          </View>
        </View>

        {/* FAQ Section */}
        <Text style={styles.faqSectionTitle}>Dúvidas frequentes</Text>
        <View style={styles.faqContainer}>
          <FlatList
            data={faqOptions}
            renderItem={renderFaqCard}
            keyExtractor={(item) => item.id}
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.faqContent}
            snapToInterval={276}
            decelerationRate="fast"
          />
        </View>

        {/* Tab Navigation */}
        <View style={styles.tabContainer}>
          <TouchableOpacity
            style={[styles.tab, activeTab === 0 && styles.activeTab]}
            onPress={() => setActiveTab(0)}
          >
            <Text
              style={[styles.tabText, activeTab === 0 && styles.activeTabText]}
            >
              Tirar dúvidas
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.tab, activeTab === 1 && styles.activeTab]}
            onPress={() => setActiveTab(1)}
          >
            <Text
              style={[styles.tabText, activeTab === 1 && styles.activeTabText]}
            >
              Entre em contato
            </Text>
          </TouchableOpacity>
        </View>

        {/* Tab Content */}
        {renderTabContent()}
      </View>
    </ScreenWithHeader>
  );
};

export default Support;
