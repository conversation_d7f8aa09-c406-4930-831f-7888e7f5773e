{"root": "C:\\Users\\<USER>\\OneDrive\\Documentos\\Projeto\\club-m-app", "reactNativePath": "C:\\Users\\<USER>\\OneDrive\\Documentos\\Projeto\\club-m-app\\node_modules\\react-native", "dependencies": {"@react-native-async-storage/async-storage": {"root": "C:\\Users\\<USER>\\OneDrive\\Documentos\\Projeto\\club-m-app\\node_modules\\@react-native-async-storage\\async-storage", "name": "@react-native-async-storage/async-storage", "platforms": {"android": {"sourceDir": "C:\\Users\\<USER>\\OneDrive\\Documentos\\Projeto\\club-m-app\\node_modules\\@react-native-async-storage\\async-storage\\android", "packageImportPath": "import com.reactnativecommunity.asyncstorage.AsyncStoragePackage;", "packageInstance": "new AsyncStoragePackage()", "buildTypes": [], "libraryName": "rnasyncstorage", "componentDescriptors": [], "cmakeListsPath": "C:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "expo": {"root": "C:\\Users\\<USER>\\OneDrive\\Documentos\\Projeto\\club-m-app\\node_modules\\expo", "name": "expo", "platforms": {"android": {"sourceDir": "C:\\Users\\<USER>\\OneDrive\\Documentos\\Projeto\\club-m-app\\node_modules\\expo\\android", "packageImportPath": "import expo.modules.ExpoModulesPackage;", "packageInstance": "new ExpoModulesPackage()", "buildTypes": [], "componentDescriptors": [], "cmakeListsPath": "C:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/expo/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-blob-util": {"root": "C:\\Users\\<USER>\\OneDrive\\Documentos\\Projeto\\club-m-app\\node_modules\\react-native-blob-util", "name": "react-native-blob-util", "platforms": {"android": {"sourceDir": "C:\\Users\\<USER>\\OneDrive\\Documentos\\Projeto\\club-m-app\\node_modules\\react-native-blob-util\\android", "packageImportPath": "import com.ReactNativeBlobUtil.ReactNativeBlobUtilPackage;", "packageInstance": "new ReactNativeBlobUtilPackage()", "buildTypes": [], "libraryName": "ReactNativeBlobUtilSpec", "componentDescriptors": [], "cmakeListsPath": "C:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-blob-util/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-pager-view": {"root": "C:\\Users\\<USER>\\OneDrive\\Documentos\\Projeto\\club-m-app\\node_modules\\react-native-pager-view", "name": "react-native-pager-view", "platforms": {"android": {"sourceDir": "C:\\Users\\<USER>\\OneDrive\\Documentos\\Projeto\\club-m-app\\node_modules\\react-native-pager-view\\android", "packageImportPath": "import com.reactnativepagerview.PagerViewPackage;", "packageInstance": "new PagerViewPackage()", "buildTypes": [], "libraryName": "pagerview", "componentDescriptors": ["RNCViewPagerComponentDescriptor"], "cmakeListsPath": "C:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-pdf": {"root": "C:\\Users\\<USER>\\OneDrive\\Documentos\\Projeto\\club-m-app\\node_modules\\react-native-pdf", "name": "react-native-pdf", "platforms": {"android": {"sourceDir": "C:\\Users\\<USER>\\OneDrive\\Documentos\\Projeto\\club-m-app\\node_modules\\react-native-pdf\\android", "packageImportPath": "import org.wonday.pdf.RNPDFPackage;", "packageInstance": "new RNPDFPackage()", "buildTypes": [], "libraryName": "rnpdf", "componentDescriptors": ["RNPDFPdfViewComponentDescriptor"], "cmakeListsPath": "C:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-pdf/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-reanimated": {"root": "C:\\Users\\<USER>\\OneDrive\\Documentos\\Projeto\\club-m-app\\node_modules\\react-native-reanimated", "name": "react-native-reanimated", "platforms": {"android": {"sourceDir": "C:\\Users\\<USER>\\OneDrive\\Documentos\\Projeto\\club-m-app\\node_modules\\react-native-reanimated\\android", "packageImportPath": "import com.swmansion.reanimated.ReanimatedPackage;", "packageInstance": "new ReanimatedPackage()", "buildTypes": [], "libraryName": "rnreanimated", "componentDescriptors": [], "cmakeListsPath": "C:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-safe-area-context": {"root": "C:\\Users\\<USER>\\OneDrive\\Documentos\\Projeto\\club-m-app\\node_modules\\react-native-safe-area-context", "name": "react-native-safe-area-context", "platforms": {"android": {"sourceDir": "C:\\Users\\<USER>\\OneDrive\\Documentos\\Projeto\\club-m-app\\node_modules\\react-native-safe-area-context\\android", "packageImportPath": "import com.th3rdwave.safeareacontext.SafeAreaContextPackage;", "packageInstance": "new SafeAreaContextPackage()", "buildTypes": [], "libraryName": "safeareacontext", "componentDescriptors": ["RNCSafeAreaProviderComponentDescriptor", "RNCSafeAreaViewComponentDescriptor"], "cmakeListsPath": "C:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-safe-area-context/android/src/main/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-screens": {"root": "C:\\Users\\<USER>\\OneDrive\\Documentos\\Projeto\\club-m-app\\node_modules\\react-native-screens", "name": "react-native-screens", "platforms": {"android": {"sourceDir": "C:\\Users\\<USER>\\OneDrive\\Documentos\\Projeto\\club-m-app\\node_modules\\react-native-screens\\android", "packageImportPath": "import com.swmansion.rnscreens.RNScreensPackage;", "packageInstance": "new RNScreensPackage()", "buildTypes": [], "libraryName": "rnscreens", "componentDescriptors": ["RNSFullWindowOverlayComponentDescriptor", "RNSScreenContainerComponentDescriptor", "RNSScreenNavigationContainerComponentDescriptor", "RNSScreenStackHeaderConfigComponentDescriptor", "RNSScreenStackHeaderSubviewComponentDescriptor", "RNSScreenStackComponentDescriptor", "RNSSearchBarComponentDescriptor", "RNSScreenComponentDescriptor", "RNSScreenFooterComponentDescriptor", "RNSScreenContentWrapperComponentDescriptor", "RNSModalScreenComponentDescriptor"], "cmakeListsPath": "C:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-screens/android/src/main/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-svg": {"root": "C:\\Users\\<USER>\\OneDrive\\Documentos\\Projeto\\club-m-app\\node_modules\\react-native-svg", "name": "react-native-svg", "platforms": {"android": {"sourceDir": "C:\\Users\\<USER>\\OneDrive\\Documentos\\Projeto\\club-m-app\\node_modules\\react-native-svg\\android", "packageImportPath": "import com.horcrux.svg.SvgPackage;", "packageInstance": "new SvgPackage()", "buildTypes": [], "libraryName": "rnsvg", "componentDescriptors": ["RNSVGCircleComponentDescriptor", "RNSVGClipPathComponentDescriptor", "RNSVGDefsComponentDescriptor", "RNSVGFeBlendComponentDescriptor", "RNSVGFeColorMatrixComponentDescriptor", "RNSVGFeCompositeComponentDescriptor", "RNSVGFeFloodComponentDescriptor", "RNSVGFeGaussianBlurComponentDescriptor", "RNSVGFeMergeComponentDescriptor", "RNSVGFeOffsetComponentDescriptor", "RNSVGFilterComponentDescriptor", "RNSVGEllipseComponentDescriptor", "RNSVGForeignObjectComponentDescriptor", "RNSVGGroupComponentDescriptor", "RNSVGImageComponentDescriptor", "RNSVGLinearGradientComponentDescriptor", "RNSVGLineComponentDescriptor", "RNSVGMarkerComponentDescriptor", "RNSVGMaskComponentDescriptor", "RNSVGPathComponentDescriptor", "RNSVGPatternComponentDescriptor", "RNSVGRadialGradientComponentDescriptor", "RNSVGRectComponentDescriptor", "RNSVGSvgViewAndroidComponentDescriptor", "RNSVGSymbolComponentDescriptor", "RNSVGTextComponentDescriptor", "RNSVGTextPathComponentDescriptor", "RNSVGTSpanComponentDescriptor", "RNSVGUseComponentDescriptor"], "cmakeListsPath": "C:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-svg/android/src/main/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-webview": {"root": "C:\\Users\\<USER>\\OneDrive\\Documentos\\Projeto\\club-m-app\\node_modules\\react-native-webview", "name": "react-native-webview", "platforms": {"android": {"sourceDir": "C:\\Users\\<USER>\\OneDrive\\Documentos\\Projeto\\club-m-app\\node_modules\\react-native-webview\\android", "packageImportPath": "import com.reactnativecommunity.webview.RNCWebViewPackage;", "packageInstance": "new RNCWebViewPackage()", "buildTypes": [], "libraryName": "RNCWebViewSpec", "componentDescriptors": ["RNCWebViewComponentDescriptor"], "cmakeListsPath": "C:/Users/<USER>/OneDrive/Documentos/Projeto/club-m-app/node_modules/react-native-webview/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}}, "project": {"android": {"packageName": "studio.takasaki.clubm", "sourceDir": "C:\\Users\\<USER>\\OneDrive\\Documentos\\Projeto\\club-m-app\\android"}}}