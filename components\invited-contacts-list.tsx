/**
 * Invited Contacts List Component
 * Displays a list of indications/invitations sent by the user
 */

import React, {useState, useCallback, useMemo} from "react";
import {
  View,
  Text,
  ScrollView,
  RefreshControl,
  ActivityIndicator,
  Alert
} from "react-native";
import {useTranslation} from "react-i18next";
import ScreenWithHeader from "./screen-with-header";
import FullSizeButton from "./full-size-button";
import InvisibleFullSizeButton from "./invisible-full-size-button";
import InviteFriendsModal from "./modals/invite-friends-modal";
import UsersPlusIcon from "./icons/users-plus-icon";
import SearchIcon from "./icons/search-icon";
import SendIcon from "./icons/send-icon";
import InputField from "./input-field";
import {useIndications} from "@/hooks/api/use-indications";
import {Indication, IndicationStatus} from "@/models/api/indications.models";
import {useGuestUser} from "@/contexts/guest-user-context";

import styles from "@/styles/components/invited-contacts-list.style";
import {showSuccessToast} from "@/utils/error-handler";

// Usar os tipos da API de indicações
type Referral = Indication;

interface InvitedContactsListProps {
  onClose: () => void;
}

const InvitedContactsList: React.FC<InvitedContactsListProps> = ({onClose}) => {
  const {t} = useTranslation();
  const {isGuest, isTokenReady} = useGuestUser();
  const [searchTerm, setSearchTerm] = useState("");
  const [showInviteModal, setShowInviteModal] = useState(false);
  const [refreshing, setRefreshing] = useState(false);

  // Buscar indicações da API - only enable when token is ready for guest users
  const {
    data: indicationsResponse,
    isLoading,
    error,
    refetch
  } = useIndications(
    {
      page: 1,
      pageSize: 50
    },
    {
      enabled: !isGuest || (isGuest && isTokenReady) // Only enable when token is ready for guest users
    } as any
  );

  // Debug da resposta da API
  React.useEffect(() => {
    if (indicationsResponse) {
      console.log(
        "🔍 [INVITED-CONTACTS] Resposta da API:",
        indicationsResponse
      );
      console.log("🔍 [INVITED-CONTACTS] Dados:", indicationsResponse.data);
      console.log(
        "🔍 [INVITED-CONTACTS] Tipo dos dados:",
        typeof indicationsResponse.data
      );
      console.log(
        "🔍 [INVITED-CONTACTS] É array?",
        Array.isArray(indicationsResponse.data)
      );
      if (
        Array.isArray(indicationsResponse.data) &&
        indicationsResponse.data.length > 0
      ) {
        console.log(
          "🔍 [INVITED-CONTACTS] Primeiro item original:",
          indicationsResponse.data[0]
        );
        console.log(
          "🔍 [INVITED-CONTACTS] name do primeiro item:",
          indicationsResponse.data[0]?.name
        );
        console.log(
          "🔍 [INVITED-CONTACTS] referredName do primeiro item:",
          indicationsResponse.data[0]?.referredName
        );
      }
    }
  }, [indicationsResponse]);

  const allReferrals = (indicationsResponse?.data || [])
    .filter((referral) => {
      if (!referral || typeof referral !== "object") {
        console.warn(
          "❌ [INVITED-CONTACTS] Referral inválido encontrado:",
          referral
        );
        return false;
      }
      return true;
    })
    .map((referral) => {
      // Mapear os campos da API para o formato esperado pelo componente
      return {
        ...referral,
        referredName: referral.name || referral.referredName,
        referredEmail:
          referral.email ||
          referral.referredEmail ||
          `${referral.document}@clubm.app`,
        referredPhoneNumber:
          referral.phoneNumber || referral.referredPhoneNumber,
        referredDocument: referral.document || referral.referredDocument,
        // Mapear status numérico para string se necessário
        status: (() => {
          if (typeof referral.status === "number") {
            return referral.status === 0 ? "pending" : "accepted";
          }
          return referral.status;
        })()
      };
    });

  // Debug dos dados mapeados
  React.useEffect(() => {
    if (allReferrals.length > 0) {
      console.log("🔍 [INVITED-CONTACTS] Dados mapeados:", allReferrals);
      console.log(
        "🔍 [INVITED-CONTACTS] Primeiro item mapeado:",
        allReferrals[0]
      );
    }
  }, [allReferrals]);

  // Filtrar referências baseado na busca
  const filteredReferrals = useMemo(() => {
    if (!searchTerm.trim()) return allReferrals;
    return allReferrals.filter(
      (referral) =>
        (referral.referredName || "")
          .toLowerCase()
          .includes(searchTerm.toLowerCase()) ||
        (referral.referredEmail || "")
          .toLowerCase()
          .includes(searchTerm.toLowerCase())
    );
  }, [allReferrals, searchTerm]);

  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    try {
      await refetch();
    } catch (error) {
      console.error("Erro ao atualizar indicações:", error);
    } finally {
      setRefreshing(false);
    }
  }, [refetch]);

  const getStatusText = useCallback(
    (status: IndicationStatus) => {
      switch (status) {
        case IndicationStatus.PENDING:
          return t("contactsList.statusPending", "Convite enviado");
        case IndicationStatus.ACCEPTED:
          return t("contactsList.statusAccepted", "Aceito");
        case IndicationStatus.REJECTED:
          return t("contactsList.statusRejected", "Rejeitado");
        case IndicationStatus.EXPIRED:
          return t("contactsList.statusExpired", "Expirado");
        case IndicationStatus.CANCELLED:
          return t("contactsList.statusCancelled", "Cancelado");
        default:
          return "";
      }
    },
    [t]
  );

  const getStatusColor = useCallback((status: IndicationStatus) => {
    switch (status) {
      case IndicationStatus.PENDING:
        return styles.statusInvited;
      case IndicationStatus.ACCEPTED:
        return styles.statusActive;
      case IndicationStatus.REJECTED:
      case IndicationStatus.EXPIRED:
      case IndicationStatus.CANCELLED:
        return styles.statusNotActive;
      default:
        return styles.statusInvited;
    }
  }, []);

  const formatDate = useCallback((dateString: string) => {
    if (!dateString) return "Data não informada";
    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) return "Data inválida";
      return date.toLocaleDateString("pt-BR");
    } catch {
      return "Data inválida";
    }
  }, []);

  const getInitials = useCallback((name: string | undefined | null): string => {
    if (!name || typeof name !== "string") {
      return "??";
    }

    try {
      const trimmedName = name.trim();
      if (!trimmedName) return "??";

      const nameParts = trimmedName
        .split(" ")
        .filter((part) => part.length > 0);
      if (nameParts.length === 0) return "??";

      if (nameParts.length === 1) {
        return nameParts[0].charAt(0).toUpperCase();
      }

      const firstInitial = nameParts[0].charAt(0).toUpperCase();
      const lastInitial = nameParts[nameParts.length - 1]
        .charAt(0)
        .toUpperCase();

      return `${firstInitial}${lastInitial}`;
    } catch (error) {
      console.warn("❌ [INVITED-CONTACTS] Erro ao gerar iniciais:", error);
      return "??";
    }
  }, []);

  const handleCancelReferral = useCallback(
    async (referral: Referral) => {
      Alert.alert(
        t("contactsList.cancelReferral.title", "Cancelar Convite"),
        t(
          "contactsList.cancelReferral.message",
          "Deseja cancelar o convite para {{name}}?",
          {
            name: referral.referredName || "este contato"
          }
        ),
        [
          {
            text: t("common.cancel", "Cancelar"),
            style: "cancel"
          },
          {
            text: t("common.confirm", "Confirmar"),
            style: "destructive",
            onPress: () => {
              // TODO: Implementar cancelamento real quando API estiver disponível
              showSuccessToast(
                t("contactsList.cancelSuccess.title", "Sucesso!"),
                t(
                  "contactsList.cancelSuccess.message",
                  "Convite cancelado com sucesso!"
                )
              );
            }
          }
        ]
      );
    },
    [t]
  );

  const handleResendInvite = useCallback(
    (referral: Referral) => {
      Alert.alert(
        t("contactsList.resendConfirm.title", "Reenviar Convite"),
        t(
          "contactsList.resendConfirm.message",
          "Deseja reenviar o convite para {{name}}?",
          {
            name: referral.referredName || "este contato"
          }
        ),
        [
          {
            text: t("common.cancel", "Cancelar"),
            style: "cancel"
          },
          {
            text: t("common.confirm", "Confirmar"),
            onPress: () => {
              // TODO: Implementar reenvio real quando API estiver disponível
              showSuccessToast(
                t("contactsList.resendSuccess.title", "Sucesso!"),
                t(
                  "contactsList.resendSuccess.message",
                  "Convite reenviado com sucesso!"
                )
              );
            }
          }
        ]
      );
    },
    [t]
  );

  const renderReferralItem = useCallback(
    (referral: Referral) => {
      // Validação adicional para garantir que o referral é válido
      if (!referral?.id) {
        console.warn(
          "❌ [INVITED-CONTACTS] Tentativa de renderizar referral inválido:",
          referral
        );
        return null;
      }

      return (
        <View key={referral.id} style={styles.contactItem}>
          {/* Plane icon circle as per Motiff */}
          <View style={styles.planeIconCircle}>
            <SendIcon width={20} height={20} />
          </View>
          <View style={styles.contactInfo}>
            <Text style={styles.contactName}>
              {referral.referredName || "Nome não informado"}
            </Text>
            <Text
              style={styles.contactEmail}
              numberOfLines={1}
              ellipsizeMode="middle"
            >
              {referral.referredEmail || "Email não informado"}
            </Text>
            <Text style={styles.inviteDate}>
              {t("contactsList.invitedOn", "Enviado em:")}{" "}
              {formatDate(referral.createdAt)}
            </Text>
            {referral.message && (
              <Text style={styles.contactMessage} numberOfLines={2}>
                {referral.message}
              </Text>
            )}
          </View>
          <View style={styles.contactActions}>
            <View style={styles.statusRow}>
              <Text style={styles.statusText}>
                {getStatusText(referral.status)}
              </Text>
              <View
                style={[
                  styles.statusDot,
                  referral.status === IndicationStatus.ACCEPTED
                    ? {backgroundColor: "#3CCB7F"}
                    : referral.status === IndicationStatus.PENDING
                    ? {backgroundColor: "#FDB022"}
                    : {backgroundColor: "#FDA29B"}
                ]}
              />
            </View>
          </View>
        </View>
      );
    },
    [
      getStatusColor,
      getStatusText,
      formatDate,
      getInitials,
      handleResendInvite,
      handleCancelReferral,
      t
    ]
  );

  if (isLoading) {
    return (
      <ScreenWithHeader
        screenTitle={t("contactsList.title", "Lista de convidados")}
        backButton
        onBackPress={onClose}
      >
        <View style={styles.container}>
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#007AFF" />
            <Text style={styles.loadingText}>
              {t("contactsList.loading", "Carregando convites...")}
            </Text>
          </View>
        </View>
      </ScreenWithHeader>
    );
  }

  if (error) {
    return (
      <ScreenWithHeader
        screenTitle={t("contactsList.title", "Lista de convidados")}
        backButton
        onBackPress={onClose}
      >
        <View style={styles.container}>
          <View style={styles.emptyContainer}>
            <Text style={styles.emptyText}>
              {t("contactsList.error", "Erro ao carregar convites")}
            </Text>
            <Text style={styles.emptySubtext}>
              {t("contactsList.errorSubtext", "Tente novamente mais tarde")}
            </Text>
          </View>
        </View>
      </ScreenWithHeader>
    );
  }

  return (
    <ScreenWithHeader
      screenTitle={t("contactsList.title", "Lista de convidados")}
      backButton
      onBackPress={onClose}
    >
      <View style={styles.container}>
        <View style={styles.panel}>
          <View style={styles.searchContainer}>
            <InputField
              placeholder={t(
                "contactsList.searchPlaceholder",
                "Buscar usuário"
              )}
              value={searchTerm}
              onChangeText={setSearchTerm}
              icon={() => <SearchIcon />}
            />
          </View>

          {filteredReferrals.length === 0 ? (
            <View style={styles.emptyContainer}>
              <Text style={styles.emptyText}>
                {searchTerm.trim()
                  ? t(
                      "contactsList.noResults",
                      "Nenhum convite encontrado para sua busca."
                    )
                  : t(
                      "contactsList.empty",
                      "Você ainda não enviou nenhum convite."
                    )}
              </Text>
              <Text style={styles.emptySubtext}>
                {!searchTerm.trim() &&
                  t(
                    "contactsList.emptySubtext",
                    "Comece convidando seus amigos!"
                  )}
              </Text>
            </View>
          ) : (
            <ScrollView
              style={styles.contactsList}
              showsVerticalScrollIndicator={false}
              refreshControl={
                <RefreshControl
                  refreshing={refreshing}
                  onRefresh={handleRefresh}
                  colors={["#007AFF"]}
                />
              }
            >
              {filteredReferrals.map((item, idx) => (
                <React.Fragment key={item.id}>
                  {renderReferralItem(item)}
                  {idx < filteredReferrals.length - 1 && (
                    <View
                      style={{
                        height: 1,
                        backgroundColor: "#282A2E",
                        opacity: 0.6,
                        marginLeft: 68,
                        marginVertical: 4
                      }}
                    />
                  )}
                </React.Fragment>
              ))}
            </ScrollView>
          )}
        </View>

        {/* Footer buttons like Motiff */}
        <View style={{marginTop: 16}}>
          <FullSizeButton
            text={t("referral.inviteFriends", "Convidar amigos")}
            onPress={() => setShowInviteModal(true)}
            icon={<UsersPlusIcon />}
          />
          <InvisibleFullSizeButton
            text={t("common.back", "Voltar")}
            onPress={onClose}
          />
        </View>
        {showInviteModal ? (
          <InviteFriendsModal onClose={() => setShowInviteModal(false)} />
        ) : null}
      </View>
    </ScreenWithHeader>
  );
};

export default InvitedContactsList;
